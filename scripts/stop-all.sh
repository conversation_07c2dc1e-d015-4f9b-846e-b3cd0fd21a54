#!/bin/bash

# Hive SaaS 系统停止脚本
# 用于停止所有服务

echo "🛑 停止 Hive SaaS 系统..."

# 从 PID 文件停止服务
stop_service() {
    local service=$1
    local pid_file="../storage/temp/$service.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo "🛑 停止 $service 服务 (PID: $pid)..."
            kill $pid 2>/dev/null || true
            sleep 2
            
            # 如果进程仍在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                echo "⚡ 强制停止 $service 服务..."
                kill -9 $pid 2>/dev/null || true
            fi
        else
            echo "ℹ️  $service 服务已停止"
        fi
        rm -f "$pid_file"
    else
        echo "ℹ️  未找到 $service 服务的 PID 文件"
    fi
}

# 停止各个服务
stop_service "backend"
stop_service "frontend" 
stop_service "whatsapp"

# 额外清理：通过端口杀死进程
echo "🧹 清理端口占用..."

# 清理后端端口 8081
if lsof -ti:8081 > /dev/null 2>&1; then
    echo "🔧 清理后端端口 8081..."
    lsof -ti:8081 | xargs kill -9 2>/dev/null || true
fi

# 清理前端端口 5173
if lsof -ti:5173 > /dev/null 2>&1; then
    echo "🎨 清理前端端口 5173..."
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
fi

# 清理 WhatsApp 端口 3000
if lsof -ti:3000 > /dev/null 2>&1; then
    echo "📱 清理 WhatsApp 端口 3000..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
fi

# 清理可能的 Node.js 进程
echo "🧹 清理 Node.js 进程..."
pkill -f "whatsapp-node-service" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true

echo ""
echo "✅ 所有服务已停止"
echo ""
echo "📝 日志文件保留在 storage/logs/ 目录中"
echo "🚀 重新启动系统: ./start-all.sh"
