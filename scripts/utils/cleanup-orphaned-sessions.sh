#!/bin/bash

echo "🧹 清理孤立的WhatsApp Session文件"
echo "================================"

# 获取当前活跃的session列表
echo "1. 获取活跃Session列表..."
ACTIVE_SESSIONS=$(curl -s http://localhost:3000/api/whatsapp/sessions | jq -r '.data[].session_id')

echo "活跃的Session:"
echo "$ACTIVE_SESSIONS"

echo ""
echo "2. 检查文件系统中的Session文件夹..."

cd whatsapp-node-service/sessions

# 列出所有session文件夹
for session_dir in session-*; do
    if [ -d "$session_dir" ]; then
        # 提取session ID (去掉session-前缀)
        session_id=${session_dir#session-}
        
        echo "检查文件夹: $session_dir (Session ID: $session_id)"
        
        # 检查这个session是否在活跃列表中
        if echo "$ACTIVE_SESSIONS" | grep -q "^$session_id$"; then
            echo "  ✅ 保留 - Session $session_id 仍然活跃"
        else
            echo "  🗑️  删除 - Session $session_id 已不活跃"
            
            # 显示文件夹大小
            folder_size=$(du -sh "$session_dir" | cut -f1)
            echo "  📁 文件夹大小: $folder_size"
            
            # 删除文件夹
            rm -rf "$session_dir"
            echo "  ✅ 已删除文件夹: $session_dir"
        fi
        echo ""
    fi
done

echo "3. 清理完成后的状态..."
echo "剩余的Session文件夹:"
ls -la session-* 2>/dev/null || echo "没有剩余的Session文件夹"

echo ""
echo "清理完成！"