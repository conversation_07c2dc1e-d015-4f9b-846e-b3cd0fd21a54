#!/bin/bash

# Hive SaaS 系统启动脚本
# 用于同时启动所有服务

echo "🚀 启动 Hive SaaS 系统..."

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if lsof -ti:$port > /dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用 ($service)"
        echo "正在清理端口..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# 清理端口
echo "🧹 清理端口..."
check_port 8081 "后端服务"
check_port 5173 "前端服务"
check_port 3000 "WhatsApp服务"

# 创建日志目录
mkdir -p ../storage/logs

# 启动后端服务
echo "🔧 启动后端服务 (端口: 8081)..."
cd ../backend
nohup go run *.go > ../storage/logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务 PID: $BACKEND_PID"

# 等待后端启动
sleep 3

# 启动前端服务
echo "🎨 启动前端服务 (端口: 5173)..."
cd ../frontend
nohup pnpm dev > ../storage/logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务 PID: $FRONTEND_PID"

# 启动 WhatsApp 服务
echo "📱 启动 WhatsApp 服务 (端口: 3000)..."
cd ../whatsapp-node-service
nohup npm start > ../storage/logs/whatsapp.log 2>&1 &
WHATSAPP_PID=$!
echo "WhatsApp 服务 PID: $WHATSAPP_PID"

# 保存 PID 到文件
cd ../scripts
echo $BACKEND_PID > ../storage/temp/backend.pid
echo $FRONTEND_PID > ../storage/temp/frontend.pid
echo $WHATSAPP_PID > ../storage/temp/whatsapp.pid

echo ""
echo "✅ 所有服务启动完成！"
echo ""
echo "📊 服务状态："
echo "  🔧 后端服务:    http://localhost:8081"
echo "  🎨 前端应用:    http://localhost:5173"
echo "  📱 WhatsApp:    http://localhost:3000"
echo ""
echo "📝 日志文件："
echo "  后端日志:      storage/logs/backend.log"
echo "  前端日志:      storage/logs/frontend.log"
echo "  WhatsApp日志:  storage/logs/whatsapp.log"
echo ""
echo "🛑 停止所有服务: ./stop-all.sh"
echo ""

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -s http://localhost:8081/health > /dev/null; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务启动失败"
fi

if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务启动失败"
fi

if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ WhatsApp 服务运行正常"
else
    echo "❌ WhatsApp 服务启动失败"
fi

echo ""
echo "🎉 系统启动完成！访问 http://localhost:5173 开始使用"
