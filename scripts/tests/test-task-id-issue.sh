#!/bin/bash

echo "🧪 测试任务ID问题"
echo "================================"

# 获取token
echo "1. 获取认证token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，请检查用户凭据"
  exit 1
fi

echo "✅ 登录成功，获取到token"

# 获取任务列表
echo ""
echo "2. 获取任务列表..."
TASKS_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN")

echo "任务列表响应:"
echo "$TASKS_RESPONSE" | jq .

# 获取第一个任务的task_id
TASK_ID=$(echo "$TASKS_RESPONSE" | jq -r '.data.tasks[0].task_id')
echo ""
echo "第一个任务的task_id: $TASK_ID"

# 测试获取任务详情
echo ""
echo "3. 测试获取任务详情..."
DETAIL_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks/$TASK_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "任务详情响应:"
echo "$DETAIL_RESPONSE" | jq .

# 测试删除任务（模拟）
echo ""
echo "4. 测试删除任务（模拟）..."
DELETE_RESPONSE=$(curl -s -X DELETE "http://localhost:8081/api/group-sending/tasks/$TASK_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "删除响应:"
echo "$DELETE_RESPONSE" | jq .

echo ""
echo "✅ 测试完成！"
echo ""
echo "📋 分析结果："
echo "- 后端返回的task_id: $TASK_ID"
echo "- 如果task_id包含连字符，可能在前端split时出现问题"
echo "- 建议检查前端命令字符串的解析逻辑" 