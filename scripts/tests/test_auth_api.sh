#!/bin/bash

echo "🧪 测试API认证"
echo "=============="

# 测试无认证的请求
echo "1. 测试无认证的请求..."
response1=$(curl -s -X POST http://localhost:8081/api/whatsapp/accounts/1/connect-session)
echo "无认证响应: $response1"

# 测试无效认证的请求
echo ""
echo "2. 测试无效认证的请求..."
response2=$(curl -s -X POST http://localhost:8081/api/whatsapp/accounts/1/connect-session -H "Authorization: Bearer invalid-token")
echo "无效认证响应: $response2"

# 测试健康检查（不需要认证）
echo ""
echo "3. 测试健康检查..."
response3=$(curl -s http://localhost:8081/health)
echo "健康检查响应: $response3"

# 检查Go后端日志
echo ""
echo "4. 检查最近的Go后端日志..."
cd backend && tail -5 backend.log 2>/dev/null || echo "无法读取日志"

echo ""
echo "✅ 测试完成"
echo ""
echo "📝 分析："
echo "- 如果无认证和无效认证都返回401，说明API端点存在"
echo "- 问题可能是前端用户未登录或token无效"
echo "- 需要确保用户已登录并获取有效token" 