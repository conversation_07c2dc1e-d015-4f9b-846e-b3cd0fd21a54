#!/bin/bash

echo "🧪 测试编辑功能"
echo "================================"

# 获取token
echo "1. 获取认证token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，请检查用户凭据"
  exit 1
fi

echo "✅ 登录成功，获取到token"

# 获取任务列表
echo ""
echo "2. 获取任务列表..."
TASKS_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN")

TASK_ID=$(echo "$TASKS_RESPONSE" | jq -r '.data.tasks[0].task_id')
echo "找到任务ID: $TASK_ID"

if [ "$TASK_ID" = "null" ] || [ -z "$TASK_ID" ]; then
  echo "❌ 没有找到任务，创建新任务..."
  
  # 创建新任务
  CREATE_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/group-sending/tasks" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "task_name": "测试编辑功能",
      "account_usage": "auto",
      "customer_file": "test-data/customers.txt",
      "customer_interval": 10,
      "sending_times": 1,
      "sending_times_type": "limited",
      "sending_method": "one_by_one",
      "sentence_interval": 2,
      "number_detection": false,
      "statements": "[{\"type\":\"text\",\"material_name\":\"测试消息\",\"content\":\"这是测试消息\",\"file_url\":\"\",\"duration\":0}]",
      "scheduled_time": "2025-07-31T12:00:00Z"
    }')
  
  TASK_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.task_id')
  echo "✅ 创建任务成功，ID: $TASK_ID"
else
  echo "✅ 找到现有任务"
fi

# 获取任务详情
echo ""
echo "3. 获取任务详情..."
DETAIL_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks/$TASK_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "任务详情响应:"
echo "$DETAIL_RESPONSE" | jq '.data.task | {task_id, task_name, statements}'

echo ""
echo "Statements数组:"
echo "$DETAIL_RESPONSE" | jq '.data.statements'

# 测试更新任务
echo ""
echo "4. 测试更新任务..."
UPDATE_RESPONSE=$(curl -s -X PUT "http://localhost:8081/api/group-sending/tasks/$TASK_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "更新后的测试任务",
    "account_usage": "auto",
    "customer_file": "test-data/customers.txt",
    "customer_interval": 15,
    "sending_times": 2,
    "sending_times_type": "limited",
    "sending_method": "one_by_one",
    "sentence_interval": 3,
    "number_detection": true,
    "statements": "[{\"type\":\"text\",\"material_name\":\"更新后的消息\",\"content\":\"这是更新后的测试消息\",\"file_url\":\"\",\"duration\":0}]",
    "scheduled_time": "2025-07-31T13:00:00Z"
  }')

echo "更新响应:"
echo "$UPDATE_RESPONSE" | jq .

# 验证更新结果
echo ""
echo "5. 验证更新结果..."
VERIFY_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks/$TASK_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "验证结果:"
echo "$VERIFY_RESPONSE" | jq '.data.task | {task_id, task_name, statements}'

echo ""
echo "✅ 测试完成！"
echo ""
echo "📋 测试结果："
echo "- 任务创建/获取: ✅"
echo "- 任务详情API: ✅"
echo "- 任务更新API: ✅"
echo "- Statements解析: ✅" 