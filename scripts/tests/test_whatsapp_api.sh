#!/bin/bash

# WhatsApp API 测试脚本
BASE_URL="http://localhost:8081/api"
TOKEN=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 登录获取token
login() {
    print_info "正在登录..."
    
    # 直接使用临时文件来避免变量赋值问题
    RESPONSE_FILE=$(mktemp)
    curl --noproxy "*" -s -X POST "$BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "admin",
            "password": "admin123"
        }' > "$RESPONSE_FILE"
    
    RESPONSE=$(cat "$RESPONSE_FILE")
    echo "Response: $RESPONSE"
    
    TOKEN=$(cat "$RESPONSE_FILE" | jq -r '.data.token')
    echo "Token: $TOKEN"
    
    rm "$RESPONSE_FILE"
    
    if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
        print_success "登录成功，获取到token"
    else
        print_error "登录失败"
        echo $RESPONSE
        exit 1
    fi
}

# 测试WhatsApp分组API
test_groups() {
    print_info "测试WhatsApp分组API..."
    
    # 获取分组列表
    print_info "获取分组列表..."
    RESPONSE=$(curl --noproxy "*" -s -X GET "$BASE_URL/whatsapp/groups" \
        -H "Authorization: Bearer $TOKEN")
    echo $RESPONSE | jq '.'
    
    # 创建测试分组
    print_info "创建测试分组..."
    RESPONSE=$(curl --noproxy "*" -s -X POST "$BASE_URL/whatsapp/groups" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "name": "测试分组",
            "description": "这是一个测试分组",
            "color": "#FF6B6B",
            "sort_order": 1,
            "status": true
        }')
    echo $RESPONSE | jq '.'
    
    # 获取分组列表
    print_info "再次获取分组列表..."
    RESPONSE=$(curl --noproxy "*" -s -X GET "$BASE_URL/whatsapp/groups" \
        -H "Authorization: Bearer $TOKEN")
    echo $RESPONSE | jq '.'
}

# 测试WhatsApp账号API
test_accounts() {
    print_info "测试WhatsApp账号API..."
    
    # 获取账号列表
    print_info "获取账号列表..."
    RESPONSE=$(curl --noproxy "*" -s -X GET "$BASE_URL/whatsapp/accounts" \
        -H "Authorization: Bearer $TOKEN")
    echo $RESPONSE | jq '.'
    
    # 创建测试账号
    print_info "创建测试账号..."
    RESPONSE=$(curl --noproxy "*" -s -X POST "$BASE_URL/whatsapp/accounts" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "account_name": "测试账号",
            "phone_number": "***********",
            "group_id": null,
            "auto_reconnect": true,
            "max_reconnect_attempts": 3
        }')
    echo $RESPONSE | jq '.'
    
    # 获取账号列表
    print_info "再次获取账号列表..."
    RESPONSE=$(curl --noproxy "*" -s -X GET "$BASE_URL/whatsapp/accounts" \
        -H "Authorization: Bearer $TOKEN")
    echo $RESPONSE | jq '.'
}

# 主函数
main() {
    print_info "开始WhatsApp API测试..."
    
    # 检查jq是否安装
    if ! command -v jq &> /dev/null; then
        print_error "jq未安装，请先安装jq: brew install jq"
        exit 1
    fi
    
    # 检查后端服务是否运行
    if ! curl --noproxy "*" -s "$BASE_URL/../health" > /dev/null; then
        print_error "后端服务未运行，请先启动后端服务"
        exit 1
    fi
    
    # 执行测试
    login
    test_groups
    test_accounts
    
    print_success "WhatsApp API测试完成！"
}

# 运行主函数
main 