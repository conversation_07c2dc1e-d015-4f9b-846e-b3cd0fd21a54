#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}测试创建Session并连接...${NC}"

# 1. 登录获取token
echo -e "${YELLOW}1. 登录获取token...${NC}"

LOGIN_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -n "$TOKEN" ]]; then
    echo -e "${GREEN}✓ 登录成功${NC}"
else
    echo -e "${RED}✗ 登录失败${NC}"
    exit 1
fi

# 2. 创建新的Session
echo -e "${YELLOW}2. 创建新的Session...${NC}"

CREATE_SESSION_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/whatsapp/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "phone_number": "*************",
    "account_name": "测试账号2",
    "group_id": null
  }')

echo "创建Session响应: $CREATE_SESSION_RESPONSE"

# 3. 获取新创建的账号ID
echo -e "${YELLOW}3. 获取新创建的账号...${NC}"

ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

echo "账号列表: $ACCOUNTS_RESPONSE"

# 4. 等待几秒钟让Session初始化
echo -e "${YELLOW}4. 等待Session初始化...${NC}"
sleep 3

# 5. 尝试连接
echo -e "${YELLOW}5. 尝试连接账号...${NC}"

# 获取最新的账号ID
LATEST_ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)

if [[ -n "$LATEST_ACCOUNT_ID" ]]; then
    echo -e "${GREEN}✓ 找到最新账号ID: $LATEST_ACCOUNT_ID${NC}"
    
    CONNECT_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/whatsapp/accounts/$LATEST_ACCOUNT_ID/connect-session" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN")
    
    echo "连接响应: $CONNECT_RESPONSE"
else
    echo -e "${RED}✗ 未找到新账号${NC}"
fi

echo -e "${GREEN}测试完成！${NC}" 