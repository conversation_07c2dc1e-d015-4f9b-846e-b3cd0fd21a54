#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始完整集成测试...${NC}"

# 1. 检查所有服务状态
echo -e "${YELLOW}1. 检查服务状态...${NC}"

# 检查Go后端
GO_HEALTH=$(curl --noproxy localhost -s http://localhost:8081/health)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✓ Go后端运行正常${NC}"
else
    echo -e "${RED}✗ Go后端连接失败${NC}"
    exit 1
fi

# 检查Node服务
NODE_HEALTH=$(curl --noproxy localhost -s http://localhost:3000/health)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✓ Node服务运行正常${NC}"
else
    echo -e "${RED}✗ Node服务连接失败${NC}"
    exit 1
fi

# 检查前端服务
FRONTEND_HEALTH=$(curl --noproxy localhost -s http://localhost:5173)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✓ 前端服务运行正常${NC}"
else
    echo -e "${RED}✗ 前端服务连接失败${NC}"
    exit 1
fi

# 2. 测试用户认证
echo -e "${YELLOW}2. 测试用户认证...${NC}"

LOGIN_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -n "$TOKEN" ]]; then
    echo -e "${GREEN}✓ 用户认证成功${NC}"
else
    echo -e "${RED}✗ 用户认证失败${NC}"
    exit 1
fi

# 3. 测试创建WhatsApp Session
echo -e "${YELLOW}3. 测试创建WhatsApp Session...${NC}"

CREATE_SESSION_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/whatsapp/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "phone_number": "*************",
    "account_name": "测试账号",
    "group_id": null
  }')

echo "创建Session响应: $CREATE_SESSION_RESPONSE"

# 4. 测试获取账号列表
echo -e "${YELLOW}4. 测试获取账号列表...${NC}"

ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

echo "账号列表响应: $ACCOUNTS_RESPONSE"

# 5. 测试Node服务功能
echo -e "${YELLOW}5. 测试Node服务功能...${NC}"

# 获取Session列表
SESSIONS_RESPONSE=$(curl --noproxy localhost -s "http://localhost:3000/api/whatsapp/sessions")
echo "Node Session列表: $SESSIONS_RESPONSE"

# 6. 测试前端页面访问
echo -e "${YELLOW}6. 测试前端页面访问...${NC}"

FRONTEND_PAGE=$(curl --noproxy localhost -s "http://localhost:5173")
if [[ $FRONTEND_PAGE == *"Vite App"* ]]; then
    echo -e "${GREEN}✓ 前端页面访问正常${NC}"
else
    echo -e "${RED}✗ 前端页面访问失败${NC}"
fi

echo -e "${GREEN}完整集成测试完成！${NC}"
echo -e "${YELLOW}服务地址:${NC}"
echo -e "  - Go后端: http://localhost:8081"
echo -e "  - Node服务: http://localhost:3000"
echo -e "  - 前端: http://localhost:5173"
echo -e "${YELLOW}管理界面:${NC}"
echo -e "  - 登录: http://localhost:5173/login"
echo -e "  - WhatsApp管理: http://localhost:5173/whatsapp/accounts" 