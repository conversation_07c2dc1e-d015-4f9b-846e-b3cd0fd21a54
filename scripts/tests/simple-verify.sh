#!/bin/bash

echo "=== Hive SaaS 群发功能系统验证 ==="
echo ""

# 检查服务状态
echo "1. 检查服务状态..."

echo "后端服务 (端口8081):"
if curl -s http://localhost:8081/health > /dev/null; then
    echo "✅ 后端服务正常运行"
else
    echo "❌ 后端服务未响应"
fi

echo ""
echo "WhatsApp服务 (端口3000):"
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ WhatsApp服务正常运行"
else
    echo "❌ WhatsApp服务未响应"
fi

echo ""
echo "前端服务 (端口5173):"
if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ 前端服务正常运行"
else
    echo "❌ 前端服务未响应"
fi

# 测试API功能
echo ""
echo "2. 测试API功能..."

echo ""
echo "登录测试:"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

echo "登录响应: $LOGIN_RESPONSE"

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

echo "提取的Token: $TOKEN"

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
    echo "✅ 登录成功"
    echo "Token: ${TOKEN:0:50}..."
    
    echo ""
    echo "群发任务API测试:"
    TASKS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/group-sending/tasks \
      -H "Authorization: Bearer $TOKEN")
    
    echo "任务列表响应: $TASKS_RESPONSE"
    
    if echo "$TASKS_RESPONSE" | jq -e '.data.tasks' > /dev/null 2>&1; then
        echo "✅ 群发任务API正常"
        TASK_COUNT=$(echo "$TASKS_RESPONSE" | jq '.data.total')
        echo "当前任务数量: $TASK_COUNT"
    else
        echo "❌ 群发任务API异常"
    fi
    
else
    echo "❌ 登录失败"
fi

# 检查数据库和测试数据
echo ""
echo "3. 检查数据库和测试数据..."

if [ -f "storage/database/hive.db" ]; then
    echo "✅ 数据库文件存在"
    DB_SIZE=$(ls -lh storage/database/hive.db | awk '{print $5}')
    echo "数据库大小: $DB_SIZE"
else
    echo "❌ 数据库文件不存在"
fi

if [ -f "test-data/customers.txt" ]; then
    echo "✅ 测试客户文件存在"
    CUSTOMER_COUNT=$(wc -l < test-data/customers.txt)
    echo "客户数量: $CUSTOMER_COUNT"
else
    echo "❌ 测试客户文件不存在"
fi

# 系统信息
echo ""
echo "4. 系统信息..."

echo "服务端口:"
echo "  后端服务: http://localhost:8081"
echo "  WhatsApp服务: http://localhost:3000"
echo "  前端服务: http://localhost:5173"

echo ""
echo "测试账号:"
echo "  用户名: admin"
echo "  密码: admin123"

echo ""
echo "访问地址:"
echo "  前端界面: http://localhost:5173"
echo "  群发功能: http://localhost:5173/#/marketing/group-sending"

echo ""
echo "=== 验证完成 ==="
echo ""
echo "下一步操作:"
echo "1. 打开浏览器访问: http://localhost:5173"
echo "2. 使用测试账号登录: admin/admin123"
echo "3. 进入'营销互动' -> '陌生人群发'"
echo "4. 创建群发任务进行测试"
echo ""
echo "安全提醒:"
echo "- 使用测试账号进行WhatsApp连接"
echo "- 使用预设的安全测试消息"
echo "- 设置合理的发送间隔 (60秒以上)"
echo "- 避免发送敏感或商业推广内容" 