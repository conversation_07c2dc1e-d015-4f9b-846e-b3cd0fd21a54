#!/bin/bash

echo "🔧 测试文件上传功能"
echo "================================"

# 获取JWT Token
echo "1. 获取Token..."
TOKEN=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 获取Token失败"
    exit 1
fi

echo "✅ Token获取成功"

echo ""
echo "2. 测试文件上传..."
UPLOAD_RESPONSE=$(curl -s -X POST http://localhost:8081/api/files/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@phonenum.txt" \
  -F "category=customer_list")

echo "上传响应:"
echo "$UPLOAD_RESPONSE" | jq '.'

# 检查上传是否成功
UPLOAD_CODE=$(echo "$UPLOAD_RESPONSE" | jq -r '.code // 0')
FILE_PATH=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.file.file_path // empty')

if [ "$UPLOAD_CODE" = "200" ] && [ -n "$FILE_PATH" ]; then
    echo "✅ 文件上传成功: $FILE_PATH"
    
    echo ""
    echo "3. 验证文件内容..."
    if [ -f "$FILE_PATH" ]; then
        echo "✅ 文件存在"
        echo "文件内容:"
        cat "$FILE_PATH"
    else
        echo "❌ 文件不存在: $FILE_PATH"
    fi
else
    echo "❌ 文件上传失败"
fi

echo ""
echo "测试完成！"