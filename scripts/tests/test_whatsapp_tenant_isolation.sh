#!/bin/bash

# 测试WhatsApp管理的租户隔离功能
# 验证系统管理员和租户管理员都只能看到当前租户的数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BACKEND_URL="http://localhost:8081"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"

echo -e "${BLUE}=== WhatsApp管理租户隔离功能测试 ===${NC}"
echo

# 函数：打印测试结果
print_result() {
    local test_name="$1"
    local status="$2"
    local message="$3"
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✓ $test_name: $message${NC}"
    else
        echo -e "${RED}✗ $test_name: $message${NC}"
    fi
}

# 函数：检查服务状态
check_services() {
    echo -e "${YELLOW}1. 检查服务状态...${NC}"
    
    # 检查后端服务
    if curl --noproxy "*" -s "$BACKEND_URL/health" > /dev/null 2>&1; then
        print_result "后端服务" "PASS" "运行正常"
    else
        print_result "后端服务" "FAIL" "无法连接"
        return 1
    fi
    
    echo
}

# 函数：获取访问令牌
get_access_token() {
    echo -e "${YELLOW}2. 获取访问令牌...${NC}"
    
    local response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$ADMIN_USERNAME\",
            \"password\": \"$ADMIN_PASSWORD\"
        }")
    
    if echo "$response" | grep -q '"token"'; then
        ACCESS_TOKEN=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        print_result "获取令牌" "PASS" "成功获取访问令牌"
        echo "令牌: ${ACCESS_TOKEN:0:20}..."
    else
        print_result "获取令牌" "FAIL" "登录失败"
        echo "响应: $response"
        return 1
    fi
    
    echo
}

# 函数：测试系统管理租户的WhatsApp数据隔离
test_system_tenant_isolation() {
    echo -e "${YELLOW}3. 测试系统管理租户的WhatsApp数据隔离...${NC}"
    
    # 首先切换到系统管理（租户ID为0）
    local switch_response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/tenant/switch" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"tenant_id\": 0,
            \"reason\": \"测试系统管理模式下的数据隔离\"
        }")
    
    echo "切换到系统管理响应: $switch_response"
    
    # 如果切换成功，获取新的token
    if echo "$switch_response" | grep -q '"code":200'; then
        ACCESS_TOKEN=$(echo "$switch_response" | grep -o '"new_token":"[^"]*"' | cut -d'"' -f4)
        print_result "切换到系统管理" "PASS" "成功切换到系统管理模式"
        echo "新令牌: ${ACCESS_TOKEN:0:20}..."
    else
        print_result "切换到系统管理" "FAIL" "无法切换到系统管理模式"
        echo "响应: $switch_response"
        return 1
    fi
    
    # 测试获取WhatsApp账号列表（应该返回错误，因为系统管理租户没有账号）
    local accounts_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/whatsapp/accounts" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "系统管理租户获取账号响应: $accounts_response"
    
    # 检查是否返回了正确的错误信息
    if echo "$accounts_response" | grep -q "用户必须属于某个租户才能查看WhatsApp账号"; then
        print_result "系统管理租户账号隔离" "PASS" "系统管理租户正确拒绝访问WhatsApp账号"
    else
        print_result "系统管理租户账号隔离" "FAIL" "系统管理租户应该拒绝访问WhatsApp账号"
    fi
    
    # 测试获取WhatsApp分组列表（应该返回错误，因为系统管理租户没有分组）
    local groups_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/whatsapp/groups" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "系统管理租户获取分组响应: $groups_response"
    
    # 检查是否返回了正确的错误信息
    if echo "$groups_response" | grep -q "用户必须属于某个租户才能查看WhatsApp分组"; then
        print_result "系统管理租户分组隔离" "PASS" "系统管理租户正确拒绝访问WhatsApp分组"
    else
        print_result "系统管理租户分组隔离" "FAIL" "系统管理租户应该拒绝访问WhatsApp分组"
    fi
    
    echo
}

# 函数：测试其他租户的WhatsApp数据隔离
test_other_tenant_isolation() {
    echo -e "${YELLOW}4. 测试其他租户的WhatsApp数据隔离...${NC}"
    
    # 获取租户列表
    local tenants_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/tenants" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "租户列表响应: $tenants_response"
    
    # 查找非系统管理租户
    local non_system_tenant_id=$(echo "$tenants_response" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -n "$non_system_tenant_id" ]; then
        print_result "找到其他租户" "PASS" "租户ID: $non_system_tenant_id"
        
        # 切换到其他租户
        local switch_response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/tenant/switch" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"tenant_id\": $non_system_tenant_id,
                \"reason\": \"测试其他租户的数据隔离\"
            }")
        
        echo "切换租户响应: $switch_response"
        
        # 如果切换成功，获取新的token
        if echo "$switch_response" | grep -q '"code":200'; then
            ACCESS_TOKEN=$(echo "$switch_response" | grep -o '"new_token":"[^"]*"' | cut -d'"' -f4)
            print_result "切换到其他租户" "PASS" "成功切换到其他租户"
            echo "新令牌: ${ACCESS_TOKEN:0:20}..."
        else
            print_result "切换到其他租户" "FAIL" "无法切换到其他租户"
            echo "响应: $switch_response"
            return 1
        fi
        
        # 测试获取WhatsApp账号列表（应该成功，但只显示当前租户的账号）
        local accounts_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/whatsapp/accounts" \
            -H "Authorization: Bearer $ACCESS_TOKEN")
        
        echo "其他租户获取账号响应: $accounts_response"
        
        # 检查是否成功获取账号列表
        if echo "$accounts_response" | grep -q '"code":200'; then
            print_result "其他租户账号访问" "PASS" "其他租户可以正常访问WhatsApp账号列表"
        else
            print_result "其他租户账号访问" "FAIL" "其他租户应该可以访问WhatsApp账号列表"
        fi
        
        # 测试获取WhatsApp分组列表（应该成功，但只显示当前租户的分组）
        local groups_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/whatsapp/groups" \
            -H "Authorization: Bearer $ACCESS_TOKEN")
        
        echo "其他租户获取分组响应: $groups_response"
        
        # 检查是否成功获取分组列表
        if echo "$groups_response" | grep -q '"code":200'; then
            print_result "其他租户分组访问" "PASS" "其他租户可以正常访问WhatsApp分组列表"
        else
            print_result "其他租户分组访问" "FAIL" "其他租户应该可以访问WhatsApp分组列表"
        fi
        
    else
        print_result "找到其他租户" "FAIL" "未找到非系统管理租户"
    fi
    
    echo
}

# 函数：测试权限控制
test_permission_control() {
    echo -e "${YELLOW}5. 测试权限控制...${NC}"
    
    # 测试系统管理员在租户中的权限
    if [ -n "$non_system_tenant_id" ]; then
        # 测试创建WhatsApp账号的权限
        local create_account_response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/whatsapp/accounts" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"account_name\": \"测试账号\",
                \"phone_number\": \"***********\",
                \"group_id\": null,
                \"auto_reconnect\": true,
                \"max_reconnect_attempts\": 3
            }")
        
        echo "创建账号响应: $create_account_response"
        
        # 检查是否有权限创建账号
        if echo "$create_account_response" | grep -q '"code":200'; then
            print_result "系统管理员创建账号权限" "PASS" "系统管理员在租户中可以创建WhatsApp账号"
        else
            print_result "系统管理员创建账号权限" "FAIL" "系统管理员应该可以在租户中创建WhatsApp账号"
        fi
        
        # 测试创建WhatsApp分组的权限
        local create_group_response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/whatsapp/groups" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"name\": \"测试分组\",
                \"description\": \"这是一个测试分组\",
                \"color\": \"#FF6B6B\",
                \"sort_order\": 1,
                \"status\": true
            }")
        
        echo "创建分组响应: $create_group_response"
        
        # 检查是否有权限创建分组
        if echo "$create_group_response" | grep -q '"code":200'; then
            print_result "系统管理员创建分组权限" "PASS" "系统管理员在租户中可以创建WhatsApp分组"
        else
            print_result "系统管理员创建分组权限" "FAIL" "系统管理员应该可以在租户中创建WhatsApp分组"
        fi
        
    fi
    
    echo
}

# 主测试流程
main() {
    echo -e "${BLUE}开始WhatsApp管理租户隔离功能测试${NC}"
    echo "=================================================="
    echo
    
    # 执行测试步骤
    check_services || exit 1
    get_access_token || exit 1
    test_system_tenant_isolation
    test_other_tenant_isolation
    test_permission_control
    
    echo -e "${GREEN}=== 测试完成 ===${NC}"
    echo -e "${BLUE}测试总结:${NC}"
    echo "- 验证了系统管理租户的WhatsApp数据隔离"
    echo "- 验证了其他租户的WhatsApp数据访问"
    echo "- 验证了权限控制逻辑"
    echo "- 确保所有用户都只能看到当前租户的数据"
}

# 执行主函数
main "$@" 