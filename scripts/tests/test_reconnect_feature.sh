#!/bin/bash

# 测试断开Chrome后重连功能
echo "🧪 测试断开Chrome后重连功能"

# 设置curl别名
alias curl='curl --noproxy "*"'

# 1. 创建测试账户
echo "📝 步骤1: 创建测试账户"
ACCOUNT_RESPONSE=$(curl -s -X POST http://localhost:8081/api/whatsapp/accounts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{
    "account_name": "test-reconnect-account",
    "phone_number": "**********"
  }')

echo "账户创建响应: $ACCOUNT_RESPONSE"

# 提取账户ID
ACCOUNT_ID=$(echo $ACCOUNT_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "账户ID: $ACCOUNT_ID"

if [ -z "$ACCOUNT_ID" ]; then
    echo "❌ 无法获取账户ID，退出测试"
    exit 1
fi

# 2. 创建session
echo "📝 步骤2: 创建session"
SESSION_RESPONSE=$(curl -s -X POST http://localhost:8081/api/whatsapp/sessions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{
    "account_name": "test-reconnect-account",
    "phone_number": "**********"
  }')

echo "Session创建响应: $SESSION_RESPONSE"

# 3. 检查Chrome进程
echo "📝 步骤3: 检查Chrome进程"
ps aux | grep -i chrome | grep -v grep
CHROME_COUNT=$(ps aux | grep -i chrome | grep -v grep | wc -l)
echo "当前Chrome进程数量: $CHROME_COUNT"

# 4. 断开连接（关闭Chrome）
echo "📝 步骤4: 断开连接（关闭Chrome）"
DISCONNECT_RESPONSE=$(curl -s -X POST http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID/disconnect-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{
    "account_id": '$ACCOUNT_ID'
  }')

echo "断开连接响应: $DISCONNECT_RESPONSE"

# 等待一下
sleep 2

# 5. 再次检查Chrome进程
echo "📝 步骤5: 检查断开后的Chrome进程"
ps aux | grep -i chrome | grep -v grep
CHROME_COUNT_AFTER=$(ps aux | grep -i chrome | grep -v grep | wc -l)
echo "断开后Chrome进程数量: $CHROME_COUNT_AFTER"

# 6. 重连
echo "📝 步骤6: 重连"
RECONNECT_RESPONSE=$(curl -s -X POST http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID/reconnect-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{
    "account_id": '$ACCOUNT_ID'
  }')

echo "重连响应: $RECONNECT_RESPONSE"

# 等待一下
sleep 3

# 7. 检查重连后的Chrome进程
echo "📝 步骤7: 检查重连后的Chrome进程"
ps aux | grep -i chrome | grep -v grep
CHROME_COUNT_RECONNECT=$(ps aux | grep -i chrome | grep -v grep | wc -l)
echo "重连后Chrome进程数量: $CHROME_COUNT_RECONNECT"

# 8. 检查session文件
echo "📝 步骤8: 检查session文件"
ls -la whatsapp-node-service/sessions/ | grep session

# 9. 测试结果总结
echo "📊 测试结果总结:"
echo "  - 初始Chrome进程数: $CHROME_COUNT"
echo "  - 断开后Chrome进程数: $CHROME_COUNT_AFTER"
echo "  - 重连后Chrome进程数: $CHROME_COUNT_RECONNECT"

if [ "$CHROME_COUNT_AFTER" -lt "$CHROME_COUNT" ] && [ "$CHROME_COUNT_RECONNECT" -gt "$CHROME_COUNT_AFTER" ]; then
    echo "✅ 测试成功: Chrome进程正确关闭和重启"
else
    echo "❌ 测试失败: Chrome进程管理异常"
fi

echo "🧹 清理测试数据..."
# 清理测试账户
curl -s -X DELETE http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID \
  -H "Authorization: Bearer test-token"

echo "✅ 测试完成" 