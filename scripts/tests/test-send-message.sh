#!/bin/bash

echo "=== WhatsApp发送消息功能测试 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务状态
echo -e "${BLUE}1. 检查服务状态...${NC}"

echo -e "\n${YELLOW}后端服务 (端口8081):${NC}"
if curl -s http://localhost:8081/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务正常运行${NC}"
else
    echo -e "${RED}❌ 后端服务未响应${NC}"
    exit 1
fi

echo -e "\n${YELLOW}WhatsApp服务 (端口3000):${NC}"
if curl -s http://localhost:3000/health > /dev/null; then
    echo -e "${GREEN}✅ WhatsApp服务正常运行${NC}"
else
    echo -e "${RED}❌ WhatsApp服务未响应${NC}"
fi

echo -e "\n${YELLOW}前端服务 (端口5173):${NC}"
if curl -s http://localhost:5173 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务正常运行${NC}"
else
    echo -e "${RED}❌ 前端服务未响应${NC}"
fi

# 登录获取token
echo -e "\n${BLUE}2. 登录获取token...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
    echo -e "${GREEN}✅ 登录成功${NC}"
    echo -e "Token: ${TOKEN:0:50}..."
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

# 获取WhatsApp账号列表
echo -e "\n${BLUE}3. 获取WhatsApp账号列表...${NC}"
ACCOUNTS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/whatsapp/accounts \
  -H "Authorization: Bearer $TOKEN")

ACCOUNT_COUNT=$(echo "$ACCOUNTS_RESPONSE" | jq '.data.accounts | length')

if [ "$ACCOUNT_COUNT" -gt 0 ]; then
    echo -e "${GREEN}✅ 找到 $ACCOUNT_COUNT 个WhatsApp账号${NC}"
    
    # 获取第一个账号的ID
    FIRST_ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].id')
    FIRST_ACCOUNT_NAME=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].account_name')
    FIRST_ACCOUNT_STATUS=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].connection_status')
    
    echo -e "第一个账号: ID=$FIRST_ACCOUNT_ID, 名称=$FIRST_ACCOUNT_NAME, 状态=$FIRST_ACCOUNT_STATUS"
    
    if [ "$FIRST_ACCOUNT_STATUS" = "connected" ]; then
        echo -e "${GREEN}✅ 账号已连接，可以进行发送测试${NC}"
        
        # 测试发送历史API
        echo -e "\n${BLUE}4. 测试发送历史API...${NC}"
        HISTORY_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/whatsapp/accounts/$FIRST_ACCOUNT_ID/send-history" \
          -H "Authorization: Bearer $TOKEN")
        
        if echo "$HISTORY_RESPONSE" | jq -e '.data' > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 发送历史API正常${NC}"
            HISTORY_COUNT=$(echo "$HISTORY_RESPONSE" | jq '.data.total')
            echo -e "历史记录数量: $HISTORY_COUNT"
        else
            echo -e "${RED}❌ 发送历史API异常${NC}"
            echo "响应: $HISTORY_RESPONSE"
        fi
        
        # 测试发送消息API（模拟）
        echo -e "\n${BLUE}5. 测试发送消息API...${NC}"
        echo -e "${YELLOW}注意: 这是模拟测试，不会真正发送消息${NC}"
        
        # 创建测试文件
        echo "这是一个测试文件" > test-message.txt
        
        SEND_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/whatsapp/accounts/$FIRST_ACCOUNT_ID/send-message" \
          -H "Authorization: Bearer $TOKEN" \
          -F "phone_number=*************" \
          -F "message_type=text" \
          -F "message=这是一条测试消息，请忽略。")
        
        if echo "$SEND_RESPONSE" | jq -e '.code' > /dev/null 2>&1; then
            RESPONSE_CODE=$(echo "$SEND_RESPONSE" | jq -r '.code')
            if [ "$RESPONSE_CODE" = "200" ]; then
                echo -e "${GREEN}✅ 发送消息API正常${NC}"
            else
                echo -e "${YELLOW}⚠️  发送消息API返回: $RESPONSE_CODE${NC}"
                echo "响应: $SEND_RESPONSE"
            fi
        else
            echo -e "${RED}❌ 发送消息API异常${NC}"
            echo "响应: $SEND_RESPONSE"
        fi
        
        # 清理测试文件
        rm -f test-message.txt
        
    else
        echo -e "${YELLOW}⚠️  账号未连接，无法进行发送测试${NC}"
        echo -e "当前状态: $FIRST_ACCOUNT_STATUS"
    fi
    
else
    echo -e "${RED}❌ 没有找到WhatsApp账号${NC}"
    echo "响应: $ACCOUNTS_RESPONSE"
fi

# 前端访问信息
echo -e "\n${BLUE}6. 前端访问信息...${NC}"
echo -e "${YELLOW}前端界面:${NC}"
echo -e "  http://localhost:5173"
echo -e ""
echo -e "${YELLOW}WhatsApp账号管理:${NC}"
echo -e "  http://localhost:5173/#/whatsapp/accounts"
echo -e ""
echo -e "${YELLOW}发送消息页面:${NC}"
if [ "$ACCOUNT_COUNT" -gt 0 ]; then
    echo -e "  http://localhost:5173/#/whatsapp/accounts/$FIRST_ACCOUNT_ID/send-message"
else
    echo -e "  需要先创建WhatsApp账号"
fi

echo -e "\n${BLUE}7. 测试完成总结...${NC}"

# 检查所有关键组件
COMPONENTS=(
    "后端服务:8081"
    "WhatsApp服务:3000"
    "前端服务:5173"
    "数据库连接:backend/hive.db"
)

ALL_OK=true

for component in "${COMPONENTS[@]}"; do
    name=$(echo $component | cut -d: -f1)
    port=$(echo $component | cut -d: -f2)
    
    if [[ $port =~ ^[0-9]+$ ]]; then
        # 检查端口
        if curl -s http://localhost:$port/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name 正常${NC}"
        else
            echo -e "${RED}❌ $name 异常${NC}"
            ALL_OK=false
        fi
    else
        # 检查文件
        if [ -f "$port" ]; then
            echo -e "${GREEN}✅ $name 存在${NC}"
        else
            echo -e "${RED}❌ $name 不存在${NC}"
            ALL_OK=false
        fi
    fi
done

echo ""
if [ "$ALL_OK" = true ]; then
    echo -e "${GREEN}🎉 发送消息功能测试完成！所有组件正常运行。${NC}"
    echo ""
    echo -e "${BLUE}下一步操作:${NC}"
    echo -e "1. 打开浏览器访问: http://localhost:5173"
    echo -e "2. 使用测试账号登录: admin/admin123"
    echo -e "3. 进入'WhatsApp管理' -> 'WhatsApp账号管理'"
    echo -e "4. 点击账号的'更多'按钮 -> '发送消息'"
    echo -e "5. 在发送消息页面进行测试"
    echo ""
    echo -e "${YELLOW}安全提醒:${NC}"
    echo -e "- 确保WhatsApp账号已连接"
    echo -e "- 使用测试手机号进行发送"
    echo -e "- 避免发送敏感或商业推广内容"
    echo -e "- 设置合理的发送间隔"
else
    echo -e "${RED}⚠️  测试发现问题，请检查上述异常组件。${NC}"
fi

echo ""
echo -e "${BLUE}=== 测试完成 ===${NC}" 