#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}测试WhatsApp账号连接功能...${NC}"

# 1. 登录获取token
echo -e "${YELLOW}1. 登录获取token...${NC}"

LOGIN_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -n "$TOKEN" ]]; then
    echo -e "${GREEN}✓ 登录成功${NC}"
else
    echo -e "${RED}✗ 登录失败${NC}"
    exit 1
fi

# 2. 获取账号列表
echo -e "${YELLOW}2. 获取账号列表...${NC}"

ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)

if [[ -n "$ACCOUNT_ID" ]]; then
    echo -e "${GREEN}✓ 找到账号ID: $ACCOUNT_ID${NC}"
else
    echo -e "${RED}✗ 未找到账号${NC}"
    exit 1
fi

# 3. 连接账号
echo -e "${YELLOW}3. 连接WhatsApp账号...${NC}"

CONNECT_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID/connect-session" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN")

echo "连接响应: $CONNECT_RESPONSE"

# 4. 获取账号信息
echo -e "${YELLOW}4. 获取账号详细信息...${NC}"

INFO_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID/info" \
  -H "Authorization: Bearer $TOKEN")

echo "信息响应: $INFO_RESPONSE"

# 5. 再次获取账号列表查看更新
echo -e "${YELLOW}5. 重新获取账号列表查看更新...${NC}"

UPDATED_ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

echo "更新后的账号列表: $UPDATED_ACCOUNTS_RESPONSE"

echo -e "${GREEN}测试完成！${NC}" 