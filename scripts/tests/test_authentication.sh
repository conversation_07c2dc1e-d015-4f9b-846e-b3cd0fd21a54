#!/bin/bash

echo "🔐 测试WhatsApp认证"
echo "=================="

# 测试创建新session
echo "1. 测试创建新session..."
create_response=$(curl -s -X POST http://localhost:3000/api/whatsapp/action \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-auth-1",
    "phone_number": "8615659989049",
    "tenant_id": 1,
    "action": "create"
  }')

echo "创建响应: $create_response"

# 等待一下让session初始化
echo ""
echo "2. 等待session初始化..."
sleep 3

# 测试获取session信息
echo "3. 测试获取session信息..."
info_response=$(curl -s -X POST http://localhost:3000/api/whatsapp/action \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-auth-1",
    "phone_number": "8615659989049",
    "tenant_id": 1,
    "action": "get_info"
  }')

echo "信息响应: $info_response"

# 检查认证状态
echo ""
echo "4. 检查认证状态..."
if echo "$info_response" | grep -q "未认证"; then
  echo "❌ Session未认证"
  echo "可能原因："
  echo "- 需要扫码登录"
  echo "- 认证时间不够"
  echo "- Chrome进程冲突"
else
  echo "✅ Session已认证"
fi

echo ""
echo "✅ 测试完成" 