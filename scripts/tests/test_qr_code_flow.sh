#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}测试二维码生成流程...${NC}"

# 1. 登录获取token
echo -e "${YELLOW}1. 登录获取token...${NC}"

LOGIN_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -n "$TOKEN" ]]; then
    echo -e "${GREEN}✓ 登录成功${NC}"
else
    echo -e "${RED}✗ 登录失败${NC}"
    exit 1
fi

# 2. 创建Session并获取二维码
echo -e "${YELLOW}2. 创建Session并获取二维码...${NC}"

CREATE_SESSION_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/whatsapp/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "phone_number": "*************",
    "account_name": "新账号",
    "group_id": null
  }')

echo "创建Session响应: $CREATE_SESSION_RESPONSE"

# 3. 检查是否有二维码
echo -e "${YELLOW}3. 检查二维码...${NC}"

QR_CODE=$(echo "$CREATE_SESSION_RESPONSE" | grep -o '"qr_code":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -n "$QR_CODE" ]]; then
    echo -e "${GREEN}✓ 二维码生成成功${NC}"
    echo "二维码数据: ${QR_CODE:0:50}..."
else
    echo -e "${YELLOW}⚠ 没有二维码数据，可能需要等待或重新生成${NC}"
fi

# 4. 获取账号列表
echo -e "${YELLOW}4. 获取账号列表...${NC}"

ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

echo "账号列表: $ACCOUNTS_RESPONSE"

echo -e "${GREEN}测试完成！${NC}"
echo -e "${YELLOW}现在可以访问前端页面测试二维码显示:${NC}"
echo -e "  - 前端地址: http://localhost:5173"
echo -e "  - 登录后进入WhatsApp账号管理页面"
echo -e "  - 点击'扫码登录添加账号'按钮"
echo -e "  - 选择分组后点击'获取二维码'" 