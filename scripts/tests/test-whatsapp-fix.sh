#!/bin/bash

echo "🔧 测试WhatsApp发送消息修复"
echo "================================"

# 检查服务是否运行
echo "1. 检查服务状态..."
curl -s http://localhost:3000/health | jq '.'

echo ""
echo "2. 检查活跃Session..."
curl -s http://localhost:3000/api/whatsapp/sessions | jq '.'

echo ""
echo "3. 测试发送消息..."
curl -X POST http://localhost:8081/api/whatsapp/accounts/1/send-message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $(curl -s -X POST http://localhost:8081/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' | jq -r '.data.token')" \
  -d '{
    "to": "*************",
    "message": "测试修复后的消息发送",
    "message_type": "text"
  }' | jq '.'

echo ""
echo "测试完成！"