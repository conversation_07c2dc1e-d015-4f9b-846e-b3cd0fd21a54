#!/bin/bash

echo "=== Hive SaaS 完整系统验证 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务状态
echo -e "${BLUE}1. 检查所有服务状态...${NC}"

echo -e "\n${YELLOW}后端服务 (端口8081):${NC}"
if curl -s http://localhost:8081/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务正常运行${NC}"
else
    echo -e "${RED}❌ 后端服务未响应${NC}"
    exit 1
fi

echo -e "\n${YELLOW}WhatsApp服务 (端口3000):${NC}"
if curl -s http://localhost:3000/health > /dev/null; then
    echo -e "${GREEN}✅ WhatsApp服务正常运行${NC}"
else
    echo -e "${RED}❌ WhatsApp服务未响应${NC}"
fi

echo -e "\n${YELLOW}前端服务 (端口5173):${NC}"
if curl -s http://localhost:5173 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务正常运行${NC}"
else
    echo -e "${RED}❌ 前端服务未响应${NC}"
fi

# 登录获取token
echo -e "\n${BLUE}2. 登录获取token...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
    echo -e "${GREEN}✅ 登录成功${NC}"
    echo -e "Token: ${TOKEN:0:50}..."
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

# 测试WhatsApp账号API
echo -e "\n${BLUE}3. 测试WhatsApp账号API...${NC}"
ACCOUNTS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/whatsapp/accounts \
  -H "Authorization: Bearer $TOKEN")

if echo "$ACCOUNTS_RESPONSE" | jq -e '.data.accounts' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ WhatsApp账号API正常${NC}"
    ACCOUNT_COUNT=$(echo "$ACCOUNTS_RESPONSE" | jq '.data.accounts | length')
    echo -e "账号数量: $ACCOUNT_COUNT"
    
    if [ "$ACCOUNT_COUNT" -gt 0 ]; then
        FIRST_ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].id')
        FIRST_ACCOUNT_NAME=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].account_name')
        echo -e "第一个账号: ID=$FIRST_ACCOUNT_ID, 名称=$FIRST_ACCOUNT_NAME"
    fi
else
    echo -e "${RED}❌ WhatsApp账号API异常${NC}"
    echo "响应: $ACCOUNTS_RESPONSE"
fi

# 测试群发任务API
echo -e "\n${BLUE}4. 测试群发任务API...${NC}"
GROUP_SENDING_RESPONSE=$(curl -s -X GET http://localhost:8081/api/group-sending/tasks \
  -H "Authorization: Bearer $TOKEN")

if echo "$GROUP_SENDING_RESPONSE" | jq -e '.data.tasks' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 群发任务API正常${NC}"
    TASK_COUNT=$(echo "$GROUP_SENDING_RESPONSE" | jq '.data.total')
    echo -e "任务数量: $TASK_COUNT"
else
    echo -e "${RED}❌ 群发任务API异常${NC}"
    echo "响应: $GROUP_SENDING_RESPONSE"
fi

# 测试发送消息API（如果有账号）
if [ "$ACCOUNT_COUNT" -gt 0 ]; then
    echo -e "\n${BLUE}5. 测试发送消息API...${NC}"
    SEND_HISTORY_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/whatsapp/accounts/$FIRST_ACCOUNT_ID/send-history" \
      -H "Authorization: Bearer $TOKEN")
    
    if echo "$SEND_HISTORY_RESPONSE" | jq -e '.data' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 发送消息API正常${NC}"
        HISTORY_COUNT=$(echo "$SEND_HISTORY_RESPONSE" | jq '.data.total')
        echo -e "发送历史数量: $HISTORY_COUNT"
    else
        echo -e "${RED}❌ 发送消息API异常${NC}"
        echo "响应: $SEND_HISTORY_RESPONSE"
    fi
fi

# 检查数据库文件
echo -e "\n${BLUE}6. 检查数据库文件...${NC}"
if [ -f "storage/database/hive.db" ]; then
    echo -e "${GREEN}✅ 数据库文件存在${NC}"
    DB_SIZE=$(ls -lh storage/database/hive.db | awk '{print $5}')
    echo -e "数据库大小: $DB_SIZE"
else
    echo -e "${RED}❌ 数据库文件不存在${NC}"
fi

# 检查测试数据
echo -e "\n${BLUE}7. 检查测试数据...${NC}"
if [ -f "test-data/customers.txt" ]; then
    echo -e "${GREEN}✅ 测试客户文件存在${NC}"
    CUSTOMER_COUNT=$(wc -l < test-data/customers.txt)
    echo -e "客户数量: $CUSTOMER_COUNT"
else
    echo -e "${RED}❌ 测试客户文件不存在${NC}"
fi

# 检查上传目录
echo -e "\n${BLUE}8. 检查上传目录...${NC}"
if [ -d "storage/uploads" ]; then
    echo -e "${GREEN}✅ 上传目录存在${NC}"
else
    echo -e "${YELLOW}⚠️  上传目录不存在，将自动创建${NC}"
    mkdir -p storage/uploads/whatsapp
fi

# 系统信息
echo -e "\n${BLUE}9. 系统访问信息...${NC}"
echo -e "${YELLOW}前端界面:${NC}"
echo -e "  http://localhost:5173"
echo -e ""
echo -e "${YELLOW}测试账号:${NC}"
echo -e "  用户名: admin"
echo -e "  密码: admin123"
echo -e ""
echo -e "${YELLOW}主要功能页面:${NC}"
echo -e "  WhatsApp账号管理: http://localhost:5173/#/whatsapp/accounts"
if [ "$ACCOUNT_COUNT" -gt 0 ]; then
    echo -e "  账号详情页面: http://localhost:5173/#/whatsapp/accounts/$FIRST_ACCOUNT_ID"
    echo -e "  发送消息页面: http://localhost:5173/#/whatsapp/accounts/$FIRST_ACCOUNT_ID/send-message"
fi
echo -e "  群发任务管理: http://localhost:5173/#/marketing/group-sending"

# 功能验证总结
echo -e "\n${BLUE}10. 功能验证总结...${NC}"

COMPONENTS=(
    "后端服务:8081"
    "WhatsApp服务:3000"
    "前端服务:5173"
    "数据库文件:storage/database/hive.db"
    "测试数据:test-data/customers.txt"
    "上传目录:storage/uploads"
)

ALL_OK=true

for component in "${COMPONENTS[@]}"; do
    name=$(echo $component | cut -d: -f1)
    port=$(echo $component | cut -d: -f2)
    
    if [[ $port =~ ^[0-9]+$ ]]; then
        # 检查端口
        if curl -s http://localhost:$port/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name 正常${NC}"
        else
            echo -e "${RED}❌ $name 异常${NC}"
            ALL_OK=false
        fi
    else
        # 检查文件/目录
        if [ -e "$port" ]; then
            echo -e "${GREEN}✅ $name 存在${NC}"
        else
            echo -e "${RED}❌ $name 不存在${NC}"
            ALL_OK=false
        fi
    fi
done

echo ""
if [ "$ALL_OK" = true ]; then
    echo -e "${GREEN}🎉 系统验证完成！所有组件正常运行。${NC}"
    echo ""
    echo -e "${BLUE}已实现的功能:${NC}"
    echo -e "✅ 用户认证和权限管理"
    echo -e "✅ 多租户系统"
    echo -e "✅ WhatsApp账号管理"
    echo -e "✅ WhatsApp发送消息功能"
    echo -e "✅ 群发任务管理"
    echo -e "✅ 文件上传和管理"
    echo -e "✅ 操作日志记录"
    echo ""
    echo -e "${BLUE}下一步操作:${NC}"
    echo -e "1. 打开浏览器访问: http://localhost:5173"
    echo -e "2. 使用测试账号登录: admin/admin123"
    echo -e "3. 探索各个功能模块"
    echo -e "4. 测试WhatsApp发送消息功能"
    echo -e "5. 测试群发任务功能"
    echo ""
    echo -e "${YELLOW}安全提醒:${NC}"
    echo -e "- 使用测试账号进行WhatsApp连接"
    echo -e "- 使用预设的安全测试消息"
    echo -e "- 避免发送敏感或商业推广内容"
    echo -e "- 设置合理的发送间隔"
else
    echo -e "${RED}⚠️  系统验证发现问题，请检查上述异常组件。${NC}"
fi

echo ""
echo -e "${BLUE}=== 验证完成 ===${NC}" 