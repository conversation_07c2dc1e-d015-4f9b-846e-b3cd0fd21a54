#!/bin/bash

echo "=== 群发功能测试脚本 ==="
echo ""

# 检查服务状态
echo "1. 检查服务状态..."
echo "后端服务:"
curl -s http://localhost:8081/health | jq . 2>/dev/null || echo "后端服务未响应"

echo ""
echo "WhatsApp服务:"
curl -s http://localhost:3000/health | jq . 2>/dev/null || echo "WhatsApp服务未响应"

echo ""
echo "前端服务:"
curl -s http://localhost:5173 > /dev/null && echo "前端服务正常" || echo "前端服务未响应"

echo ""
echo "2. 测试群发任务API..."

# 登录获取token
echo "登录获取token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token' 2>/dev/null)

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "登录失败，请检查用户凭据"
  exit 1
fi

echo "登录成功，Token: ${TOKEN:0:20}..."

# 获取群发任务列表
echo ""
echo "获取群发任务列表..."
TASKS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/group-sending/tasks \
  -H "Authorization: Bearer $TOKEN")

echo "任务列表响应:"
echo $TASKS_RESPONSE | jq . 2>/dev/null || echo $TASKS_RESPONSE

# 创建测试任务
echo ""
echo "创建测试群发任务..."
CREATE_RESPONSE=$(curl -s -X POST http://localhost:8081/api/group-sending/tasks \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "测试群发任务",
    "account_usage": "auto",
    "customer_file": "test-data/customers.txt",
    "customer_interval": 60,
    "sending_times": 1,
    "sending_times_type": "limited",
    "sending_method": "one_by_one",
    "sentence_interval": 1,
    "number_detection": false,
    "statements": "[{\"type\":\"text\",\"content\":\"您好，这是一条测试消息，请忽略。\",\"order\":1}]"
  }')

echo "创建任务响应:"
echo $CREATE_RESPONSE | jq . 2>/dev/null || echo $CREATE_RESPONSE

# 获取任务ID
TASK_ID=$(echo $CREATE_RESPONSE | jq -r '.data.id' 2>/dev/null)

if [ "$TASK_ID" != "null" ] && [ -n "$TASK_ID" ]; then
  echo ""
  echo "任务创建成功，ID: $TASK_ID"
  
  # 获取任务详情
  echo ""
  echo "获取任务详情..."
  DETAIL_RESPONSE=$(curl -s -X GET http://localhost:8081/api/group-sending/tasks/$TASK_ID \
    -H "Authorization: Bearer $TOKEN")
  
  echo "任务详情:"
  echo $DETAIL_RESPONSE | jq . 2>/dev/null || echo $DETAIL_RESPONSE
  
  # 启动任务（仅测试API，不实际发送）
  echo ""
  echo "测试启动任务API..."
  START_RESPONSE=$(curl -s -X POST http://localhost:8081/api/group-sending/tasks/$TASK_ID/start \
    -H "Authorization: Bearer $TOKEN")
  
  echo "启动任务响应:"
  echo $START_RESPONSE | jq . 2>/dev/null || echo $START_RESPONSE
  
else
  echo "任务创建失败"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "访问地址:"
echo "- 前端: http://localhost:5173"
echo "- 后端API: http://localhost:8081"
echo "- WhatsApp服务: http://localhost:3000"
echo ""
echo "测试说明:"
echo "1. 登录系统后进入'营销互动' -> '陌生人群发'"
echo "2. 可以看到测试任务已创建"
echo "3. 可以创建新任务进行测试"
echo "4. 使用测试账号进行安全测试" 