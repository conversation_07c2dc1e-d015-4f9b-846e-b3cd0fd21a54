#!/bin/bash

echo "🧪 测试认证修复效果"
echo "===================="

# 检查服务状态
echo "1. 检查服务状态..."
echo "Go后端: $(curl -s http://localhost:8081/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
echo "Node服务: $(curl -s http://localhost:3000/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"

# 测试API端点
echo ""
echo "2. 测试清理Session API端点..."
response=$(curl -s -X DELETE http://localhost:8081/api/whatsapp/sessions/test-session/cleanup)
echo "响应: $response"

# 测试获取Session状态
echo ""
echo "3. 测试获取Session状态..."
sessions=$(curl -s http://localhost:8081/api/whatsapp/sessions/internal)
echo "Session数量: $(echo $sessions | jq '.data.data | length' 2>/dev/null || echo "获取失败")"

echo ""
echo "✅ 测试完成"
echo ""
echo "📝 修复说明："
echo "- 改进了getClientInfo方法中的认证状态检查"
echo "- 增加了等待时间，确保isRegistered属性正确更新"
echo "- 在ready事件处理中增加了延迟，确保状态同步"
echo "- 修复了API端点路径问题" 