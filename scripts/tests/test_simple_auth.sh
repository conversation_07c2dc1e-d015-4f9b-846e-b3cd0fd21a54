#!/bin/bash

echo "🔐 简单认证测试"
echo "================"

# 测试创建新session
echo "1. 创建新session..."
create_response=$(curl -s -X POST http://localhost:3000/api/whatsapp/action \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-simple-1",
    "phone_number": "8615659989049",
    "tenant_id": 1,
    "action": "create"
  }')

echo "创建响应: $create_response"

# 等待session初始化
echo ""
echo "2. 等待session初始化..."
sleep 5

# 测试获取信息
echo "3. 获取session信息..."
info_response=$(curl -s -X POST http://localhost:3000/api/whatsapp/action \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-simple-1",
    "phone_number": "8615659989049",
    "tenant_id": 1,
    "action": "get_info"
  }')

echo "信息响应: $info_response"

echo ""
echo "✅ 测试完成" 