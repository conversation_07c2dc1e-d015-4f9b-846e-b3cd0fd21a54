#!/bin/bash

echo "🔧 测试群发任务权限修复"
echo "================================"

# 获取JWT Token
echo "1. 获取系统管理员Token..."
TOKEN=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 获取Token失败"
    exit 1
fi

echo "✅ Token获取成功: ${TOKEN:0:20}..."

echo ""
echo "2. 获取群发任务列表..."
TASKS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/group-sending/tasks \
  -H "Authorization: Bearer $TOKEN")

echo "$TASKS_RESPONSE" | jq '.'

# 提取第一个任务ID
TASK_ID=$(echo "$TASKS_RESPONSE" | jq -r '.data.tasks[0].task_id // empty')

if [ -z "$TASK_ID" ]; then
    echo "⚠️ 没有找到群发任务，创建一个测试任务..."
    
    # 创建测试任务
    CREATE_RESPONSE=$(curl -s -X POST http://localhost:8081/api/group-sending/tasks \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -d '{
        "task_name": "权限测试任务",
        "account_usage": "auto",
        "customer_file": "test_customers.txt",
        "customer_interval": 60,
        "sending_times": 1,
        "sending_times_type": "limited",
        "sending_method": "one_by_one",
        "sentence_interval": 1,
        "number_detection": false,
        "statements": "[{\"type\":\"text\",\"material_name\":\"测试消息\",\"content\":\"这是一条测试消息\",\"file_url\":\"\",\"duration\":0}]"
      }')
    
    echo "创建任务响应:"
    echo "$CREATE_RESPONSE" | jq '.'
    
    TASK_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.task_id // empty')
fi

if [ -z "$TASK_ID" ]; then
    echo "❌ 无法获取任务ID"
    exit 1
fi

echo "✅ 使用任务ID: $TASK_ID"

echo ""
echo "3. 测试删除群发任务权限..."
DELETE_RESPONSE=$(curl -s -X DELETE http://localhost:8081/api/group-sending/tasks/$TASK_ID \
  -H "Authorization: Bearer $TOKEN")

echo "删除任务响应:"
echo "$DELETE_RESPONSE" | jq '.'

# 检查响应状态
DELETE_CODE=$(echo "$DELETE_RESPONSE" | jq -r '.code // 0')

if [ "$DELETE_CODE" = "200" ]; then
    echo "✅ 系统管理员可以删除群发任务"
elif [ "$DELETE_CODE" = "403" ]; then
    echo "❌ 系统管理员无权删除群发任务 - 权限检查有问题"
else
    echo "⚠️ 删除任务返回状态码: $DELETE_CODE"
fi

echo ""
echo "4. 测试批量删除权限..."
if [ "$DELETE_CODE" != "200" ]; then
    # 如果单个删除失败，测试批量删除
    BATCH_DELETE_RESPONSE=$(curl -s -X POST http://localhost:8081/api/group-sending/tasks/batch-delete \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -d "{\"task_ids\":[\"$TASK_ID\"]}")
    
    echo "批量删除响应:"
    echo "$BATCH_DELETE_RESPONSE" | jq '.'
fi

echo ""
echo "测试完成！"