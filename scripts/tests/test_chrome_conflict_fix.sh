#!/bin/bash

echo "🧪 测试Chrome进程冲突修复效果"
echo "=============================="

# 检查服务状态
echo "1. 检查服务状态..."
echo "Go后端: $(curl -s http://localhost:8081/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
echo "Node服务: $(curl -s http://localhost:3000/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"

# 检查Chrome进程
echo ""
echo "2. 检查Chrome进程..."
chrome_processes=$(ps aux | grep -i chrome | grep -v grep | wc -l)
echo "Chrome进程数量: $chrome_processes"

# 检查Session文件
echo ""
echo "3. 检查Session文件..."
session_files=$(find whatsapp-node-service/sessions -name "SingletonLock" 2>/dev/null | wc -l)
echo "SingletonLock文件数量: $session_files"

if [ "$session_files" -gt 0 ]; then
  echo "发现锁文件:"
  find whatsapp-node-service/sessions -name "SingletonLock" 2>/dev/null
fi

# 测试Session状态
echo ""
echo "4. 测试Session状态..."
sessions=$(curl -s http://localhost:8081/api/whatsapp/sessions/internal)
session_count=$(echo $sessions | jq '.data.data | length' 2>/dev/null || echo "0")
echo "Session数量: $session_count"

echo ""
echo "✅ 测试完成"
echo ""
echo "📝 修复说明："
echo "- 修复了Chrome进程冲突问题"
echo "- 在连接前自动清理SingletonLock文件"
echo "- 增加了Chrome启动参数，减少冲突"
echo "- Go后端能正确识别Chrome进程冲突"
echo "- 现在应该能正常复用现有的Chrome进程" 