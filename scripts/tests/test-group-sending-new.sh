#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 新群发功能测试 ===${NC}"

# 1. 检查服务状态
echo -e "\n${YELLOW}1. 检查服务状态...${NC}"
if curl -s http://localhost:8081/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务正常运行${NC}"
else
    echo -e "${RED}❌ 后端服务未响应${NC}"
    exit 1
fi

if curl -s http://localhost:3000/health > /dev/null; then
    echo -e "${GREEN}✅ WhatsApp服务正常运行${NC}"
else
    echo -e "${RED}❌ WhatsApp服务未响应${NC}"
    exit 1
fi

# 2. 登录获取token
echo -e "\n${YELLOW}2. 登录获取token...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ] && [ "$TOKEN" != "" ]; then
    echo -e "${GREEN}✅ 登录成功${NC}"
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

# 3. 获取WhatsApp账号
echo -e "\n${YELLOW}3. 获取WhatsApp账号...${NC}"
ACCOUNTS_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].id')
ACCOUNT_NAME=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].account_name')
CONNECTION_STATUS=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].connection_status')

if [ "$ACCOUNT_ID" != "null" ] && [ -n "$ACCOUNT_ID" ]; then
    echo -e "${GREEN}✅ 获取账号成功${NC}"
    echo "账号ID: $ACCOUNT_ID"
    echo "账号名称: $ACCOUNT_NAME"
    echo "连接状态: $CONNECTION_STATUS"
else
    echo -e "${RED}❌ 获取账号失败${NC}"
    echo "响应: $ACCOUNTS_RESPONSE"
    exit 1
fi

# 4. 创建群发任务
echo -e "\n${YELLOW}4. 创建群发任务...${NC}"
TASK_DATA='{
  "task_name": "测试群发任务",
  "account_usage": "auto",
  "customer_file": "test_customers.txt",
  "customer_interval": 5,
  "sending_times": 1,
  "sending_times_type": "limited",
  "sending_method": "one_by_one",
  "sentence_interval": 2,
  "number_detection": false,
  "statements": "[{\"type\":\"text\",\"material_name\":\"测试消息\",\"content\":\"这是一条测试消息\",\"file_url\":\"\",\"duration\":0}]"
}'

CREATE_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "$TASK_DATA")

TASK_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.task_id')

if [ "$TASK_ID" != "null" ] && [ -n "$TASK_ID" ]; then
    echo -e "${GREEN}✅ 创建群发任务成功${NC}"
    echo "任务ID: $TASK_ID"
else
    echo -e "${RED}❌ 创建群发任务失败${NC}"
    echo "响应: $CREATE_RESPONSE"
    exit 1
fi

# 5. 获取群发任务列表
echo -e "\n${YELLOW}5. 获取群发任务列表...${NC}"
TASKS_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN")

if echo "$TASKS_RESPONSE" | jq -e '.data.tasks' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 获取群发任务列表成功${NC}"
    TASK_COUNT=$(echo "$TASKS_RESPONSE" | jq '.data.total')
    echo "任务数量: $TASK_COUNT"
else
    echo -e "${RED}❌ 获取群发任务列表失败${NC}"
    echo "响应: $TASKS_RESPONSE"
fi

# 6. 获取群发任务详情
echo -e "\n${YELLOW}6. 获取群发任务详情...${NC}"
DETAIL_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks/$TASK_ID" \
  -H "Authorization: Bearer $TOKEN")

if echo "$DETAIL_RESPONSE" | jq -e '.data.task' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 获取群发任务详情成功${NC}"
    TASK_STATUS=$(echo "$DETAIL_RESPONSE" | jq -r '.data.task.status')
    echo "任务状态: $TASK_STATUS"
else
    echo -e "${RED}❌ 获取群发任务详情失败${NC}"
    echo "响应: $DETAIL_RESPONSE"
fi

# 7. 启动群发任务（如果账号已连接）
echo -e "\n${YELLOW}7. 启动群发任务...${NC}"
if [ "$CONNECTION_STATUS" = "connected" ]; then
    START_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/group-sending/tasks/$TASK_ID/start" \
      -H "Authorization: Bearer $TOKEN")

    if echo "$START_RESPONSE" | jq -e '.code' > /dev/null 2>&1; then
        RESPONSE_CODE=$(echo "$START_RESPONSE" | jq -r '.code')
        if [ "$RESPONSE_CODE" = "200" ]; then
            echo -e "${GREEN}✅ 启动群发任务成功${NC}"
        else
            echo -e "${YELLOW}⚠️ 启动群发任务失败: $(echo "$START_RESPONSE" | jq -r '.message')${NC}"
        fi
    else
        echo -e "${RED}❌ 启动群发任务请求失败${NC}"
        echo "响应: $START_RESPONSE"
    fi
else
    echo -e "${YELLOW}⚠️ 账号未连接，跳过启动测试${NC}"
fi

# 8. 测试结果总结
echo -e "\n${BLUE}8. 测试结果总结...${NC}"
echo -e "${GREEN}✅ 服务状态检查正常${NC}"
echo -e "${GREEN}✅ 登录功能正常${NC}"
echo -e "${GREEN}✅ 账号管理API正常${NC}"
echo -e "${GREEN}✅ 群发任务创建成功${NC}"
echo -e "${GREEN}✅ 群发任务列表API正常${NC}"
echo -e "${GREEN}✅ 群发任务详情API正常${NC}"

if [ "$CONNECTION_STATUS" = "connected" ]; then
    echo -e "${GREEN}✅ 群发任务启动测试完成${NC}"
else
    echo -e "${YELLOW}⚠️ 账号未连接，无法测试启动功能${NC}"
fi

echo -e "\n${BLUE}=== 新群发功能测试完成 ===${NC}"

echo -e "\n${YELLOW}下一步操作:${NC}"
echo "1. 打开浏览器访问: http://localhost:5173"
echo "2. 登录账号: admin/admin123"
echo "3. 进入群发任务管理页面"
echo "4. 测试群发任务的创建、启动、暂停、停止功能"

echo -e "\n${YELLOW}安全提醒:${NC}"
echo "- 使用测试账号进行WhatsApp连接"
echo "- 发送安全的测试消息"
echo "- 避免发送敏感内容"
echo "- 注意发送频率，避免账号被封" 