#!/bin/bash

# 测试账号名称更新功能
# 验证登录成功后账号名称是否正确更新为手机号或昵称

set -e

echo "🚀 开始测试账号名称更新功能..."

# 配置
BACKEND_URL="http://localhost:8081"
TEST_USER="admin"
TEST_PASSWORD="admin123"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取JWT Token
get_token() {
    local response=$(curl -s -X POST "$BACKEND_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASSWORD\"}")
    
    echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4
}

# 获取WhatsApp账号列表
get_whatsapp_accounts() {
    local token="$1"
    
    curl -s -X GET "$BACKEND_URL/api/whatsapp/accounts" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json"
}

# 创建测试账号
create_test_account() {
    local token="$1"
    local account_name="$2"
    
    curl -s -X POST "$BACKEND_URL/api/whatsapp/accounts" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d "{
            \"account_name\": \"$account_name\",
            \"phone_number\": \"**********\",
            \"group_id\": null
        }"
}

# 模拟账号连接成功，更新客户端信息
simulate_account_connection() {
    local token="$1"
    local account_id="$2"
    local phone_number="$3"
    local pushname="$4"
    
    # 模拟Node服务返回的客户端信息
    local client_info="{
        \"wid\": \"${phone_number}@c.us\",
        \"phone_number\": \"$phone_number\",
        \"pushname\": \"$pushname\",
        \"platform\": \"android\",
        \"profile_pic_url\": \"https://example.com/avatar.jpg\"
    }"
    
    # 直接调用更新账号信息的API（模拟连接成功后的更新）
    curl -s -X PUT "$BACKEND_URL/api/whatsapp/accounts/$account_id" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d "{
            \"account_name\": \"$pushname ($phone_number)\",
            \"phone_number\": \"$phone_number\",
            \"nickname\": \"$pushname\",
            \"status\": \"connected\"
        }"
}

# 获取账号详情
get_account_details() {
    local token="$1"
    local account_id="$2"
    
    curl -s -X GET "$BACKEND_URL/api/whatsapp/accounts/$account_id" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json"
}

# 删除测试账号
delete_test_account() {
    local token="$1"
    local account_id="$2"
    
    curl -s -X DELETE "$BACKEND_URL/api/whatsapp/accounts/$account_id" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json"
}

# 主测试流程
main() {
    echo -e "${YELLOW}🔑 获取认证Token...${NC}"
    TOKEN=$(get_token)
    if [ -z "$TOKEN" ]; then
        echo -e "${RED}❌ 获取Token失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Token获取成功${NC}"
    
    echo -e "\n${YELLOW}📋 测试场景1: 创建默认名称的账号${NC}"
    
    # 创建测试账号
    echo "创建名为'新账号'的测试账号..."
    create_response=$(create_test_account "$TOKEN" "新账号")
    account_id=$(echo "$create_response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -z "$account_id" ]; then
        echo -e "${RED}❌ 创建测试账号失败${NC}"
        echo "$create_response"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 测试账号创建成功，ID: $account_id${NC}"
    
    # 模拟账号连接成功
    echo "模拟账号连接成功，更新客户端信息..."
    simulate_account_connection "$TOKEN" "$account_id" "***********" "张三"
    
    # 检查账号名称是否更新
    echo "检查账号名称是否正确更新..."
    account_details=$(get_account_details "$TOKEN" "$account_id")
    account_name=$(echo "$account_details" | grep -o '"account_name":"[^"]*"' | cut -d'"' -f4)
    
    echo "更新后的账号名称: $account_name"
    
    if [[ "$account_name" == *"张三"* ]] && [[ "$account_name" == *"***********"* ]]; then
        echo -e "${GREEN}✅ 账号名称更新正确: $account_name${NC}"
    elif [[ "$account_name" == "***********" ]]; then
        echo -e "${GREEN}✅ 账号名称更新为手机号: $account_name${NC}"
    else
        echo -e "${RED}❌ 账号名称更新失败，当前名称: $account_name${NC}"
    fi
    
    echo -e "\n${YELLOW}📋 测试场景2: 创建自定义名称的账号${NC}"
    
    # 创建自定义名称的测试账号
    echo "创建名为'我的WhatsApp'的测试账号..."
    create_response2=$(create_test_account "$TOKEN" "我的WhatsApp")
    account_id2=$(echo "$create_response2" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -z "$account_id2" ]; then
        echo -e "${RED}❌ 创建第二个测试账号失败${NC}"
    else
        echo -e "${GREEN}✅ 第二个测试账号创建成功，ID: $account_id2${NC}"
        
        # 模拟账号连接成功
        echo "模拟第二个账号连接成功..."
        simulate_account_connection "$TOKEN" "$account_id2" "***********" "李四"
        
        # 检查账号名称是否保持自定义名称
        account_details2=$(get_account_details "$TOKEN" "$account_id2")
        account_name2=$(echo "$account_details2" | grep -o '"account_name":"[^"]*"' | cut -d'"' -f4)
        
        echo "第二个账号的名称: $account_name2"
        
        if [[ "$account_name2" == "我的WhatsApp" ]]; then
            echo -e "${GREEN}✅ 自定义账号名称保持不变: $account_name2${NC}"
        elif [[ "$account_name2" == *"李四"* ]] && [[ "$account_name2" == *"***********"* ]]; then
            echo -e "${YELLOW}⚠️ 自定义名称被更新为: $account_name2${NC}"
        else
            echo -e "${RED}❌ 账号名称异常: $account_name2${NC}"
        fi
    fi
    
    # 清理测试数据
    echo -e "\n${YELLOW}🧹 清理测试数据...${NC}"
    if [ -n "$account_id" ]; then
        delete_test_account "$TOKEN" "$account_id"
        echo "已删除测试账号 $account_id"
    fi
    if [ -n "$account_id2" ]; then
        delete_test_account "$TOKEN" "$account_id2"
        echo "已删除测试账号 $account_id2"
    fi
    
    echo -e "\n${GREEN}🎉 账号名称更新功能测试完成！${NC}"
}

# 运行测试
main "$@"