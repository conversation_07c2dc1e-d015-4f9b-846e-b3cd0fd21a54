#!/bin/bash

echo "🧹 测试孤立Session文件清理功能"
echo "================================"

echo "1. 检查当前状态..."
echo ""

echo "📁 文件系统中的Session文件夹:"
ls -la whatsapp-node-service/sessions/

echo ""
echo "💾 数据库中的Session记录:"
curl -s http://localhost:8081/api/whatsapp/sessions/internal | jq '.data[] | {session_id, status, phone_number}'

echo ""
echo "🧠 内存中的Session:"
curl -s http://localhost:3000/api/whatsapp/sessions | jq '.data[] | {session_id, status, phone_number}'

echo ""
echo "2. 触发清理任务..."
echo "等待下一次定时清理任务执行（最多5分钟）..."
echo "或者重启Node服务来立即触发清理"

echo ""
echo "3. 清理逻辑说明:"
echo "✅ 保留条件: 数据库中存在 OR 内存中存在"
echo "🗑️ 清理条件: 数据库中不存在 AND 内存中不存在"

echo ""
echo "预期结果:"
echo "- 只保留活跃的Session文件夹"
echo "- 清理孤立的Session文件夹"

echo ""
echo "测试完成！请观察Node服务日志中的清理信息。"