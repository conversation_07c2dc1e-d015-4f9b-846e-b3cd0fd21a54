#!/bin/bash

echo "🧪 测试Session恢复修复效果"
echo "=========================="

# 检查服务状态
echo "1. 检查服务状态..."
echo "Go后端: $(curl -s http://localhost:8081/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
echo "Node服务: $(curl -s http://localhost:3000/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"

# 获取Session状态
echo ""
echo "2. 获取Session状态..."
sessions=$(curl -s http://localhost:8081/api/whatsapp/sessions/internal)
session_count=$(echo $sessions | jq '.data.data | length' 2>/dev/null || echo "0")
echo "Session数量: $session_count"

if [ "$session_count" -gt 0 ]; then
  echo "Session详情:"
  echo $sessions | jq '.data.data[0] | {session_id, status, phone_number, is_session_valid}' 2>/dev/null
fi

# 测试Node服务Session列表
echo ""
echo "3. 测试Node服务Session列表..."
node_sessions=$(curl -s http://localhost:3000/api/whatsapp/sessions)
node_count=$(echo $node_sessions | jq '.data | length' 2>/dev/null || echo "0")
echo "Node服务Session数量: $node_count"

echo ""
echo "✅ 测试完成"
echo ""
echo "📝 修复说明："
echo "- 修复了Session恢复时无限等待ready事件的问题"
echo "- 对于已认证的客户端，直接解析而不等待ready事件"
echo "- 改进了认证状态检查逻辑" 