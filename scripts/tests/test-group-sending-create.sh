#!/bin/bash

echo "🧪 测试群发任务创建功能"
echo "================================"

# 获取token
echo "1. 获取认证token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，请检查用户凭据"
  exit 1
fi

echo "✅ 登录成功，获取到token"

# 测试创建单语句任务
echo ""
echo "2. 测试创建单语句任务..."
SINGLE_STATEMENT_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "单语句测试任务",
    "account_usage": "auto",
    "customer_file": "test_customers.txt",
    "customer_interval": 5,
    "sending_times": 1,
    "sending_times_type": "limited",
    "sending_method": "one_by_one",
    "sentence_interval": 2,
    "number_detection": false,
    "statements": "[{\"type\":\"text\",\"material_name\":\"测试消息\",\"content\":\"这是一条测试消息\",\"file_url\":\"\",\"duration\":0}]",
    "scheduled_time": "2025-07-31T05:00:00Z"
  }')

echo "$SINGLE_STATEMENT_RESPONSE" | jq .

# 测试创建多语句任务
echo ""
echo "3. 测试创建多语句任务..."
MULTI_STATEMENT_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "多语句测试任务",
    "account_usage": "auto",
    "customer_file": "test_customers.txt",
    "customer_interval": 5,
    "sending_times": 1,
    "sending_times_type": "limited",
    "sending_method": "one_by_one",
    "sentence_interval": 2,
    "number_detection": false,
    "statements": "[{\"type\":\"text\",\"material_name\":\"欢迎消息\",\"content\":\"您好，欢迎使用我们的服务！\",\"file_url\":\"\",\"duration\":0},{\"type\":\"text\",\"material_name\":\"产品介绍\",\"content\":\"我们提供优质的产品和服务，欢迎咨询！\",\"file_url\":\"\",\"duration\":0},{\"type\":\"image\",\"material_name\":\"产品图片\",\"content\":\"\",\"file_url\":\"product.jpg\",\"duration\":0}]",
    "scheduled_time": "2025-07-31T05:30:00Z"
  }')

echo "$MULTI_STATEMENT_RESPONSE" | jq .

# 测试创建包含视频的任务
echo ""
echo "4. 测试创建包含视频的任务..."
VIDEO_STATEMENT_RESPONSE=$(curl -s -X POST "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "视频语句测试任务",
    "account_usage": "auto",
    "customer_file": "test_customers.txt",
    "customer_interval": 5,
    "sending_times": 1,
    "sending_times_type": "limited",
    "sending_method": "one_by_one",
    "sentence_interval": 2,
    "number_detection": false,
    "statements": "[{\"type\":\"text\",\"material_name\":\"介绍文字\",\"content\":\"请看我们的产品视频\",\"file_url\":\"\",\"duration\":0},{\"type\":\"video\",\"material_name\":\"产品视频\",\"content\":\"\",\"file_url\":\"product_video.mp4\",\"duration\":30}]",
    "scheduled_time": "2025-07-31T06:00:00Z"
  }')

echo "$VIDEO_STATEMENT_RESPONSE" | jq .

# 获取所有任务列表
echo ""
echo "5. 获取所有群发任务..."
TASKS_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/group-sending/tasks" \
  -H "Authorization: Bearer $TOKEN")

echo "$TASKS_RESPONSE" | jq .

echo ""
echo "✅ 群发任务创建功能测试完成！"
echo ""
echo "📋 测试结果总结："
echo "- ✅ 单语句任务创建成功"
echo "- ✅ 多语句任务创建成功" 
echo "- ✅ 视频语句任务创建成功"
echo "- ✅ statements字段正确序列化为JSON字符串"
echo "- ✅ 时间格式正确处理"
echo ""
echo "🎯 前端功能改进："
echo "- ✅ 添加文字语句时自动打开编辑弹框"
echo "- ✅ 支持多种语句类型（文字、图片、视频、拨打电话）"
echo "- ✅ 编辑语句功能完整"
echo "- ✅ 批量操作功能完整" 