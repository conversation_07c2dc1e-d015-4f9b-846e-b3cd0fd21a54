#!/bin/bash

echo "🔧 测试客户文件上传和解析功能"
echo "================================"

# 获取JWT Token
echo "1. 获取Token..."
TOKEN=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 获取Token失败"
    exit 1
fi

echo "✅ Token获取成功"

echo ""
echo "2. 上传客户文件..."
UPLOAD_RESPONSE=$(curl -s -X POST http://localhost:8081/api/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@phonenum.txt" \
  -F "category=customer_list")

echo "上传响应:"
echo "$UPLOAD_RESPONSE" | jq '.'

# 提取文件路径
FILE_PATH=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.file_path // empty')

if [ -z "$FILE_PATH" ]; then
    echo "❌ 文件上传失败"
    exit 1
fi

echo "✅ 文件上传成功: $FILE_PATH"

echo ""
echo "3. 创建群发任务测试文件解析..."
CREATE_RESPONSE=$(curl -s -X POST http://localhost:8081/api/group-sending/tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{
    \"task_name\": \"文件解析测试任务\",
    \"account_usage\": \"auto\",
    \"customer_file\": \"$FILE_PATH\",
    \"customer_interval\": 60,
    \"sending_times\": 1,
    \"sending_times_type\": \"limited\",
    \"sending_method\": \"one_by_one\",
    \"sentence_interval\": 1,
    \"number_detection\": false,
    \"statements\": \"[{\\\"type\\\":\\\"text\\\",\\\"material_name\\\":\\\"测试消息\\\",\\\"content\\\":\\\"这是一条测试消息\\\",\\\"file_url\\\":\\\"\\\",\\\"duration\\\":0}]\"
  }")

echo "创建任务响应:"
echo "$CREATE_RESPONSE" | jq '.'

# 检查任务创建是否成功
TASK_CODE=$(echo "$CREATE_RESPONSE" | jq -r '.code // 0')
CUSTOMER_COUNT=$(echo "$CREATE_RESPONSE" | jq -r '.data.customer_count // 0')

if [ "$TASK_CODE" = "200" ]; then
    echo "✅ 任务创建成功"
    echo "✅ 解析到客户数量: $CUSTOMER_COUNT"
    
    if [ "$CUSTOMER_COUNT" -gt "0" ]; then
        echo "✅ 文件解析功能正常工作"
    else
        echo "❌ 文件解析失败，客户数量为0"
    fi
else
    echo "❌ 任务创建失败"
fi

echo ""
echo "4. 检查上传的文件内容..."
if [ -f "$FILE_PATH" ]; then
    echo "✅ 文件存在: $FILE_PATH"
    echo "文件内容:"
    cat "$FILE_PATH"
else
    echo "❌ 文件不存在: $FILE_PATH"
fi

echo ""
echo "测试完成！"