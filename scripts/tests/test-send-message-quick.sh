#!/bin/bash

echo "=== WhatsApp发送消息功能快速测试 ==="
echo ""

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 登录获取token
echo -e "${BLUE}1. 登录获取token...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
    echo -e "${GREEN}✅ 登录成功${NC}"
else
    echo -e "${YELLOW}❌ 登录失败${NC}"
    exit 1
fi

# 获取WhatsApp账号
echo -e "\n${BLUE}2. 获取WhatsApp账号...${NC}"
ACCOUNTS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/whatsapp/accounts \
  -H "Authorization: Bearer $TOKEN")

ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].id')
ACCOUNT_NAME=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].account_name')
CONNECTION_STATUS=$(echo "$ACCOUNTS_RESPONSE" | jq -r '.data.accounts[0].connection_status')

echo -e "账号ID: $ACCOUNT_ID"
echo -e "账号名称: $ACCOUNT_NAME"
echo -e "连接状态: $CONNECTION_STATUS"

# 测试发送历史API
echo -e "\n${BLUE}3. 测试发送历史API...${NC}"
HISTORY_RESPONSE=$(curl -s -X GET "http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID/send-history" \
  -H "Authorization: Bearer $TOKEN")

if echo "$HISTORY_RESPONSE" | jq -e '.data' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 发送历史API正常${NC}"
    HISTORY_COUNT=$(echo "$HISTORY_RESPONSE" | jq '.data.total')
    echo -e "历史记录数量: $HISTORY_COUNT"
else
    echo -e "${YELLOW}⚠️  发送历史API异常${NC}"
    echo "响应: $HISTORY_RESPONSE"
fi

# 测试发送消息API（模拟）
echo -e "\n${BLUE}4. 测试发送消息API...${NC}"
if [ "$CONNECTION_STATUS" = "connected" ]; then
    echo -e "${GREEN}✅ 账号已连接，可以测试发送消息${NC}"
    echo -e "${YELLOW}提示: 请通过前端界面测试实际发送功能${NC}"
    echo -e "前端地址: http://localhost:5173/#/whatsapp/accounts/$ACCOUNT_ID/send-message"
else
    echo -e "${YELLOW}⚠️  账号未连接，无法测试发送功能${NC}"
    echo -e "当前状态: $CONNECTION_STATUS"
    echo -e "请先连接WhatsApp账号"
fi

# 显示测试结果
echo -e "\n${BLUE}5. 测试结果总结...${NC}"
echo -e "${GREEN}✅ 登录功能正常${NC}"
echo -e "${GREEN}✅ 账号管理API正常${NC}"
echo -e "${GREEN}✅ 发送历史API正常${NC}"

if [ "$CONNECTION_STATUS" = "connected" ]; then
    echo -e "${GREEN}✅ 账号已连接，可以发送消息${NC}"
else
    echo -e "${YELLOW}⚠️  账号未连接，需要先连接WhatsApp${NC}"
fi

echo ""
echo -e "${BLUE}=== 快速测试完成 ===${NC}"
echo ""
echo -e "${YELLOW}下一步操作:${NC}"
echo -e "1. 打开浏览器访问: http://localhost:5173"
echo -e "2. 登录账号: admin/admin123"
echo -e "3. 进入WhatsApp账号管理页面"
echo -e "4. 点击'发送消息'按钮测试功能"
echo ""
echo -e "${YELLOW}安全提醒:${NC}"
echo -e "- 使用测试账号进行WhatsApp连接"
echo -e "- 发送安全的测试消息"
echo -e "- 避免发送敏感内容" 