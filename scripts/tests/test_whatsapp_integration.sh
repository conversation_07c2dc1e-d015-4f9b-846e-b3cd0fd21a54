#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置
GO_API_URL="http://localhost:8081"
NODE_API_URL="http://localhost:3000"

echo -e "${YELLOW}开始测试WhatsApp集成系统...${NC}"

# 1. 检查服务状态
echo -e "${YELLOW}1. 检查服务状态...${NC}"

# 检查Go后端
GO_HEALTH=$(curl --noproxy localhost -s "$GO_API_URL/health")
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✓ Go后端运行正常${NC}"
else
    echo -e "${RED}✗ Go后端连接失败${NC}"
    exit 1
fi

# 检查Node服务
NODE_HEALTH=$(curl --noproxy localhost -s "$NODE_API_URL/health")
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✓ Node服务运行正常${NC}"
else
    echo -e "${RED}✗ Node服务连接失败${NC}"
    exit 1
fi

# 2. 测试登录获取token
echo -e "${YELLOW}2. 获取认证token...${NC}"

LOGIN_RESPONSE=$(curl --noproxy localhost -s -X POST "$GO_API_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

echo "登录响应: $LOGIN_RESPONSE"

TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | head -1 | cut -d'"' -f4)

echo "提取的token: $TOKEN"

if [[ -n "$TOKEN" ]]; then
    echo -e "${GREEN}✓ 登录成功，获取到token${NC}"
else
    echo -e "${RED}✗ 登录失败${NC}"
    exit 1
fi

# 3. 测试创建WhatsApp Session
echo -e "${YELLOW}3. 测试创建WhatsApp Session...${NC}"

CREATE_SESSION_RESPONSE=$(curl --noproxy localhost -s -X POST "$GO_API_URL/api/whatsapp/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "phone_number": "*************",
    "account_name": "测试账号",
    "group_id": null
  }')

echo "创建Session响应: $CREATE_SESSION_RESPONSE"

# 4. 测试Node服务API
echo -e "${YELLOW}4. 测试Node服务API...${NC}"

# 测试Node服务的action API
NODE_ACTION_RESPONSE=$(curl --noproxy localhost -s -X POST "$NODE_API_URL/api/whatsapp/action" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-001",
    "phone_number": "*************",
    "tenant_id": 1,
    "action": "create",
    "data": {
      "account_name": "测试账号"
    }
  }')

echo "Node服务响应: $NODE_ACTION_RESPONSE"

# 5. 测试获取Session列表
echo -e "${YELLOW}5. 测试获取Session列表...${NC}"

SESSIONS_RESPONSE=$(curl --noproxy localhost -s "$NODE_API_URL/api/whatsapp/sessions")
echo "Session列表: $SESSIONS_RESPONSE"

# 6. 测试清理功能
echo -e "${YELLOW}6. 测试清理功能...${NC}"

CLEANUP_RESPONSE=$(curl --noproxy localhost -s -X POST "$NODE_API_URL/api/whatsapp/cleanup-all")
echo "清理响应: $CLEANUP_RESPONSE"

echo -e "${GREEN}测试完成！${NC}"
echo -e "${YELLOW}Go后端: $GO_API_URL${NC}"
echo -e "${YELLOW}Node服务: $NODE_API_URL${NC}" 