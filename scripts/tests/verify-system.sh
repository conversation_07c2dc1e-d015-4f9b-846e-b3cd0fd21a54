#!/bin/bash

echo "=== Hive SaaS 群发功能系统验证 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务状态
echo -e "${BLUE}1. 检查服务状态...${NC}"

echo -e "\n${YELLOW}后端服务 (端口8081):${NC}"
if curl -s http://localhost:8081/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务正常运行${NC}"
else
    echo -e "${RED}❌ 后端服务未响应${NC}"
fi

echo -e "\n${YELLOW}WhatsApp服务 (端口3000):${NC}"
if curl -s http://localhost:3000/health > /dev/null; then
    echo -e "${GREEN}✅ WhatsApp服务正常运行${NC}"
else
    echo -e "${RED}❌ WhatsApp服务未响应${NC}"
fi

echo -e "\n${YELLOW}前端服务 (端口5173):${NC}"
if curl -s http://localhost:5173 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务正常运行${NC}"
else
    echo -e "${RED}❌ 前端服务未响应${NC}"
fi

# 测试API功能
echo -e "\n${BLUE}2. 测试API功能...${NC}"

echo -e "\n${YELLOW}登录测试:${NC}"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token' 2>/dev/null)

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ] && [ "$TOKEN" != "" ]; then
    echo -e "${GREEN}✅ 登录成功${NC}"
    echo -e "Token: ${TOKEN:0:50}..."
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
    echo "Token: $TOKEN"
    exit 1
fi

echo -e "\n${YELLOW}群发任务API测试:${NC}"
TASKS_RESPONSE=$(curl -s -X GET http://localhost:8081/api/group-sending/tasks \
  -H "Authorization: Bearer $TOKEN")

if echo "$TASKS_RESPONSE" | jq -e '.data.tasks' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 群发任务API正常${NC}"
    TASK_COUNT=$(echo "$TASKS_RESPONSE" | jq '.data.total')
    echo -e "当前任务数量: $TASK_COUNT"
else
    echo -e "${RED}❌ 群发任务API异常${NC}"
    echo "响应: $TASKS_RESPONSE"
fi

# 创建测试任务
echo -e "\n${YELLOW}创建测试任务:${NC}"
CREATE_RESPONSE=$(curl -s -X POST http://localhost:8081/api/group-sending/tasks \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "系统验证测试任务",
    "account_usage": "auto",
    "customer_file": "test-data/customers.txt",
    "customer_interval": 60,
    "sending_times": 1,
    "sending_times_type": "limited",
    "sending_method": "one_by_one",
    "sentence_interval": 1,
    "number_detection": false,
    "statements": "[{\"type\":\"text\",\"content\":\"您好，这是一条测试消息，请忽略。\",\"order\":1}]"
  }')

TASK_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.id' 2>/dev/null)

if [ "$TASK_ID" != "null" ] && [ -n "$TASK_ID" ]; then
    echo -e "${GREEN}✅ 测试任务创建成功 (ID: $TASK_ID)${NC}"
else
    echo -e "${RED}❌ 测试任务创建失败${NC}"
    echo "响应: $CREATE_RESPONSE"
fi

# 检查数据库
echo -e "\n${BLUE}3. 检查数据库...${NC}"

if [ -f "storage/database/hive.db" ]; then
    echo -e "${GREEN}✅ 数据库文件存在${NC}"
    DB_SIZE=$(ls -lh storage/database/hive.db | awk '{print $5}')
    echo -e "数据库大小: $DB_SIZE"
else
    echo -e "${RED}❌ 数据库文件不存在${NC}"
fi

# 检查测试数据
echo -e "\n${BLUE}4. 检查测试数据...${NC}"

if [ -f "test-data/customers.txt" ]; then
    echo -e "${GREEN}✅ 测试客户文件存在${NC}"
    CUSTOMER_COUNT=$(wc -l < test-data/customers.txt)
    echo -e "客户数量: $CUSTOMER_COUNT"
else
    echo -e "${RED}❌ 测试客户文件不存在${NC}"
fi

# 系统信息
echo -e "\n${BLUE}5. 系统信息...${NC}"

echo -e "${YELLOW}服务端口:${NC}"
echo -e "  后端服务: http://localhost:8081"
echo -e "  WhatsApp服务: http://localhost:3000"
echo -e "  前端服务: http://localhost:5173"

echo -e "\n${YELLOW}测试账号:${NC}"
echo -e "  用户名: admin"
echo -e "  密码: admin123"

echo -e "\n${YELLOW}访问地址:${NC}"
echo -e "  前端界面: http://localhost:5173"
echo -e "  群发功能: http://localhost:5173/#/marketing/group-sending"

echo -e "\n${BLUE}6. 验证结果总结...${NC}"

# 检查所有关键组件
COMPONENTS=(
    "后端服务:8081"
    "WhatsApp服务:3000"
    "前端服务:5173"
    "数据库文件:storage/database/hive.db"
    "测试数据:test-data/customers.txt"
)

ALL_OK=true

for component in "${COMPONENTS[@]}"; do
    name=$(echo $component | cut -d: -f1)
    port=$(echo $component | cut -d: -f2)
    
    if [[ $port =~ ^[0-9]+$ ]]; then
        # 检查端口
        if curl -s http://localhost:$port/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name 正常${NC}"
        else
            echo -e "${RED}❌ $name 异常${NC}"
            ALL_OK=false
        fi
    else
        # 检查文件
        if [ -f "$port" ]; then
            echo -e "${GREEN}✅ $name 存在${NC}"
        else
            echo -e "${RED}❌ $name 不存在${NC}"
            ALL_OK=false
        fi
    fi
done

echo ""
if [ "$ALL_OK" = true ]; then
    echo -e "${GREEN}🎉 系统验证完成！所有组件正常运行。${NC}"
    echo ""
    echo -e "${BLUE}下一步操作:${NC}"
    echo -e "1. 打开浏览器访问: http://localhost:5173"
    echo -e "2. 使用测试账号登录: admin/admin123"
    echo -e "3. 进入'营销互动' -> '陌生人群发'"
    echo -e "4. 创建群发任务进行测试"
    echo ""
    echo -e "${YELLOW}安全提醒:${NC}"
    echo -e "- 使用测试账号进行WhatsApp连接"
    echo -e "- 使用预设的安全测试消息"
    echo -e "- 设置合理的发送间隔 (60秒以上)"
    echo -e "- 避免发送敏感或商业推广内容"
else
    echo -e "${RED}⚠️  系统验证发现问题，请检查上述异常组件。${NC}"
fi

echo ""
echo -e "${BLUE}=== 验证完成 ===${NC}" 