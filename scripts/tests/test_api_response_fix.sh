#!/bin/bash

echo "🧪 测试API响应格式修复效果"
echo "=========================="

# 检查服务状态
echo "1. 检查服务状态..."
echo "Go后端: $(curl -s http://localhost:8081/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
echo "Node服务: $(curl -s http://localhost:3000/health > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
echo "前端: $(curl -s http://localhost:5173 > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"

# 测试API响应格式
echo ""
echo "2. 测试API响应格式..."
echo "健康检查响应:"
curl -s http://localhost:8081/health

echo ""
echo "Node服务健康检查响应:"
curl -s http://localhost:3000/health

echo ""
echo "✅ 测试完成"
echo ""
echo "📝 修复说明："
echo "- 修复了前端检查result.success的问题"
echo "- 改为检查result.code === 200"
echo "- 适配了Go后端的ApiResponse格式"
echo "- 现在前端应该能正确显示账户信息了" 