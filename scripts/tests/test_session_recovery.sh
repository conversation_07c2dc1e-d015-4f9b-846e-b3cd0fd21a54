#!/bin/bash

# Session恢复机制测试脚本
# 测试Node服务重启后的session恢复功能

set -e

echo "🚀 开始测试Session恢复机制..."

# 配置
BACKEND_URL="http://localhost:8081"
NODE_URL="http://localhost:3000"
TEST_USER="admin"
TEST_PASSWORD="admin123"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_case() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "\n${BLUE}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ 失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 获取JWT Token
get_token() {
    local response=$(curl -s -X POST "$BACKEND_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASSWORD\"}")
    
    echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4
}

# 检查服务状态
check_service_status() {
    local url="$1"
    local service_name="$2"
    
    if curl -s -f "$url/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name 服务运行正常${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name 服务未运行${NC}"
        return 1
    fi
}

# 获取Node服务状态
get_node_service_status() {
    local token="$1"
    
    curl -s -X GET "$BACKEND_URL/api/whatsapp/node-service/status" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json"
}

# 获取WhatsApp账号列表
get_whatsapp_accounts() {
    local token="$1"
    
    curl -s -X GET "$BACKEND_URL/api/whatsapp/accounts" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json"
}

# 测试按需恢复
test_on_demand_restore() {
    local session_id="$1"
    
    curl -s -X POST "$NODE_URL/api/whatsapp/session/$session_id/restore" \
        -H "Content-Type: application/json" \
        -d "{}"
}

# 主测试流程
main() {
    echo -e "${YELLOW}🔍 检查服务状态...${NC}"
    
    # 检查后端服务
    if ! check_service_status "$BACKEND_URL" "Go后端"; then
        echo -e "${RED}请先启动Go后端服务${NC}"
        exit 1
    fi
    
    # 检查Node服务
    if ! check_service_status "$NODE_URL" "Node.js"; then
        echo -e "${RED}请先启动Node.js服务${NC}"
        exit 1
    fi
    
    echo -e "\n${YELLOW}🔑 获取认证Token...${NC}"
    TOKEN=$(get_token)
    if [ -z "$TOKEN" ]; then
        echo -e "${RED}❌ 获取Token失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Token获取成功${NC}"
    
    # 测试1: 检查Node服务状态API
    test_case "检查Node服务状态API" "
        response=\$(get_node_service_status '$TOKEN')
        echo \"\$response\" | grep -q '\"success\":true' && 
        echo \"\$response\" | grep -q '\"services\"'
    "
    
    # 测试2: 获取WhatsApp账号列表
    test_case "获取WhatsApp账号列表" "
        response=\$(get_whatsapp_accounts '$TOKEN')
        echo \"\$response\" | grep -q '\"success\":true'
    "
    
    # 测试3: 检查Node服务心跳机制
    test_case "检查Node服务心跳机制" "
        # 等待心跳更新
        sleep 35
        response=\$(get_node_service_status '$TOKEN')
        echo \"\$response\" | grep -q '\"status\":\"running\"'
    "
    
    # 测试4: 模拟Node服务重启通知
    test_case "模拟Node服务重启通知" "
        response=\$(curl -s -X POST '$BACKEND_URL/api/whatsapp/node-service/restart' \
            -H 'Content-Type: application/json' \
            -d '{
                \"service_id\": \"test-node-service\",
                \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
                \"active_sessions\": [\"test-session-1\", \"test-session-2\"],
                \"process_pid\": 12345
            }')
        echo \"\$response\" | grep -q '\"success\":true'
    "
    
    # 测试5: 检查Node服务状态更新
    test_case "检查Node服务状态更新" "
        response=\$(get_node_service_status '$TOKEN')
        echo \"\$response\" | grep -q 'test-node-service'
    "
    
    # 测试6: 测试按需恢复API（使用不存在的session）
    test_case "测试按需恢复API（不存在的session）" "
        response=\$(test_on_demand_restore 'non-existent-session')
        echo \"\$response\" | grep -q '\"success\":false'
    "
    
    # 测试7: 检查健康检查机制
    test_case "检查健康检查机制运行" "
        # 检查Node服务日志中是否有健康检查信息
        # 这里简化为检查服务是否正常响应
        curl -s -f '$NODE_URL/api/whatsapp/service/status' > /dev/null
    "
    
    # 显示测试结果
    echo -e "\n${YELLOW}📊 测试结果统计:${NC}"
    echo -e "总测试数: $TOTAL_TESTS"
    echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
    echo -e "${RED}失败: $FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 所有测试通过！Session恢复机制工作正常。${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ 有 $FAILED_TESTS 个测试失败。${NC}"
        exit 1
    fi
}

# 运行测试
main "$@"