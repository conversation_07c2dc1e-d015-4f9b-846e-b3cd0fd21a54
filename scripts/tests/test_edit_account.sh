#!/bin/bash

echo "✏️ 测试编辑账号功能"
echo "=================="

# 测试更新账号信息
echo "1. 测试更新账号信息..."
update_response=$(curl -s -X PUT http://localhost:8081/api/whatsapp/accounts/1 \
  -H "Authorization: Bearer test-token" \
  -H "Content-Type: application/json" \
  -d '{
    "remark": "测试备注 - $(date)",
    "group_id": 1,
    "account_status": "normal"
  }')

echo "更新响应: $update_response"

# 检查响应
if echo "$update_response" | grep -q '"code":200'; then
  echo ""
  echo "✅ 编辑功能测试成功！"
  echo "📝 备注已更新"
  echo "📁 分组已更新"
  echo "📊 状态已更新"
else
  echo ""
  echo "❌ 编辑功能测试失败"
  echo "错误: $(echo "$update_response" | jq -r '.message' 2>/dev/null || echo '未知错误')"
fi

echo ""
echo "✅ 测试完成" 