#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}测试WhatsApp账号删除流程...${NC}"

# 1. 登录获取token
echo -e "${YELLOW}1. 登录获取token...${NC}"

LOGIN_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}')

TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -n "$TOKEN" ]]; then
    echo -e "${GREEN}✓ 登录成功${NC}"
else
    echo -e "${RED}✗ 登录失败${NC}"
    exit 1
fi

# 2. 创建新的Session
echo -e "${YELLOW}2. 创建新的Session...${NC}"

CREATE_SESSION_RESPONSE=$(curl --noproxy localhost -s -X POST "http://localhost:8081/api/whatsapp/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "phone_number": "*************",
    "account_name": "测试删除账号",
    "group_id": null
  }')

echo "创建Session响应: $CREATE_SESSION_RESPONSE"

# 3. 获取账号列表
echo -e "${YELLOW}3. 获取账号列表...${NC}"

ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

ACCOUNT_ID=$(echo "$ACCOUNTS_RESPONSE" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)

if [[ -n "$ACCOUNT_ID" ]]; then
    echo -e "${GREEN}✓ 找到账号ID: $ACCOUNT_ID${NC}"
else
    echo -e "${RED}✗ 未找到账号${NC}"
    exit 1
fi

# 4. 检查Session文件
echo -e "${YELLOW}4. 检查Session文件...${NC}"

ls -la whatsapp-node-service/sessions/

# 5. 删除账号
echo -e "${YELLOW}5. 删除账号...${NC}"

DELETE_RESPONSE=$(curl --noproxy localhost -s -X DELETE "http://localhost:8081/api/whatsapp/accounts/$ACCOUNT_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "删除响应: $DELETE_RESPONSE"

# 6. 验证删除结果
echo -e "${YELLOW}6. 验证删除结果...${NC}"

FINAL_ACCOUNTS_RESPONSE=$(curl --noproxy localhost -s -X GET "http://localhost:8081/api/whatsapp/accounts" \
  -H "Authorization: Bearer $TOKEN")

echo "最终账号列表: $FINAL_ACCOUNTS_RESPONSE"

# 7. 检查Session文件是否被清理
echo -e "${YELLOW}7. 检查Session文件是否被清理...${NC}"

ls -la whatsapp-node-service/sessions/

echo -e "${GREEN}测试完成！${NC}" 