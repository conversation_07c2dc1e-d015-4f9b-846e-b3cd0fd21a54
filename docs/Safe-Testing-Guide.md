# 群发功能安全测试指南

## 测试环境设置

### 1. 测试账号准备
- 准备2-3个WhatsApp测试账号
- 确保账号之间可以互相发送消息
- 避免使用主要业务账号进行测试

### 2. 安全测试消息
系统内置的安全测试消息：
```
- "您好，这是一条测试消息，请忽略。"
- "测试消息：系统正在验证功能。"
- "这是一条自动发送的测试消息。"
- "功能测试中，请勿回复。"
- "系统消息：功能验证完成。"
```

### 3. 测试手机号
使用测试文件 `test-data/customers.txt` 中的手机号：
```
13800138000
13800138001
13800138002
...
```

## 测试步骤

### 1. 创建测试任务
1. 登录系统，进入"营销互动" -> "陌生人群发"
2. 点击"新建任务"
3. 填写任务信息：
   - 任务名称：测试任务-001
   - 使用账号：选择已连接的测试账号
   - 客户资源：上传测试客户文件
   - 客户间隔：60秒（避免发送过快）
   - 发送次数：1次/天
   - 发送方式：逐条发送
   - 群发语句：使用系统默认的安全测试消息

### 2. 执行测试
1. 启动任务前，确保目标手机号已添加到WhatsApp联系人
2. 点击"启动任务"
3. 监控任务执行状态和日志
4. 观察消息发送情况

### 3. 验证结果
1. 检查目标手机是否收到测试消息
2. 查看发送日志和错误信息
3. 确认任务状态更新正确

## 安全注意事项

### 1. 消息内容安全
- ✅ 使用系统预设的安全测试消息
- ✅ 避免发送营销、广告类内容
- ✅ 消息内容明确标注为测试
- ❌ 不要发送敏感、政治、商业推广内容

### 2. 发送频率控制
- ✅ 设置合理的客户间隔（建议60秒以上）
- ✅ 限制每日发送次数
- ✅ 避免短时间内大量发送
- ❌ 不要设置过短的间隔时间

### 3. 账号安全
- ✅ 使用专门的测试账号
- ✅ 定期检查账号状态
- ✅ 监控发送成功率
- ❌ 不要使用主要业务账号测试

### 4. 数据安全
- ✅ 使用测试数据文件
- ✅ 避免使用真实客户数据
- ✅ 测试完成后清理测试数据
- ❌ 不要在生产环境测试

## 测试场景

### 场景1：基础功能测试
- 目标：验证消息发送功能
- 步骤：创建简单任务，发送1-2条消息
- 预期：消息成功发送，状态正确更新

### 场景2：批量发送测试
- 目标：验证批量发送功能
- 步骤：创建包含10个手机号的任务
- 预期：按间隔逐个发送，进度正确显示

### 场景3：任务控制测试
- 目标：验证任务控制功能
- 步骤：启动任务后暂停、恢复、终止
- 预期：任务状态正确响应控制命令

### 场景4：错误处理测试
- 目标：验证错误处理机制
- 步骤：使用无效手机号或断开网络
- 预期：错误被正确记录和处理

## 监控指标

### 1. 发送成功率
- 目标：> 90%
- 监控：发送日志中的成功/失败比例

### 2. 任务执行时间
- 目标：符合设定的间隔时间
- 监控：实际发送间隔与设定值的偏差

### 3. 系统稳定性
- 目标：无崩溃或异常
- 监控：系统日志和错误信息

### 4. 账号状态
- 目标：账号保持正常状态
- 监控：WhatsApp连接状态和消息发送能力

## 故障排除

### 1. 消息发送失败
- 检查目标手机号格式
- 确认联系人已添加
- 验证账号连接状态

### 2. 任务无法启动
- 检查WhatsApp服务状态
- 验证账号认证状态
- 确认任务配置正确

### 3. 发送频率异常
- 检查客户间隔设置
- 验证系统时间同步
- 查看任务执行日志

## 测试报告模板

```
测试日期：YYYY-MM-DD
测试人员：[姓名]
测试环境：[开发/测试/预生产]

### 测试结果
- 基础功能：✅/❌
- 批量发送：✅/❌
- 任务控制：✅/❌
- 错误处理：✅/❌

### 性能指标
- 发送成功率：[百分比]
- 平均发送间隔：[秒]
- 任务执行时间：[分钟]

### 问题记录
1. [问题描述]
   - 影响程度：[高/中/低]
   - 解决方案：[已解决/待处理]

### 建议
1. [改进建议]
2. [安全建议]
3. [功能建议]
```

## 总结

通过遵循本指南，可以安全、有效地测试群发功能，确保：
- 系统功能正常工作
- 不会对WhatsApp账号造成风险
- 测试数据安全可控
- 测试结果可靠有效 