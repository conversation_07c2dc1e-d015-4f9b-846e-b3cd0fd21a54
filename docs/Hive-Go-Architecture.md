# Hive Go架构设计文档

## 📋 技术栈重新设计

### 核心技术栈
- **后端**：Go + Gin + GORM + SQLite + Goroutines
- **前端**：Vue3 + TypeScript + Element Plus
- **WhatsApp服务**：Node.js + whatsapp-web.js (由Go管理)
- **数据库**：SQLite (主数据库) + Redis (缓存)
- **消息队列**：Redis Streams
- **部署**：Docker + Docker Compose

## 🏗️ 项目结构设计

### Go Modules组织
```
hive/
├── go.mod                     # Go模块根配置
├── go.sum
├── README.md
├── docker-compose.yml
├── .env
├── .gitignore
├── docs/                      # 文档目录
│   ├── Hive-Feature-Design.md
│   ├── Hive-Go-Architecture.md
│   └── API-Documentation.md
├── cmd/                       # 应用程序入口
│   ├── api/                  # API服务
│   │   ├── main.go
│   │   └── server.go
│   └── whatsapp-manager/     # WhatsApp服务管理器
│       ├── main.go
│       └── manager.go
├── internal/                  # 内部包
│   ├── config/               # 配置管理
│   │   ├── config.go
│   │   └── database.go
│   ├── models/               # 数据模型
│   │   ├── tenant.go
│   │   ├── whatsapp.go
│   │   ├── customer.go
│   │   ├── message.go
│   │   └── user.go
│   ├── repository/           # 数据访问层
│   │   ├── tenant.go
│   │   ├── whatsapp.go
│   │   ├── customer.go
│   │   └── message.go
│   ├── service/              # 业务逻辑层
│   │   ├── tenant.go
│   │   ├── whatsapp.go
│   │   ├── customer.go
│   │   └── message.go
│   ├── handler/              # HTTP处理器
│   │   ├── tenant.go
│   │   ├── whatsapp.go
│   │   ├── customer.go
│   │   └── message.go
│   ├── middleware/           # 中间件
│   │   ├── auth.go
│   │   ├── cors.go
│   │   ├── logging.go
│   │   └── rate_limit.go
│   ├── database/             # 数据库操作
│   │   ├── connection.go
│   │   ├── migration.go
│   │   └── sqlite.go
│   └── utils/                # 工具函数
│       ├── crypto.go
│       ├── jwt.go
│       └── validator.go
├── pkg/                      # 公共包
│   ├── types/               # 公共类型
│   │   └── types.go
│   └── errors/              # 错误处理
│       └── errors.go
├── services/                 # 外部服务
│   └── whatsapp-service/    # Node.js WhatsApp服务
│       ├── package.json
│       ├── src/
│       │   ├── index.ts
│       │   ├── services/
│       │   ├── controllers/
│       │   └── routes/
│       └── dist/
├── frontend/                 # Vue前端
│   ├── package.json
│   ├── vite.config.ts
│   ├── src/
│   │   ├── main.ts
│   │   ├── App.vue
│   │   ├── router/
│   │   ├── stores/
│   │   ├── components/
│   │   ├── views/
│   │   ├── utils/
│   │   └── types/
│   ├── public/
│   └── dist/
├── scripts/                  # 构建和部署脚本
│   ├── build.sh
│   ├── deploy.sh
│   └── docker/
└── tests/                    # 集成测试
    ├── api_tests/
    ├── integration_tests/
    └── performance_tests/
```

## 🎯 Go Modules配置

### 根目录 go.mod
```go
module github.com/hive/whatsapp-saas

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/gin-contrib/cors v1.4.0
    github.com/gin-contrib/logger v0.2.6
    gorm.io/gorm v1.25.5
    gorm.io/driver/sqlite v1.5.4
    github.com/golang-jwt/jwt/v5 v5.2.0
    github.com/google/uuid v1.5.0
    github.com/redis/go-redis/v9 v9.3.1
    github.com/spf13/viper v1.18.2
    github.com/sirupsen/logrus v1.9.3
    github.com/stretchr/testify v1.8.4
    golang.org/x/crypto v0.17.0
    golang.org/x/time v0.5.0
)

require (
    github.com/bytedance/sonic v1.9.1 // indirect
    github.com/chenzhuoyu/base64x v0.0.0-20221115062448-fe3a3abad311 // indirect
    github.com/davecgh/go-spew v1.1.1 // indirect
    github.com/fsnotify/fsnotify v1.7.0 // indirect
    github.com/gabriel-vasile/mimetype v1.4.2 // indirect
    github.com/gin-contrib/sse v0.1.0 // indirect
    github.com/go-playground/locales v0.14.1 // indirect
    github.com/go-playground/universal-translator v0.18.1 // indirect
    github.com/go-playground/validator/v10 v10.14.0 // indirect
    github.com/goccy/go-json v0.10.2 // indirect
    github.com/hashicorp/hcl v1.0.0 // indirect
    github.com/jinzhu/inflection v1.0.0 // indirect
    github.com/jinzhu/now v1.1.5 // indirect
    github.com/json-iterator/go v1.1.12 // indirect
    github.com/klauspost/cpuid/v2 v2.2.4 // indirect
    github.com/leodido/go-urn v1.2.4 // indirect
    github.com/magiconair/properties v1.8.7 // indirect
    github.com/mattn/go-isatty v0.0.19 // indirect
    github.com/mattn/go-sqlite3 v1.14.17 // indirect
    github.com/mitchellh/mapstructure v1.5.0 // indirect
    github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
    github.com/modern-go/reflect2 v1.0.2 // indirect
    github.com/pelletier/go-toml/v2 v2.1.0 // indirect
    github.com/pmezard/go-difflib v1.0.0 // indirect
    github.com/sagikazarmark/locafero v0.4.0 // indirect
    github.com/sagikazarmark/slog-shim v0.1.0 // indirect
    github.com/spf13/afero v1.11.0 // indirect
    github.com/spf13/cast v1.6.0 // indirect
    github.com/spf13/pflag v1.0.5 // indirect
    github.com/subosito/gotenv v1.6.0 // indirect
    github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
    github.com/ugorji/go/codec v1.2.11 // indirect
    golang.org/x/arch v0.3.0 // indirect
    golang.org/x/net v0.19.0 // indirect
    golang.org/x/sys v0.15.0 // indirect
    golang.org/x/text v0.14.0 // indirect
    gopkg.in/ini.v1 v1.67.0 // indirect
    gopkg.in/yaml.v3 v3.0.1 // indirect
)
```

## 🏗️ 核心模块设计

### 1. internal/config - 配置管理
**功能**：提供配置管理、环境变量处理等

```go
// internal/config/config.go
package config

import (
    "github.com/spf13/viper"
    "time"
)

type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    JWT      JWTConfig      `mapstructure:"jwt"`
    WhatsApp WhatsAppConfig `mapstructure:"whatsapp"`
}

type ServerConfig struct {
    Host         string        `mapstructure:"host"`
    Port         int           `mapstructure:"port"`
    ReadTimeout  time.Duration `mapstructure:"read_timeout"`
    WriteTimeout time.Duration `mapstructure:"write_timeout"`
    IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

type DatabaseConfig struct {
    Driver   string `mapstructure:"driver"`
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    User     string `mapstructure:"user"`
    Password string `mapstructure:"password"`
    DBName   string `mapstructure:"dbname"`
    SSLMode  string `mapstructure:"sslmode"`
}

type RedisConfig struct {
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    Password string `mapstructure:"password"`
    DB       int    `mapstructure:"db"`
}

type JWTConfig struct {
    Secret     string        `mapstructure:"secret"`
    Expiration time.Duration `mapstructure:"expiration"`
}

type WhatsAppConfig struct {
    NodeServicePort int    `mapstructure:"node_service_port"`
    SessionDir      string `mapstructure:"session_dir"`
    DataDir         string `mapstructure:"data_dir"`
}

func LoadConfig(path string) (*Config, error) {
    viper.SetConfigFile(path)
    viper.AutomaticEnv()

    if err := viper.ReadInConfig(); err != nil {
        return nil, err
    }

    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, err
    }

    return &config, nil
}
```

### 2. internal/models - 数据模型
**功能**：定义数据模型、GORM标签等

```go
// internal/models/tenant.go
package models

import (
    "time"
    "github.com/google/uuid"
    "gorm.io/gorm"
)

type Tenant struct {
    ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
    Name        string    `gorm:"size:100;not null"`
    Domain      *string   `gorm:"size:100;unique"`
    PlanType    string    `gorm:"size:20;default:'basic'"`
    MaxAccounts int       `gorm:"default:1"`
    MaxStorage  int64     `gorm:"default:**********"` // 1GB
    Status      string    `gorm:"size:20;default:'active'"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
}

type WhatsAppAccount struct {
    ID                   uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
    TenantID             uuid.UUID `gorm:"type:uuid;not null"`
    AccountName          string    `gorm:"size:100;not null"`
    PhoneNumber          string    `gorm:"size:20;unique;not null"`
    SessionID            *string   `gorm:"size:100;unique"`
    Status               string    `gorm:"size:20;default:'disconnected'"`
    AccountType          string    `gorm:"size:20;default:'personal'"`
    GroupID              *string   `gorm:"size:50"`
    IsPrimary            bool      `gorm:"default:false"`
    MaxConcurrentChats   int       `gorm:"default:50"`
    CurrentChatCount     int       `gorm:"default:0"`
    LastActivity         *time.Time
    LoginTime            *time.Time
    CreatedAt            time.Time
    UpdatedAt            time.Time
    DeletedAt            gorm.DeletedAt `gorm:"index"`
    
    Tenant               Tenant `gorm:"foreignKey:TenantID"`
}

type Customer struct {
    ID           uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
    TenantID     uuid.UUID `gorm:"type:uuid;not null"`
    PhoneNumber  string    `gorm:"size:20;unique;not null"`
    Name         *string   `gorm:"size:100"`
    Email        *string   `gorm:"size:100"`
    AvatarURL    *string   `gorm:"type:text"`
    Tags         string    `gorm:"type:text"` // JSON array
    Source       *string   `gorm:"size:50"`
    Status       string    `gorm:"size:20;default:'active'"`
    TotalOrders  int       `gorm:"default:0"`
    TotalAmount  float64   `gorm:"type:decimal(10,2);default:0"`
    LastContact  *time.Time
    CreatedAt    time.Time
    UpdatedAt    time.Time
    DeletedAt    gorm.DeletedAt `gorm:"index"`
    
    Tenant       Tenant `gorm:"foreignKey:TenantID"`
}

type Message struct {
    ID         uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
    TenantID   uuid.UUID `gorm:"type:uuid;not null"`
    AccountID  uuid.UUID `gorm:"type:uuid;not null"`
    CustomerID uuid.UUID `gorm:"type:uuid;not null"`
    Direction  string    `gorm:"size:20;not null"` // 'inbound' or 'outbound'
    MessageType string   `gorm:"size:20;not null"` // 'text', 'image', 'video', etc.
    Content    *string   `gorm:"type:text"`
    MediaURL   *string   `gorm:"type:text"`
    Status     string    `gorm:"size:20;default:'sent'"`
    SentAt     *time.Time
    DeliveredAt *time.Time
    ReadAt     *time.Time
    CreatedAt  time.Time
    DeletedAt  gorm.DeletedAt `gorm:"index"`
    
    Tenant     Tenant           `gorm:"foreignKey:TenantID"`
    Account    WhatsAppAccount  `gorm:"foreignKey:AccountID"`
    Customer   Customer         `gorm:"foreignKey:CustomerID"`
}
```

### 3. internal/repository - 数据访问层
**功能**：数据库操作、CRUD操作等

```go
// internal/repository/tenant.go
package repository

import (
    "context"
    "github.com/google/uuid"
    "github.com/hive/whatsapp-saas/internal/models"
    "gorm.io/gorm"
)

type TenantRepository struct {
    db *gorm.DB
}

func NewTenantRepository(db *gorm.DB) *TenantRepository {
    return &TenantRepository{db: db}
}

func (r *TenantRepository) Create(ctx context.Context, tenant *models.Tenant) error {
    return r.db.WithContext(ctx).Create(tenant).Error
}

func (r *TenantRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Tenant, error) {
    var tenant models.Tenant
    err := r.db.WithContext(ctx).Where("id = ?", id).First(&tenant).Error
    if err != nil {
        return nil, err
    }
    return &tenant, nil
}

func (r *TenantRepository) List(ctx context.Context, offset, limit int) ([]models.Tenant, error) {
    var tenants []models.Tenant
    err := r.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&tenants).Error
    return tenants, err
}

func (r *TenantRepository) Update(ctx context.Context, tenant *models.Tenant) error {
    return r.db.WithContext(ctx).Save(tenant).Error
}

func (r *TenantRepository) Delete(ctx context.Context, id uuid.UUID) error {
    return r.db.WithContext(ctx).Delete(&models.Tenant{}, id).Error
}
```

### 4. internal/service - 业务逻辑层
**功能**：业务逻辑、服务层、领域服务等

```go
// internal/service/tenant.go
package service

import (
    "context"
    "github.com/google/uuid"
    "github.com/hive/whatsapp-saas/internal/models"
    "github.com/hive/whatsapp-saas/internal/repository"
)

type TenantService struct {
    repo *repository.TenantRepository
}

func NewTenantService(repo *repository.TenantRepository) *TenantService {
    return &TenantService{repo: repo}
}

func (s *TenantService) CreateTenant(ctx context.Context, name, domain, planType string) (*models.Tenant, error) {
    tenant := &models.Tenant{
        Name:     name,
        Domain:   &domain,
        PlanType: planType,
        Status:   "active",
    }

    if err := s.repo.Create(ctx, tenant); err != nil {
        return nil, err
    }

    return tenant, nil
}

func (s *TenantService) GetTenant(ctx context.Context, id uuid.UUID) (*models.Tenant, error) {
    return s.repo.GetByID(ctx, id)
}

func (s *TenantService) ListTenants(ctx context.Context, offset, limit int) ([]models.Tenant, error) {
    return s.repo.List(ctx, offset, limit)
}

func (s *TenantService) UpdateTenant(ctx context.Context, tenant *models.Tenant) error {
    return s.repo.Update(ctx, tenant)
}

func (s *TenantService) DeleteTenant(ctx context.Context, id uuid.UUID) error {
    return s.repo.Delete(ctx, id)
}
```

### 5. internal/handler - HTTP处理器
**功能**：HTTP请求处理、响应格式化等

```go
// internal/handler/tenant.go
package handler

import (
    "net/http"
    "strconv"
    "github.com/gin-gonic/gin"
    "github.com/google/uuid"
    "github.com/hive/whatsapp-saas/internal/models"
    "github.com/hive/whatsapp-saas/internal/service"
)

type TenantHandler struct {
    service *service.TenantService
}

func NewTenantHandler(service *service.TenantService) *TenantHandler {
    return &TenantHandler{service: service}
}

type CreateTenantRequest struct {
    Name     string `json:"name" binding:"required"`
    Domain   string `json:"domain"`
    PlanType string `json:"plan_type" binding:"required"`
}

func (h *TenantHandler) CreateTenant(c *gin.Context) {
    var req CreateTenantRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    tenant, err := h.service.CreateTenant(c.Request.Context(), req.Name, req.Domain, req.PlanType)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, tenant)
}

func (h *TenantHandler) GetTenant(c *gin.Context) {
    idStr := c.Param("id")
    id, err := uuid.Parse(idStr)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
        return
    }

    tenant, err := h.service.GetTenant(c.Request.Context(), id)
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Tenant not found"})
        return
    }

    c.JSON(http.StatusOK, tenant)
}

func (h *TenantHandler) ListTenants(c *gin.Context) {
    offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
    limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

    tenants, err := h.service.ListTenants(c.Request.Context(), offset, limit)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, tenants)
}

func (h *TenantHandler) UpdateTenant(c *gin.Context) {
    idStr := c.Param("id")
    id, err := uuid.Parse(idStr)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
        return
    }

    var tenant models.Tenant
    if err := c.ShouldBindJSON(&tenant); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    tenant.ID = id
    if err := h.service.UpdateTenant(c.Request.Context(), &tenant); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, tenant)
}

func (h *TenantHandler) DeleteTenant(c *gin.Context) {
    idStr := c.Param("id")
    id, err := uuid.Parse(idStr)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
        return
    }

    if err := h.service.DeleteTenant(c.Request.Context(), id); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Tenant deleted successfully"})
}
```

### 6. cmd/api - API服务入口
**功能**：HTTP服务器、路由配置、中间件等

```go
// cmd/api/main.go
package main

import (
    "log"
    "net/http"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/gin-contrib/cors"
    "github.com/gin-contrib/logger"
    "github.com/sirupsen/logrus"
    
    "github.com/hive/whatsapp-saas/internal/config"
    "github.com/hive/whatsapp-saas/internal/database"
    "github.com/hive/whatsapp-saas/internal/handler"
    "github.com/hive/whatsapp-saas/internal/middleware"
    "github.com/hive/whatsapp-saas/internal/repository"
    "github.com/hive/whatsapp-saas/internal/service"
)

func main() {
    // 加载配置
    cfg, err := config.LoadConfig("config/config.yaml")
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // 初始化数据库
    db, err := database.InitDatabase(cfg.Database)
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }

    // 运行数据库迁移
    if err := database.RunMigrations(db); err != nil {
        log.Fatal("Failed to run migrations:", err)
    }

    // 初始化存储库
    tenantRepo := repository.NewTenantRepository(db)
    whatsappRepo := repository.NewWhatsAppRepository(db)
    customerRepo := repository.NewCustomerRepository(db)
    messageRepo := repository.NewMessageRepository(db)

    // 初始化服务
    tenantService := service.NewTenantService(tenantRepo)
    whatsappService := service.NewWhatsAppService(whatsappRepo)
    customerService := service.NewCustomerService(customerRepo)
    messageService := service.NewMessageService(messageRepo)

    // 初始化处理器
    tenantHandler := handler.NewTenantHandler(tenantService)
    whatsappHandler := handler.NewWhatsAppHandler(whatsappService)
    customerHandler := handler.NewCustomerHandler(customerService)
    messageHandler := handler.NewMessageHandler(messageService)

    // 设置Gin模式
    gin.SetMode(gin.ReleaseMode)

    // 创建Gin引擎
    r := gin.New()

    // 添加中间件
    r.Use(gin.Recovery())
    r.Use(logger.SetLogger(logger.Config{
        DefaultLevel:  "info",
        UTC:          true,
        SkipPath:     []string{"/health"},
    }))
    r.Use(cors.New(cors.Config{
        AllowOrigins:     []string{"*"},
        AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
        ExposeHeaders:    []string{"Content-Length"},
        AllowCredentials: true,
        MaxAge:           12 * time.Hour,
    }))

    // 健康检查
    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{"status": "ok"})
    })

    // API路由
    api := r.Group("/api/v1")
    {
        // 租户路由
        tenants := api.Group("/tenants")
        {
            tenants.POST("/", tenantHandler.CreateTenant)
            tenants.GET("/", tenantHandler.ListTenants)
            tenants.GET("/:id", tenantHandler.GetTenant)
            tenants.PUT("/:id", tenantHandler.UpdateTenant)
            tenants.DELETE("/:id", tenantHandler.DeleteTenant)
        }

        // WhatsApp路由
        whatsapp := api.Group("/whatsapp")
        {
            whatsapp.POST("/accounts", whatsappHandler.CreateAccount)
            whatsapp.GET("/accounts", whatsappHandler.ListAccounts)
            whatsapp.GET("/accounts/:id", whatsappHandler.GetAccount)
            whatsapp.POST("/accounts/:id/login", whatsappHandler.LoginAccount)
            whatsapp.POST("/accounts/:id/logout", whatsappHandler.LogoutAccount)
            whatsapp.POST("/accounts/:id/messages", whatsappHandler.SendMessage)
        }

        // 客户路由
        customers := api.Group("/customers")
        {
            customers.POST("/", customerHandler.CreateCustomer)
            customers.GET("/", customerHandler.ListCustomers)
            customers.GET("/:id", customerHandler.GetCustomer)
            customers.PUT("/:id", customerHandler.UpdateCustomer)
            customers.DELETE("/:id", customerHandler.DeleteCustomer)
        }

        // 消息路由
        messages := api.Group("/messages")
        {
            messages.GET("/", messageHandler.ListMessages)
            messages.GET("/:id", messageHandler.GetMessage)
            messages.POST("/", messageHandler.SendMessage)
        }
    }

    // 启动服务器
    addr := cfg.Server.Host + ":" + string(cfg.Server.Port)
    logrus.Infof("Starting server on %s", addr)
    
    srv := &http.Server{
        Addr:         addr,
        Handler:      r,
        ReadTimeout:  cfg.Server.ReadTimeout,
        WriteTimeout: cfg.Server.WriteTimeout,
        IdleTimeout:  cfg.Server.IdleTimeout,
    }

    if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
        log.Fatal("Failed to start server:", err)
    }
}
```

## 🎯 数据库设计

### SQLite表结构
```sql
-- 租户表
CREATE TABLE tenants (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    domain TEXT UNIQUE,
    plan_type TEXT NOT NULL DEFAULT 'basic',
    max_accounts INTEGER NOT NULL DEFAULT 1,
    max_storage BIGINT NOT NULL DEFAULT **********,
    status TEXT NOT NULL DEFAULT 'active',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME
);

-- WhatsApp账号表
CREATE TABLE whatsapp_accounts (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    account_name TEXT NOT NULL,
    phone_number TEXT UNIQUE NOT NULL,
    session_id TEXT UNIQUE,
    status TEXT NOT NULL DEFAULT 'disconnected',
    account_type TEXT NOT NULL DEFAULT 'personal',
    group_id TEXT,
    is_primary BOOLEAN NOT NULL DEFAULT FALSE,
    max_concurrent_chats INTEGER NOT NULL DEFAULT 50,
    current_chat_count INTEGER NOT NULL DEFAULT 0,
    last_activity DATETIME,
    login_time DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- 客户表
CREATE TABLE customers (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    phone_number TEXT UNIQUE NOT NULL,
    name TEXT,
    email TEXT,
    avatar_url TEXT,
    tags TEXT, -- JSON数组
    source TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    total_orders INTEGER NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    last_contact DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- 消息表
CREATE TABLE messages (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    account_id TEXT NOT NULL,
    customer_id TEXT NOT NULL,
    direction TEXT NOT NULL, -- 'inbound' or 'outbound'
    message_type TEXT NOT NULL, -- 'text', 'image', 'video', etc.
    content TEXT,
    media_url TEXT,
    status TEXT NOT NULL DEFAULT 'sent',
    sent_at DATETIME,
    delivered_at DATETIME,
    read_at DATETIME,
    created_at DATETIME NOT NULL,
    deleted_at DATETIME,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (account_id) REFERENCES whatsapp_accounts(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- 工单表
CREATE TABLE tickets (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    customer_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT,
    priority TEXT NOT NULL DEFAULT 'medium',
    status TEXT NOT NULL DEFAULT 'open',
    assigned_to TEXT,
    created_by TEXT,
    resolved_at DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'agent',
    status TEXT NOT NULL DEFAULT 'active',
    last_login DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);
```

## 🚀 部署配置

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  hive-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "3000:3000"
    environment:
      - GIN_MODE=release
      - HIVE_DATABASE_URL=sqlite:/data/hive.db
      - HIVE_REDIS_URL=redis://redis:6379
    volumes:
      - ./data:/data
    depends_on:
      - redis
    networks:
      - hive-network

  hive-whatsapp-manager:
    build:
      context: .
      dockerfile: Dockerfile.whatsapp-manager
    environment:
      - GIN_MODE=release
    volumes:
      - ./services/whatsapp-service:/app/services/whatsapp-service
      - ./data/sessions:/app/data/sessions
    depends_on:
      - hive-api
    networks:
      - hive-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - hive-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    depends_on:
      - hive-api
    networks:
      - hive-network

volumes:
  redis-data:

networks:
  hive-network:
    driver: bridge
```

### Dockerfile配置
```dockerfile
# Dockerfile.api
FROM golang:1.21-alpine AS builder

WORKDIR /app

# 安装依赖
RUN apk add --no-cache git

# 复制go mod文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/api

FROM alpine:latest

RUN apk --no-cache add ca-certificates

WORKDIR /root/

# 复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

EXPOSE 3000

CMD ["./main"]
```

## 🔧 开发工具配置

### VS Code配置
```json
// .vscode/settings.json
{
  "go.useLanguageServer": true,
  "go.lintTool": "golangci-lint",
  "go.formatTool": "goimports",
  "go.buildOnSave": "package",
  "go.testOnSave": false,
  "go.vetOnSave": "package",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

### 构建脚本
```bash
#!/bin/bash
# scripts/build.sh

set -e

echo "Building Hive Go project..."

# 检查Go版本
go version

# 清理之前的构建
go clean

# 运行测试
go test ./...

# 运行静态检查
golangci-lint run

# 构建API服务
go build -o bin/api ./cmd/api

# 构建WhatsApp管理器
go build -o bin/whatsapp-manager ./cmd/whatsapp-manager

# 构建前端
cd frontend
npm install
npm run build
cd ..

# 构建Node.js服务
cd services/whatsapp-service
npm install
npm run build
cd ../..

echo "Build completed successfully!"
```

## 📊 性能优化

### 1. 数据库优化
- 使用GORM连接池管理SQLite连接
- 实现查询缓存
- 优化索引设计
- 使用事务处理批量操作

### 2. 内存管理
- 使用sync.Pool减少内存分配
- 实现对象池
- 使用零拷贝技术优化数据传输

### 3. 并发处理
- 使用Goroutines处理并发
- 实现工作线程池
- 使用channel进行协程间通信

### 4. 缓存策略
- Redis缓存热点数据
- 实现本地缓存
- 使用缓存预热

## 🔒 安全设计

### 1. 认证授权
- JWT Token认证
- 基于角色的权限控制
- API密钥管理

### 2. 数据安全
- 敏感数据加密存储
- 传输层TLS加密
- 数据备份和恢复

### 3. 输入验证
- 请求参数验证
- SQL注入防护
- XSS攻击防护

## 📈 监控和日志

### 1. 日志系统
- 结构化日志记录
- 日志级别控制
- 日志轮转和归档

### 2. 监控指标
- 系统性能指标
- 业务指标监控
- 告警机制

### 3. 链路追踪
- 请求链路追踪
- 性能分析
- 错误定位

这个Go架构设计提供了高性能、简洁、易维护的解决方案，同时保持了良好的可扩展性。通过模块化组织，各个模块职责清晰，便于团队协作开发。 