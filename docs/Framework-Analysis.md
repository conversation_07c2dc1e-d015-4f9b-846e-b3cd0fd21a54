# Hive项目框架分析文档

## 📋 现有框架分析

### 后端框架分析

#### 技术栈
- **框架**: Gin + GORM + SQLite
- **认证**: JWT Token
- **权限**: RBAC (Role-Based Access Control)
- **日志**: 自定义日志系统
- **文件上传**: 本地文件存储

#### 现有功能模块
1. **用户管理** (`main.go`)
   - 用户CRUD操作
   - 用户列表、详情、创建、更新、删除
   - 个人资料管理

2. **权限系统** (`models.go`)
   - 角色管理 (Role)
   - 权限管理 (Permission)
   - 用户角色关联 (UserRole)
   - 权限检查函数

3. **系统配置** (`models.go`)
   - 系统配置管理 (SystemConfig)
   - 配置分类和类型支持

4. **日志系统** (`log_models.go`, `log_api.go`)
   - 操作日志记录
   - 日志查询和管理

5. **文件上传** (`upload_api.go`)
   - 文件上传功能
   - 文件管理

6. **数据导入导出** (`import_api.go`, `export_api.go`)
   - 数据导入功能
   - 数据导出功能

#### 代码结构
```
backend/
├── main.go              # 主入口，包含路由和中间件
├── models.go            # 数据模型定义
├── auth.go              # 认证相关API
├── role_api.go          # 角色管理API
├── config_api.go        # 配置管理API
├── log_api.go           # 日志管理API
├── log_models.go        # 日志模型
├── upload_api.go        # 文件上传API
├── import_api.go        # 数据导入API
├── export_api.go        # 数据导出API
├── uploads/             # 上传文件目录
├── go.mod               # Go模块配置
└── go.sum               # 依赖锁定文件
```

### 前端框架分析

#### 技术栈
- **框架**: Vue3 + TypeScript + Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: 自定义API调用

#### 现有功能模块
1. **认证系统**
   - 登录/登出
   - JWT Token管理
   - 路由守卫

2. **布局系统**
   - 响应式布局
   - 侧边栏导航
   - 顶部导航栏
   - 主题切换

3. **用户管理界面**
   - 用户列表
   - 用户详情
   - 用户编辑

4. **系统管理**
   - 系统配置
   - 日志查看
   - 文件管理

#### 代码结构
```
frontend/
├── src/
│   ├── main.ts          # 应用入口
│   ├── App.vue          # 根组件
│   ├── router/          # 路由配置
│   ├── stores/          # Pinia状态管理
│   ├── views/           # 页面组件
│   ├── components/      # 通用组件
│   ├── layouts/         # 布局组件
│   ├── utils/           # 工具函数
│   ├── types/           # TypeScript类型
│   ├── styles/          # 样式文件
│   └── assets/          # 静态资源
├── public/              # 公共资源
├── package.json         # 依赖配置
├── vite.config.ts       # Vite配置
└── tsconfig.json        # TypeScript配置
```

## 🎯 改造方案

### 可复用的组件

#### 后端可复用组件
1. **✅ 认证系统** - 完全复用
   - JWT Token生成和验证
   - 密码加密和验证
   - 登录/登出API

2. **✅ 权限系统** - 完全复用
   - RBAC权限模型
   - 权限检查函数
   - 角色管理API

3. **✅ 基础中间件** - 完全复用
   - CORS中间件
   - 日志中间件
   - 认证中间件

4. **✅ 系统配置** - 完全复用
   - 配置管理模型
   - 配置API

5. **✅ 文件上传** - 完全复用
   - 文件上传功能
   - 文件管理API

6. **✅ 日志系统** - 完全复用
   - 操作日志记录
   - 日志查询API

#### 前端可复用组件
1. **✅ 认证界面** - 完全复用
   - 登录页面
   - 认证状态管理
   - 路由守卫

2. **✅ 布局系统** - 完全复用
   - 响应式布局
   - 侧边栏导航
   - 顶部导航栏
   - 主题切换

3. **✅ 用户管理界面** - 部分复用
   - 用户列表组件
   - 用户表单组件
   - 需要适配多租户

4. **✅ 通用组件** - 完全复用
   - 表格组件
   - 表单组件
   - 对话框组件
   - 通知组件

### 需要新开发的组件

#### 后端新开发组件
1. **🆕 多租户系统**
   - 租户模型 (Tenant)
   - 租户隔离中间件
   - 租户管理API

2. **🆕 WhatsApp集成**
   - WhatsApp账号模型
   - 会话管理
   - 消息处理API
   - Node.js服务管理

3. **🆕 客户管理**
   - 客户模型 (Customer)
   - 客户标签系统
   - 客户分组管理

4. **🆕 消息系统**
   - 消息模型 (Message)
   - 消息发送/接收API
   - 消息状态跟踪

5. **🆕 工单系统**
   - 工单模型 (Ticket)
   - 工单流转逻辑
   - 工单分配算法

6. **🆕 营销系统**
   - 营销活动模型
   - 群发消息API
   - 营销数据分析

#### 前端新开发组件
1. **🆕 租户管理界面**
   - 租户列表页面
   - 租户详情页面
   - 租户配置页面

2. **🆕 WhatsApp管理界面**
   - 账号管理页面
   - 会话状态监控
   - 二维码登录界面

3. **🆕 客户管理界面**
   - 客户列表页面
   - 客户详情页面
   - 客户标签管理

4. **🆕 消息管理界面**
   - 消息列表页面
   - 消息发送界面
   - 消息状态监控

5. **🆕 工单管理界面**
   - 工单列表页面
   - 工单详情页面
   - 工单处理界面

6. **🆕 营销管理界面**
   - 营销活动页面
   - 群发消息界面
   - 数据分析页面

### 需要修改的组件

#### 后端修改组件
1. **🔄 用户模型** - 需要修改
   - 添加租户ID字段
   - 修改权限检查逻辑
   - 适配多租户查询

2. **🔄 数据库连接** - 需要修改
   - 支持多租户数据库隔离
   - 添加租户上下文

3. **🔄 API响应** - 需要修改
   - 添加租户信息
   - 修改数据过滤逻辑

#### 前端修改组件
1. **🔄 用户管理** - 需要修改
   - 添加租户选择
   - 修改用户列表查询
   - 适配多租户权限

2. **🔄 路由守卫** - 需要修改
   - 添加租户权限检查
   - 修改路由跳转逻辑

3. **🔄 状态管理** - 需要修改
   - 添加租户状态
   - 修改用户状态管理

## 📊 开发优先级

### 第一阶段：基础改造（1-2天）
1. **多租户基础架构**
   - 修改用户模型，添加租户ID
   - 实现租户隔离中间件
   - 修改数据库查询逻辑

2. **前端租户适配**
   - 修改用户管理界面
   - 添加租户选择功能
   - 修改路由守卫

### 第二阶段：核心功能开发（3-5天）
3. **WhatsApp集成**
   - 实现WhatsApp账号管理
   - 开发会话管理功能
   - 创建消息处理API

4. **客户管理**
   - 实现客户数据模型
   - 开发客户管理API
   - 创建客户管理界面

### 第三阶段：业务功能开发（5-7天）
5. **消息系统**
   - 实现消息发送/接收
   - 开发消息状态跟踪
   - 创建消息管理界面

6. **工单系统**
   - 实现工单模型和API
   - 开发工单流转逻辑
   - 创建工单管理界面

### 第四阶段：营销功能开发（3-4天）
7. **营销系统**
   - 实现营销活动管理
   - 开发群发消息功能
   - 创建营销分析界面

## 🔧 技术实现细节

### 数据库改造
```sql
-- 添加租户表
CREATE TABLE tenants (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    domain TEXT UNIQUE,
    plan_type TEXT NOT NULL DEFAULT 'basic',
    status TEXT NOT NULL DEFAULT 'active',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 修改用户表，添加租户ID
ALTER TABLE users ADD COLUMN tenant_id TEXT NOT NULL;
ALTER TABLE users ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id);
```

### API改造
```go
// 添加租户上下文中间件
func tenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := c.GetHeader("X-Tenant-ID")
        if tenantID == "" {
            c.JSON(400, gin.H{"error": "Tenant ID required"})
            c.Abort()
            return
        }
        c.Set("tenant_id", tenantID)
        c.Next()
    }
}
```

### 前端改造
```typescript
// 添加租户状态管理
interface TenantState {
  currentTenant: Tenant | null
  tenants: Tenant[]
  setCurrentTenant: (tenant: Tenant) => void
  loadTenants: () => Promise<void>
}
```

## 📈 开发计划

### 第1周：基础改造
- 多租户架构实现
- 数据库模型改造
- 前端租户适配

### 第2周：WhatsApp集成
- WhatsApp服务开发
- 账号管理功能
- 基础消息处理

### 第3周：客户管理
- 客户数据模型
- 客户管理API
- 客户管理界面

### 第4周：消息和工单
- 消息系统开发
- 工单系统开发
- 基础营销功能

### 第5周：营销和优化
- 营销功能完善
- 性能优化
- 测试和部署

## 🎯 总结

现有框架提供了很好的基础，特别是：
- ✅ 完整的认证和权限系统
- ✅ 成熟的用户管理功能
- ✅ 完善的日志和配置系统
- ✅ 响应式的前端界面

主要改造工作集中在：
- 🆕 多租户架构适配
- 🆕 WhatsApp集成功能
- 🆕 客户和消息管理
- 🆕 营销和工单系统

通过合理的改造和新功能开发，可以快速构建出完整的Hive WhatsApp SaaS平台。 