# WhatsApp 集成实现方案设计文档

## 📋 项目概述

本项目是一个基于 Vue3 + Go + Node.js 的 WhatsApp 管理系统，采用三层架构：
- **前端 (Vue3)**：用户界面，只与 Go 后端交互
- **后端 (Go)**：业务逻辑层，处理前端请求并与 Node.js 服务通信
- **WhatsApp 服务 (Node.js)**：WhatsApp Web.js 集成，处理实际的 WhatsApp 操作

### 🔑 重要设计原则

**二维码处理原则**
- 二维码是一次性的，只在登录过程中临时使用
- 不进行持久化存储，扫码成功后立即清除
- 通过 WebSocket 实时推送，确保用户体验
- 设置过期时间，避免二维码长期存在

## 🏗️ 系统架构

```
┌─────────────┐    HTTP/WebSocket    ┌─────────────┐    HTTP/WebSocket    ┌─────────────┐
│   Vue3      │ ◄──────────────────► │    Go       │ ◄──────────────────► │  Node.js    │
│  Frontend   │                      │  Backend    │                      │WhatsApp API │
└─────────────┘                      └─────────────┘                      └─────────────┘
                                              │                                    │
                                              ▼                                    ▼
                                       ┌─────────────┐                      ┌─────────────┐
                                       │  Database   │                      │ WhatsApp    │
                                       │   (SQLite)  │                      │   Web.js    │
                                       └─────────────┘                      └─────────────┘
```

## 🎯 功能模块设计

### 1. WhatsApp 账号管理模块

#### 1.1 数据模型设计

**Go 后端模型 (models.go)**
```go
type WhatsAppAccount struct {
    ID                uint      `json:"id" gorm:"primaryKey"`
    UserID            uint      `json:"user_id" gorm:"not null"`
    AccountName       string    `json:"account_name" gorm:"not null"`
    PhoneNumber       string    `json:"phone_number" gorm:"unique;not null"`
    SessionID         string    `json:"session_id" gorm:"unique"`
    Status            string    `json:"status" gorm:"default:disconnected"`
    LastActivity      *time.Time `json:"last_activity"`
    LoginTime         *time.Time `json:"login_time"`
    // ... 其他字段
}
```

**Node.js 服务模型**
```typescript
interface WhatsAppSession {
    id: string
    phoneNumber?: string
    status: 'disconnected' | 'connecting' | 'connected' | 'error'
    lastActivity?: Date
    loginTime?: Date
}

// 临时二维码数据（不持久化）
interface QRCodeData {
    sessionId: string
    qrCode: string
    timestamp: number
    expiresAt: number
}
```

#### 1.2 API 接口设计

**Go 后端 API**
```go
// 账号管理
POST   /api/whatsapp/accounts                    // 创建账号
GET    /api/whatsapp/accounts                    // 获取账号列表
GET    /api/whatsapp/accounts/:id                // 获取账号详情
PUT    /api/whatsapp/accounts/:id                // 更新账号
DELETE /api/whatsapp/accounts/:id                // 删除账号

// 会话管理
POST   /api/whatsapp/accounts/:id/login          // 登录账号
POST   /api/whatsapp/accounts/:id/logout         // 登出账号
GET    /api/whatsapp/accounts/:id/status         // 获取登录状态
```

**Node.js 服务 API**
```typescript
// 会话管理
POST   /api/whatsapp/sessions                    // 创建会话
GET    /api/whatsapp/sessions                    // 获取会话列表
GET    /api/whatsapp/sessions/:sessionId         // 获取会话状态
DELETE /api/whatsapp/sessions/:sessionId         // 删除会话
POST   /api/whatsapp/sessions/:sessionId/reconnect // 重连会话
```

#### 1.3 实现流程

**账号创建流程**
1. 前端调用 Go API 创建账号记录
2. Go 后端验证数据并保存到数据库
3. Go 后端调用 Node.js 服务创建会话
4. Node.js 服务返回会话ID和状态
5. Go 后端更新账号的 SessionID
6. 返回创建结果给前端

**账号登录流程**
1. 前端请求登录指定账号
2. Go 后端查找账号记录和 SessionID
3. Go 后端调用 Node.js 服务获取会话状态
4. 如果会话未连接，Node.js 服务生成临时二维码
5. Go 后端通过 WebSocket 实时推送二维码给前端
6. 前端显示二维码供用户扫码
7. Node.js 服务监听认证事件
8. 认证成功后清除二维码，更新会话状态
9. Go 后端同步更新账号状态

### 2. 聊天记录管理模块

#### 2.1 数据模型设计

**Go 后端模型**
```go
type WhatsAppChat struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    AccountID   uint      `json:"account_id" gorm:"not null"`
    ChatID      string    `json:"chat_id" gorm:"not null"`      // WhatsApp 聊天ID
    Name        string    `json:"name"`                         // 聊天名称
    Type        string    `json:"type" gorm:"default:private"`  // private, group
    UnreadCount int       `json:"unread_count" gorm:"default:0"`
    LastMessage string    `json:"last_message"`
    LastTime    *time.Time `json:"last_time"`
    Account     WhatsAppAccount `json:"account" gorm:"foreignKey:AccountID"`
    gorm.Model
}

type WhatsAppMessage struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    AccountID uint      `json:"account_id" gorm:"not null"`
    ChatID    string    `json:"chat_id" gorm:"not null"`
    MessageID string    `json:"message_id" gorm:"not null"`     // WhatsApp 消息ID
    From      string    `json:"from"`                           // 发送者
    To        string    `json:"to"`                             // 接收者
    Body      string    `json:"body" gorm:"type:text"`
    Type      string    `json:"type" gorm:"default:text"`       // text, image, video, etc.
    FromMe    bool      `json:"from_me"`
    Timestamp int64     `json:"timestamp"`
    HasMedia  bool      `json:"has_media"`
    MediaType string    `json:"media_type"`
    Account   WhatsAppAccount `json:"account" gorm:"foreignKey:AccountID"`
    gorm.Model
}
```

#### 2.2 API 接口设计

**Go 后端 API**
```go
// 聊天管理
GET    /api/whatsapp/accounts/:id/chats                    // 获取聊天列表
GET    /api/whatsapp/accounts/:id/chats/:chatId            // 获取聊天详情
GET    /api/whatsapp/accounts/:id/chats/:chatId/messages   // 获取消息记录
GET    /api/whatsapp/accounts/:id/chats/:chatId/stats      // 获取聊天统计

// 消息管理
POST   /api/whatsapp/accounts/:id/messages                 // 发送消息
GET    /api/whatsapp/accounts/:id/messages/search          // 搜索消息
GET    /api/whatsapp/accounts/:id/contacts                 // 获取联系人列表
```

**Node.js 服务 API**
```typescript
// 聊天和消息
GET    /api/whatsapp/sessions/:sessionId/chats             // 获取聊天列表
GET    /api/whatsapp/sessions/:sessionId/chats/:chatId/messages // 获取消息
POST   /api/whatsapp/sessions/:sessionId/search-messages   // 搜索消息
GET    /api/whatsapp/sessions/:sessionId/contacts          // 获取联系人
GET    /api/whatsapp/sessions/:sessionId/chats/:chatId/stats // 获取统计
```

#### 2.3 实现流程

**聊天列表获取流程**
1. 前端请求获取指定账号的聊天列表
2. Go 后端查找数据库中的聊天记录
3. Go 后端调用 Node.js 服务获取最新聊天数据
4. Go 后端同步更新数据库中的聊天信息
5. 返回聊天列表给前端

**消息记录获取流程**
1. 前端请求获取指定聊天的消息记录
2. Go 后端检查数据库中是否有缓存的消息
3. Go 后端调用 Node.js 服务获取最新消息
4. Go 后端将新消息保存到数据库
5. 返回消息记录给前端

**消息发送流程**
1. 前端发送消息请求
2. Go 后端验证账号状态
3. Go 后端调用 Node.js 服务发送消息
4. Node.js 服务返回发送结果
5. Go 后端将消息保存到数据库
6. 返回发送结果给前端

### 3. 实时通信模块

#### 3.1 二维码处理策略

**二维码特性**
- 二维码是一次性的，只在登录过程中临时使用
- 二维码有有效期，通常为几分钟
- 扫码成功后二维码立即失效
- 不需要在数据库中持久化存储

**二维码处理流程**
```typescript
// Node.js 服务中的二维码处理
client.on('qr', async (qr) => {
    // 生成临时二维码，不保存到数据库
    const qrCodeData = await QRCode.toDataURL(qr)
    
    // 通过 WebSocket 实时推送给前端
    io.to(`session:${sessionId}`).emit('qr-code', {
        sessionId,
        qrCode: qrCodeData,
        timestamp: Date.now()
    })
})

client.on('authenticated', () => {
    // 认证成功后清除二维码
    io.to(`session:${sessionId}`).emit('qr-code-clear', { sessionId })
})
```

**前端二维码处理**
```typescript
// 前端 WebSocket 监听
socket.on('qr-code', (data) => {
    // 显示二维码
    showQRCode(data.qrCode)
    // 设置二维码过期定时器
    setTimeout(() => {
        hideQRCode()
        showQRExpiredMessage()
    }, 5 * 60 * 1000) // 5分钟过期
})

socket.on('qr-code-clear', () => {
    // 清除二维码显示
    hideQRCode()
    showLoginSuccessMessage()
})
```

#### 3.2 WebSocket 设计

**Go 后端 WebSocket**
```go
// WebSocket 连接管理
type WSConnection struct {
    ID       string
    UserID   uint
    Conn     *websocket.Conn
    Send     chan []byte
}

// WebSocket 消息类型
type WSMessage struct {
    Type    string      `json:"type"`
    Data    interface{} `json:"data"`
    AccountID uint      `json:"account_id,omitempty"`
}
```

**Node.js 服务 WebSocket**
```typescript
// Socket.IO 事件
interface SocketEvents {
    'join-session': (sessionId: string) => void
    'leave-session': (sessionId: string) => void
    'client:ready': (sessionId: string, session: WhatsAppSession) => void
    'qr-code': (data: { sessionId: string, qrCode: string, timestamp: number }) => void
    'qr-code-clear': (data: { sessionId: string }) => void
    'client:message': (sessionId: string, message: any) => void
    'client:disconnected': (sessionId: string, session: WhatsAppSession) => void
}
```

#### 3.2 实时通信流程

**消息推送流程**
1. Node.js 服务接收到新消息
2. Node.js 服务通过 WebSocket 推送消息给 Go 后端
3. Go 后端将消息保存到数据库
4. Go 后端通过 WebSocket 推送消息给前端
5. 前端实时显示新消息

**状态同步流程**
1. Node.js 服务检测到会话状态变化
2. Node.js 服务推送状态更新给 Go 后端
3. Go 后端更新数据库中的账号状态
4. Go 后端推送状态更新给前端
5. 前端更新界面显示

### 4. 前端界面设计

#### 4.1 页面结构

```
src/
├── views/
│   ├── whatsapp/
│   │   ├── AccountList.vue          // 账号列表页面
│   │   ├── AccountDetail.vue        // 账号详情页面
│   │   ├── ChatList.vue             // 聊天列表页面
│   │   ├── ChatWindow.vue           // 聊天窗口页面
│   │   ├── ContactList.vue          // 联系人列表页面
│   │   └── MessageSearch.vue        // 消息搜索页面
│   └── components/
│       ├── WhatsAppAccountCard.vue  // 账号卡片组件
│       ├── ChatItem.vue             // 聊天项组件
│       ├── MessageItem.vue          // 消息项组件
│       ├── QRCodeModal.vue          // 二维码弹窗组件
│       └── MessageInput.vue         // 消息输入组件
```

#### 4.2 状态管理 (Pinia)

```typescript
// stores/whatsapp.ts
interface WhatsAppStore {
    // 账号管理
    accounts: WhatsAppAccount[]
    currentAccount: WhatsAppAccount | null
    
    // 聊天管理
    chats: WhatsAppChat[]
    currentChat: WhatsAppChat | null
    
    // 消息管理
    messages: WhatsAppMessage[]
    unreadCount: number
    
    // 实时通信
    wsConnected: boolean
    
    // 操作方法
    fetchAccounts(): Promise<void>
    createAccount(account: Partial<WhatsAppAccount>): Promise<void>
    loginAccount(accountId: number): Promise<void>
    fetchChats(accountId: number): Promise<void>
    fetchMessages(accountId: number, chatId: string): Promise<void>
    sendMessage(accountId: number, to: string, message: string): Promise<void>
    searchMessages(accountId: number, query: string): Promise<void>
}
```

#### 4.3 组件设计

**账号列表组件 (AccountList.vue)**
```vue
<template>
  <div class="account-list">
    <div class="header">
      <h2>WhatsApp 账号管理</h2>
      <el-button @click="showCreateModal">添加账号</el-button>
    </div>
    
    <div class="account-grid">
      <AccountCard 
        v-for="account in accounts" 
        :key="account.id" 
        :account="account"
        @login="loginAccount"
        @logout="logoutAccount"
        @delete="deleteAccount"
      />
    </div>
    
    <CreateAccountModal 
      v-model="showCreate"
      @created="onAccountCreated"
    />
  </div>
</template>
```

**聊天窗口组件 (ChatWindow.vue)**
```vue
<template>
  <div class="chat-window">
    <div class="chat-header">
      <h3>{{ currentChat?.name }}</h3>
      <div class="chat-actions">
        <el-button @click="showContactInfo">联系人信息</el-button>
        <el-button @click="showChatStats">聊天统计</el-button>
      </div>
    </div>
    
    <div class="message-list" ref="messageList">
      <MessageItem 
        v-for="message in messages" 
        :key="message.id" 
        :message="message"
      />
    </div>
    
    <MessageInput 
      @send="sendMessage"
      :disabled="!currentAccount?.connected"
    />
  </div>
</template>
```

### 5. 数据存储策略

#### 5.1 持久化数据

**需要持久化的数据**
- 账号基本信息（手机号、名称、状态等）
- 聊天记录和消息历史
- 联系人信息
- 用户权限和配置

**不需要持久化的数据**
- 二维码（临时使用，扫码后立即清除）
- 实时会话状态（通过内存缓存）
- 临时认证数据（WhatsApp Web.js 自动管理）

#### 5.2 缓存策略

**Go 后端缓存**
```go
// 内存缓存
type Cache struct {
    accounts map[uint]*WhatsAppAccount
    chats    map[string]*WhatsAppChat
    messages map[string][]*WhatsAppMessage
    // 注意：不缓存二维码，二维码只在内存中临时存在
    mutex    sync.RWMutex
}

// 缓存更新策略
func (c *Cache) UpdateAccount(account *WhatsAppAccount) {
    c.mutex.Lock()
    defer c.mutex.Unlock()
    c.accounts[account.ID] = account
}
```

**前端缓存**
```typescript
// 本地存储
interface LocalStorage {
    accounts: WhatsAppAccount[]
    chats: Record<string, WhatsAppChat[]>
    messages: Record<string, WhatsAppMessage[]>
    lastSync: Record<string, number>
    // 注意：不存储二维码，二维码只在内存中临时显示
}
```

#### 5.2 同步策略

**增量同步**
1. 前端记录最后同步时间
2. 请求时携带时间戳
3. 后端只返回变更数据
4. 前端合并更新本地数据

**全量同步**
1. 定期进行全量数据同步
2. 清理过期缓存数据
3. 确保数据一致性

### 6. 错误处理和监控

#### 6.1 错误处理策略

**Go 后端错误处理**
```go
// 统一错误响应
type ErrorResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

// 错误分类
const (
    ErrAccountNotFound    = "ACCOUNT_NOT_FOUND"
    ErrSessionExpired     = "SESSION_EXPIRED"
    ErrWhatsAppAPIError   = "WHATSAPP_API_ERROR"
    ErrDatabaseError      = "DATABASE_ERROR"
    ErrNetworkError       = "NETWORK_ERROR"
)
```

**前端错误处理**
```typescript
// 全局错误处理
class ErrorHandler {
    static handle(error: any) {
        if (error.code === 'SESSION_EXPIRED') {
            // 重新登录
            this.reconnectAccount()
        } else if (error.code === 'WHATSAPP_API_ERROR') {
            // 重试操作
            this.retryOperation()
        }
    }
}
```

#### 6.2 监控和日志

**Go 后端监控**
```go
// 性能监控
type Metrics struct {
    RequestCount    int64
    ResponseTime    time.Duration
    ErrorCount      int64
    ActiveAccounts  int64
}

// 日志记录
func logWhatsAppOperation(operation string, accountID uint, details map[string]interface{}) {
    log.WithFields(log.Fields{
        "operation": operation,
        "account_id": accountID,
        "details": details,
    }).Info("WhatsApp operation")
}
```

### 7. 安全考虑

#### 7.1 认证和授权

**JWT Token 验证**
```go
// 中间件验证
func authMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(401, ErrorResponse{Code: 401, Message: "未授权访问"})
            c.Abort()
            return
        }
        
        claims, err := validateToken(token)
        if err != nil {
            c.JSON(401, ErrorResponse{Code: 401, Message: "Token 无效"})
            c.Abort()
            return
        }
        
        c.Set("user_id", claims.UserID)
        c.Next()
    }
}
```

**权限控制**
```go
// 账号权限检查
func checkAccountPermission(userID, accountID uint) bool {
    var account WhatsAppAccount
    if err := db.Where("id = ? AND user_id = ?", accountID, userID).First(&account).Error; err != nil {
        return false
    }
    return true
}
```

#### 7.2 数据安全

**敏感数据加密**
```go
// 手机号加密存储
func encryptPhoneNumber(phone string) string {
    // 使用 AES 加密
    return encrypt(phone, secretKey)
}

// 会话数据保护
func protectSessionData(sessionID string) {
    // 设置访问权限
    // 定期清理过期数据
}
```

### 8. 部署和运维

#### 8.1 部署架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Nginx     │    │   Docker    │    │   Docker    │
│  Reverse    │───►│   Vue3      │    │   Go        │
│   Proxy     │    │  Frontend   │    │  Backend    │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
                                       ┌─────────────┐    ┌─────────────┐
                                       │   Docker    │    │   Docker    │
                                       │  Node.js    │    │  SQLite     │
                                       │WhatsApp API │    │  Database   │
                                       └─────────────┘    └─────────────┘
```

#### 8.2 环境配置

**Docker Compose**
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
      
  backend:
    build: ./backend
    ports:
      - "8080:8080"
    environment:
      - DB_PATH=/data/hive.db
      - WHATSAPP_SERVICE_URL=http://whatsapp-service:3000
    volumes:
      - ./data:/data
    depends_on:
      - whatsapp-service
      
  whatsapp-service:
    build: ./whatsapp-service
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./whatsapp-data:/app/data
```

### 9. 测试策略

#### 9.1 单元测试

**Go 后端测试**
```go
func TestWhatsAppAccountCreation(t *testing.T) {
    // 测试账号创建
    account := WhatsAppAccount{
        AccountName: "test_account",
        PhoneNumber: "**********",
    }
    
    result := createWhatsAppAccount(account)
    assert.Equal(t, "success", result.Status)
}
```

**前端组件测试**
```typescript
import { mount } from '@vue/test-utils'
import AccountCard from '@/components/AccountCard.vue'

describe('AccountCard', () => {
    it('should display account information', () => {
        const wrapper = mount(AccountCard, {
            props: {
                account: mockAccount
            }
        })
        
        expect(wrapper.find('.account-name').text()).toBe(mockAccount.name)
    })
})
```

#### 9.2 集成测试

**API 集成测试**
```go
func TestWhatsAppIntegration(t *testing.T) {
    // 测试完整的 WhatsApp 操作流程
    // 1. 创建账号
    // 2. 登录账号
    // 3. 获取聊天列表
    // 4. 发送消息
    // 5. 验证结果
}
```

### 10. 性能优化

#### 10.1 数据库优化

**索引优化**
```sql
-- 为常用查询添加索引
CREATE INDEX idx_whatsapp_accounts_user_id ON whatsapp_accounts(user_id);
CREATE INDEX idx_whatsapp_messages_account_chat ON whatsapp_messages(account_id, chat_id);
CREATE INDEX idx_whatsapp_messages_timestamp ON whatsapp_messages(timestamp);
```

**查询优化**
```go
// 使用预加载减少查询次数
func getAccountWithChats(accountID uint) (*WhatsAppAccount, error) {
    var account WhatsAppAccount
    err := db.Preload("Chats").Preload("Chats.Messages").First(&account, accountID).Error
    return &account, err
}
```

#### 10.2 前端优化

**虚拟滚动**
```vue
<template>
  <VirtualList
    :items="messages"
    :item-height="80"
    :container-height="400"
  >
    <template #default="{ item }">
      <MessageItem :message="item" />
    </template>
  </VirtualList>
</template>
```

**懒加载**
```typescript
// 聊天列表懒加载
const loadMoreChats = async () => {
    if (loading.value) return
    
    loading.value = true
    const newChats = await fetchChats(currentPage.value)
    chats.value.push(...newChats)
    currentPage.value++
    loading.value = false
}
```

## 📝 实施计划

### 阶段一：基础架构搭建 (1-2周)
1. 完善 Go 后端 WhatsApp API
2. 建立与 Node.js 服务的通信
3. 设计数据库模型
4. 实现基础的前端页面

### 阶段二：核心功能实现 (2-3周)
1. 实现账号管理功能
2. 实现聊天记录获取
3. 实现消息发送功能
4. 实现实时通信

### 阶段三：界面优化 (1-2周)
1. 完善前端界面设计
2. 实现响应式布局
3. 优化用户体验
4. 添加动画效果

### 阶段四：测试和部署 (1周)
1. 单元测试和集成测试
2. 性能优化
3. 安全加固
4. 部署上线

## 🎯 成功指标

1. **功能完整性**：100% 实现 phone-login.html 和 chat-history.html 的功能
2. **性能指标**：消息发送延迟 < 2秒，页面加载时间 < 3秒
3. **稳定性**：系统可用性 > 99.5%，错误率 < 0.1%
4. **用户体验**：界面响应流畅，操作简单直观
5. **安全性**：通过安全审计，无高危漏洞

这个设计方案提供了一个完整的 WhatsApp 集成解决方案，确保前端、Go 后端和 Node.js 服务之间的良好协作，同时保证了系统的可扩展性、安全性和性能。 