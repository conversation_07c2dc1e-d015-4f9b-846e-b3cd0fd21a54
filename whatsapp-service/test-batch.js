const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');

// 创建WhatsApp客户端
const client = new Client({
    authStrategy: new LocalAuth({
        clientId: 'batch-message-test'
    }),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu'
        ]
    }
});

// 生成二维码
client.on('qr', (qr) => {
    console.log('请扫描以下二维码登录WhatsApp:');
    qrcode.generate(qr, { small: true });
});

// 客户端准备就绪
client.on('ready', () => {
    console.log('✅ WhatsApp客户端已准备就绪!');
    console.log(`📱 登录账号: ${client.info.wid.user}`);
    console.log(`👤 显示名称: ${client.info.pushname}`);
    
    // 测试发送消息
    testSendMessage();
});

// 认证失败
client.on('auth_failure', (msg) => {
    console.error('❌ 认证失败:', msg);
});

// 断开连接
client.on('disconnected', (reason) => {
    console.log('🔌 客户端断开连接:', reason);
});

// 测试发送消息
async function testSendMessage() {
    try {
        console.log('\n🧪 开始测试消息发送...');
        
        // 测试发送给自己
        const ownNumber = client.info.wid.user;
        const testMessage = `🧪 测试消息 - ${new Date().toLocaleString()}`;
        
        console.log(`📤 发送测试消息到: ${ownNumber}`);
        await client.sendMessage(`${ownNumber}@c.us`, testMessage);
        console.log('✅ 测试消息发送成功!');
        
        // 批量发送测试
        await testBatchMessages();
        
    } catch (error) {
        console.error('❌ 发送消息失败:', error);
    }
}

// 批量发送测试
async function testBatchMessages() {
    try {
        console.log('\n📦 开始批量发送测试...');
        
        const ownNumber = client.info.wid.user;
        const messages = [
            '📦 批量消息测试 1',
            '📦 批量消息测试 2', 
            '📦 批量消息测试 3',
            '📦 批量消息测试 4',
            '📦 批量消息测试 5'
        ];
        
        console.log(`📤 准备发送 ${messages.length} 条消息到: ${ownNumber}`);
        
        for (let i = 0; i < messages.length; i++) {
            const message = `${messages[i]} - ${new Date().toLocaleString()}`;
            console.log(`📤 发送第 ${i + 1} 条消息...`);
            
            await client.sendMessage(`${ownNumber}@c.us`, message);
            
            // 添加延迟避免被限制
            if (i < messages.length - 1) {
                console.log('⏳ 等待 2 秒...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        console.log('✅ 批量发送测试完成!');
        
    } catch (error) {
        console.error('❌ 批量发送失败:', error);
    }
}

// 接收消息
client.on('message', (msg) => {
    console.log(`📥 收到消息: ${msg.from} - ${msg.body}`);
});

// 初始化客户端
console.log('🚀 启动WhatsApp客户端...');
client.initialize();

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n🛑 正在关闭客户端...');
    await client.destroy();
    process.exit(0);
}); 