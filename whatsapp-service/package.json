{"name": "whatsapp-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "@types/qrcode": "^1.5.5", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "puppeteer": "^24.15.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "socket.io": "^4.8.1", "whatsapp-web.js": "^1.31.0"}}