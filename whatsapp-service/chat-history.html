<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐝 Hive WhatsApp 聊天记录</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #075E54;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 4px solid #25D366;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            margin: 5px;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #075E54;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
        }
        .chat-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
        }
        .chat-item {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .chat-item:hover {
            background-color: #f8f9fa;
        }
        .chat-item.selected {
            background-color: #e3f2fd;
            border-left: 4px solid #25D366;
        }
        .chat-name {
            font-weight: 600;
            color: #075E54;
        }
        .chat-last-message {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .message-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
        }
        .message-item {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
        }
        .message-item.from-me {
            background-color: #dcf8c6;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .message-sender {
            font-weight: 600;
            color: #075E54;
        }
        .message-time {
            color: #666;
            font-size: 0.8em;
        }
        .message-body {
            color: #333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e1e5e9;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: 600;
            color: #25D366;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐝 Hive WhatsApp 聊天记录</h1>
            <p>获取和管理WhatsApp聊天记录</p>
        </div>

        <!-- 会话选择 -->
        <div class="section">
            <h3>📱 会话管理</h3>
            <div class="input-group">
                <label for="sessionId">会话ID:</label>
                <input type="text" id="sessionId" placeholder="输入会话ID或留空使用当前会话">
            </div>
            <button class="btn" onclick="getCurrentSession()">获取当前会话</button>
            <button class="btn" onclick="getAllSessions()">获取所有会话</button>
        </div>

        <!-- 聊天列表 -->
        <div class="section">
            <h3>💬 聊天列表</h3>
            <div class="input-group">
                <label for="chatLimit">聊天数量限制:</label>
                <input type="number" id="chatLimit" value="20" min="1" max="100">
            </div>
            <button class="btn" onclick="getChats()">获取聊天列表</button>
            <div id="chatList" class="chat-list"></div>
        </div>

        <!-- 消息记录 -->
        <div class="section">
            <h3>📨 消息记录</h3>
            <div class="input-group">
                <label for="selectedChatId">选中的聊天ID:</label>
                <input type="text" id="selectedChatId" readonly>
            </div>
            <div class="input-group">
                <label for="messageLimit">消息数量限制:</label>
                <input type="number" id="messageLimit" value="50" min="1" max="200">
            </div>
            <div class="input-group">
                <label for="messageFilter">消息过滤:</label>
                <select id="messageFilter">
                    <option value="">全部消息</option>
                    <option value="true">我发送的</option>
                    <option value="false">收到的消息</option>
                </select>
            </div>
            <button class="btn" onclick="getMessages()">获取消息记录</button>
            <button class="btn" onclick="getChatStats()">获取聊天统计</button>
            <div id="messageList" class="message-list"></div>
        </div>

        <!-- 搜索消息 -->
        <div class="section">
            <h3>🔍 搜索消息</h3>
            <div class="input-group">
                <label for="searchQuery">搜索关键词:</label>
                <input type="text" id="searchQuery" placeholder="输入搜索关键词">
            </div>
            <button class="btn" onclick="searchMessages()">搜索消息</button>
            <div id="searchResults" class="message-list"></div>
        </div>

        <!-- 联系人 -->
        <div class="section">
            <h3>👥 联系人</h3>
            <button class="btn" onclick="getContacts()">获取联系人列表</button>
            <div id="contactList" class="chat-list"></div>
        </div>

        <!-- 统计信息 -->
        <div class="section">
            <h3>📊 聊天统计</h3>
            <div id="chatStats" class="stats-grid"></div>
        </div>

        <!-- 日志 -->
        <div class="section">
            <h3>📝 操作日志</h3>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/whatsapp'
        let currentSessionId = null

        function log(message) {
            const logElement = document.getElementById('log')
            const timestamp = new Date().toLocaleTimeString()
            logElement.innerHTML += `[${timestamp}] ${message}\n`
            logElement.scrollTop = logElement.scrollHeight
        }

        function clearLog() {
            document.getElementById('log').innerHTML = ''
        }

        async function getCurrentSession() {
            try {
                const response = await fetch(`${API_BASE}/sessions`)
                const result = await response.json()
                
                if (result.success && result.data.length > 0) {
                    const session = result.data[0]
                    currentSessionId = session.id
                    document.getElementById('sessionId').value = session.id
                    log(`当前会话: ${session.id} (${session.status})`)
                } else {
                    log('没有找到可用会话')
                }
            } catch (error) {
                log(`获取会话失败: ${error.message}`)
            }
        }

        async function getAllSessions() {
            try {
                const response = await fetch(`${API_BASE}/sessions`)
                const result = await response.json()
                
                if (result.success) {
                    log(`找到 ${result.data.length} 个会话:`)
                    result.data.forEach(session => {
                        log(`- ${session.id}: ${session.status} (${session.phoneNumber || '未知号码'})`)
                    })
                } else {
                    log(`获取会话失败: ${result.message}`)
                }
            } catch (error) {
                log(`获取会话失败: ${error.message}`)
            }
        }

        async function getChats() {
            const sessionId = document.getElementById('sessionId').value || currentSessionId
            const limit = document.getElementById('chatLimit').value

            if (!sessionId) {
                log('请先选择会话')
                return
            }

            try {
                const response = await fetch(`${API_BASE}/sessions/${sessionId}/chats?limit=${limit}`)
                const result = await response.json()
                
                if (result.success) {
                    const chatList = document.getElementById('chatList')
                    chatList.innerHTML = ''
                    
                    result.data.forEach(chat => {
                        const chatItem = document.createElement('div')
                        chatItem.className = 'chat-item'
                        chatItem.onclick = () => selectChat(chat.id, chat.name)
                        
                        chatItem.innerHTML = `
                            <div class="chat-name">${chat.name || chat.id}</div>
                            <div class="chat-last-message">
                                ${chat.lastMessage ? chat.lastMessage.body.substring(0, 50) + '...' : '无消息'}
                                <br>
                                <small>未读: ${chat.unreadCount} | ${chat.isGroup ? '群聊' : '私聊'}</small>
                            </div>
                        `
                        chatList.appendChild(chatItem)
                    })
                    
                    log(`获取到 ${result.data.length} 个聊天`)
                } else {
                    log(`获取聊天失败: ${result.message}`)
                }
            } catch (error) {
                log(`获取聊天失败: ${error.message}`)
            }
        }

        function selectChat(chatId, chatName) {
            document.getElementById('selectedChatId').value = chatId
            document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('selected'))
            event.target.closest('.chat-item').classList.add('selected')
            log(`选中聊天: ${chatName} (${chatId})`)
        }

        async function getMessages() {
            const sessionId = document.getElementById('sessionId').value || currentSessionId
            const chatId = document.getElementById('selectedChatId').value
            const limit = document.getElementById('messageLimit').value
            const filter = document.getElementById('messageFilter').value

            if (!sessionId || !chatId) {
                log('请先选择会话和聊天')
                return
            }

            try {
                let url = `${API_BASE}/sessions/${sessionId}/chats/${chatId}/messages?limit=${limit}`
                if (filter) {
                    url += `&fromMe=${filter}`
                }

                const response = await fetch(url)
                const result = await response.json()
                
                if (result.success) {
                    const messageList = document.getElementById('messageList')
                    messageList.innerHTML = ''
                    
                    result.data.forEach(message => {
                        const messageItem = document.createElement('div')
                        messageItem.className = `message-item ${message.fromMe ? 'from-me' : ''}`
                        
                        const timestamp = new Date(message.timestamp * 1000).toLocaleString()
                        
                        messageItem.innerHTML = `
                            <div class="message-header">
                                <span class="message-sender">${message.fromMe ? '我' : message.from}</span>
                                <span class="message-time">${timestamp}</span>
                            </div>
                            <div class="message-body">${message.body}</div>
                        `
                        messageList.appendChild(messageItem)
                    })
                    
                    log(`获取到 ${result.data.length} 条消息`)
                } else {
                    log(`获取消息失败: ${result.message}`)
                }
            } catch (error) {
                log(`获取消息失败: ${error.message}`)
            }
        }

        async function searchMessages() {
            const sessionId = document.getElementById('sessionId').value || currentSessionId
            const query = document.getElementById('searchQuery').value

            if (!sessionId || !query) {
                log('请先选择会话并输入搜索关键词')
                return
            }

            try {
                const response = await fetch(`${API_BASE}/sessions/${sessionId}/search-messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query })
                })
                const result = await response.json()
                
                if (result.success) {
                    const searchResults = document.getElementById('searchResults')
                    searchResults.innerHTML = ''
                    
                    result.data.forEach(message => {
                        const messageItem = document.createElement('div')
                        messageItem.className = `message-item ${message.fromMe ? 'from-me' : ''}`
                        
                        const timestamp = new Date(message.timestamp * 1000).toLocaleString()
                        
                        messageItem.innerHTML = `
                            <div class="message-header">
                                <span class="message-sender">${message.chatName || message.chatId} - ${message.fromMe ? '我' : message.from}</span>
                                <span class="message-time">${timestamp}</span>
                            </div>
                            <div class="message-body">${message.body}</div>
                        `
                        searchResults.appendChild(messageItem)
                    })
                    
                    log(`搜索到 ${result.data.length} 条消息`)
                } else {
                    log(`搜索消息失败: ${result.message}`)
                }
            } catch (error) {
                log(`搜索消息失败: ${error.message}`)
            }
        }

        async function getContacts() {
            const sessionId = document.getElementById('sessionId').value || currentSessionId

            if (!sessionId) {
                log('请先选择会话')
                return
            }

            try {
                const response = await fetch(`${API_BASE}/sessions/${sessionId}/contacts`)
                const result = await response.json()
                
                if (result.success) {
                    const contactList = document.getElementById('contactList')
                    contactList.innerHTML = ''
                    
                    result.data.forEach(contact => {
                        const contactItem = document.createElement('div')
                        contactItem.className = 'chat-item'
                        
                        contactItem.innerHTML = `
                            <div class="chat-name">${contact.name || contact.pushname || contact.number}</div>
                            <div class="chat-last-message">
                                <small>${contact.number} | ${contact.isBusiness ? '企业' : '个人'}</small>
                            </div>
                        `
                        contactList.appendChild(contactItem)
                    })
                    
                    log(`获取到 ${result.data.length} 个联系人`)
                } else {
                    log(`获取联系人失败: ${result.message}`)
                }
            } catch (error) {
                log(`获取联系人失败: ${error.message}`)
            }
        }

        async function getChatStats() {
            const sessionId = document.getElementById('sessionId').value || currentSessionId
            const chatId = document.getElementById('selectedChatId').value

            if (!sessionId || !chatId) {
                log('请先选择会话和聊天')
                return
            }

            try {
                const response = await fetch(`${API_BASE}/sessions/${sessionId}/chats/${chatId}/stats`)
                const result = await response.json()
                
                if (result.success) {
                    const stats = result.data
                    const statsContainer = document.getElementById('chatStats')
                    statsContainer.innerHTML = ''
                    
                    const statsData = [
                        { label: '总消息数', value: stats.totalMessages },
                        { label: '我发送的', value: stats.fromMeCount },
                        { label: '收到的', value: stats.fromOthersCount },
                        { label: '媒体消息', value: stats.mediaMessages },
                        { label: '文本消息', value: stats.textMessages },
                        { label: '未读消息', value: stats.unreadCount }
                    ]
                    
                    statsData.forEach(stat => {
                        const statItem = document.createElement('div')
                        statItem.className = 'stat-item'
                        statItem.innerHTML = `
                            <div class="stat-value">${stat.value}</div>
                            <div class="stat-label">${stat.label}</div>
                        `
                        statsContainer.appendChild(statItem)
                    })
                    
                    log(`获取聊天统计成功`)
                } else {
                    log(`获取聊天统计失败: ${result.message}`)
                }
            } catch (error) {
                log(`获取聊天统计失败: ${error.message}`)
            }
        }

        // 页面加载时自动获取当前会话
        window.onload = function() {
            log('页面加载完成，正在获取当前会话...')
            getCurrentSession()
        }
    </script>
</body>
</html> 