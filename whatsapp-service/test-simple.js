const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');

console.log('🚀 启动WhatsApp简单测试...');

// 创建WhatsApp客户端
const client = new Client({
    authStrategy: new LocalAuth({
        clientId: 'simple-test'
    }),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu'
        ]
    }
});

// 二维码事件
client.on('qr', (qr) => {
    console.log('📱 请扫描以下二维码登录:');
    qrcode.generate(qr, { small: true });
});

// 认证成功事件
client.on('authenticated', () => {
    console.log('✅ 认证成功!');
});

// 客户端准备就绪
client.on('ready', () => {
    console.log('🎉 WhatsApp客户端已准备就绪!');
    console.log(`📱 登录账号: ${client.info.wid.user}`);
    console.log(`👤 显示名称: ${client.info.pushname}`);
    
    // 测试发送消息给自己
    testSendMessage();
});

// 认证失败事件
client.on('auth_failure', (msg) => {
    console.error('❌ 认证失败:', msg);
});

// 断开连接事件
client.on('disconnected', (reason) => {
    console.log('🔌 客户端断开连接:', reason);
});

// 测试发送消息
async function testSendMessage() {
    try {
        console.log('\n🧪 开始测试消息发送...');
        
        const ownNumber = client.info.wid.user;
        const testMessage = `🧪 测试消息 - ${new Date().toLocaleString()}`;
        
        console.log(`📤 发送测试消息到: ${ownNumber}@c.us`);
        console.log(`📝 消息内容: ${testMessage}`);
        
        await client.sendMessage(`${ownNumber}@c.us`, testMessage);
        console.log('✅ 测试消息发送成功!');
        
        // 等待5秒后关闭
        setTimeout(() => {
            console.log('🔌 关闭客户端...');
            client.destroy();
            process.exit(0);
        }, 5000);
        
    } catch (error) {
        console.error('❌ 发送消息失败:', error);
        client.destroy();
        process.exit(1);
    }
}

// 初始化客户端
console.log('🔄 初始化WhatsApp客户端...');
client.initialize();

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n🔄 正在关闭...');
    await client.destroy();
    process.exit(0);
}); 