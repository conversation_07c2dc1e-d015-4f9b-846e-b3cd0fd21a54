# WhatsApp Service

WhatsApp客户营销系统的Node.js服务端，负责处理WhatsApp账号的登录、消息发送等功能。

## 功能特性

- ✅ WhatsApp QR码扫码登录
- ✅ 多会话管理
- ✅ 实时状态监控
- ✅ 消息发送功能
- ✅ RESTful API接口
- ✅ WebSocket实时通信

## 技术栈

- **Node.js** - 运行时环境
- **TypeScript** - 开发语言
- **Express** - Web框架
- **Socket.IO** - 实时通信
- **whatsapp-web.js** - WhatsApp Web API
- **Puppeteer** - 浏览器自动化
- **QRCode** - 二维码生成

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
```

### 生产构建

```bash
pnpm build
pnpm start
```

## API接口

### 会话管理

#### 创建会话
```http
POST /api/whatsapp/sessions
Content-Type: application/json

{
  "sessionId": "unique-session-id"
}
```

#### 获取会话状态
```http
GET /api/whatsapp/sessions/{sessionId}
```

#### 获取所有会话
```http
GET /api/whatsapp/sessions
```

#### 断开会话
```http
DELETE /api/whatsapp/sessions/{sessionId}
```

#### 获取客户端信息
```http
GET /api/whatsapp/sessions/{sessionId}/info
```

### 消息发送

#### 发送消息
```http
POST /api/whatsapp/sessions/{sessionId}/messages
Content-Type: application/json

{
  "to": "1234567890",
  "message": "Hello World!"
}
```

## 测试页面

项目包含一个测试页面 `test-qr.html`，可以用来测试QR码登录功能：

1. 启动服务：`pnpm dev`
2. 打开浏览器访问：`file:///path/to/whatsapp-service/test-qr.html`
3. 输入会话ID并点击"创建会话"
4. 扫描生成的QR码完成WhatsApp登录

## 配置说明

配置文件：`config.env`

```env
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_PATH=../database/hive.db

# WhatsApp配置
WHATSAPP_SESSION_DIR=./sessions
WHATSAPP_DATA_DIR=./data

# 日志配置
LOG_LEVEL=debug
```

## 项目结构

```
src/
├── config/          # 配置文件
├── controllers/     # 控制器
├── routes/          # 路由
├── services/        # 业务逻辑
└── index.ts         # 入口文件
```

## 开发说明

### 会话状态

- `disconnected` - 未连接
- `connecting` - 连接中（等待扫码）
- `connected` - 已连接
- `error` - 连接错误
- `banned` - 账号被封
- `offline` - 离线

### 事件监听

服务支持以下事件：

- `client:ready` - 客户端准备就绪
- `client:qr` - 收到QR码
- `client:auth_failure` - 认证失败
- `client:disconnected` - 断开连接
- `client:message` - 收到消息
- `client:error` - 发生错误

## 注意事项

1. 首次使用需要扫码登录WhatsApp
2. 登录成功后会话会自动保存，下次无需重新扫码
3. 建议在生产环境中使用无头模式（headless: true）
4. 注意遵守WhatsApp的使用条款和政策

## 故障排除

### Chrome启动失败
如果遇到Chrome启动问题，可以：

1. 确保系统已安装Chrome浏览器
2. 修改 `src/services/WhatsAppService.ts` 中的 `executablePath`
3. 尝试使用不同的Chrome启动参数

### 连接超时
如果连接超时，可以：

1. 检查网络连接
2. 增加超时时间
3. 检查防火墙设置

## 许可证

ISC 