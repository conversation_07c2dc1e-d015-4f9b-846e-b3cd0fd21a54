<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hive WhatsApp 单账号测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f0f2f5 0%, #e0e5ec 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .container {
            background-color: #ffffff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 90%;
            max-width: 800px;
            box-sizing: border-box;
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            color: #25D366;
            margin-bottom: 10px;
            font-size: 2.2em;
        }
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        .section {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
        }
        .section h3 {
            color: #555;
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #444;
        }
        .form-group input[type="text"],
        .form-group textarea {
            width: calc(100% - 20px);
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        .form-group input[type="text"]:focus,
        .form-group textarea:focus {
            border-color: #25D366;
            outline: none;
        }
        .form-group small {
            display: block;
            margin-top: 5px;
            color: #888;
            font-size: 0.9em;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: background-color 0.3s, transform 0.2s;
            flex-grow: 1;
            min-width: 120px;
        }
        .btn-primary {
            background-color: #25D366;
            color: white;
        }
        .btn-primary:hover:not(:disabled) {
            background-color: #1da851;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #075E54;
            color: white;
        }
        .btn-secondary:hover:not(:disabled) {
            background-color: #054a42;
            transform: translateY(-2px);
        }
        .btn-warning {
            background-color: #ffc107;
            color: #333;
        }
        .btn-warning:hover:not(:disabled) {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover:not(:disabled) {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            opacity: 0.7;
        }
        .status-display {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            transition: background-color 0.3s, color 0.3s;
        }
        .status-display.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status-display.connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-display.disconnected, .status-display.offline {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .status-display.error, .status-display.banned {
            background-color: #f8d7da;
            color: #721c24;
        }
        .qr-container {
            text-align: center;
            margin-top: 20px;
        }
        .qr-code {
            max-width: 250px;
            border: 5px solid #25D366;
            border-radius: 10px;
            padding: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .log {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 250px;
            overflow-y: auto;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9em;
            line-height: 1.6;
            color: #333;
        }
        .log-item {
            padding: 5px 0;
            border-bottom: 1px dashed #ccc;
        }
        .log-item:last-child {
            border-bottom: none;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }

        @media (min-width: 768px) {
            .container {
                grid-template-columns: 1fr 1fr;
            }
            .section:nth-child(1) { /* Account Management */
                grid-column: 1 / 2;
            }
            .section:nth-child(2) { /* Message Sending */
                grid-column: 2 / 3;
            }
            .section:nth-child(3) { /* Log */
                grid-column: 1 / 3;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐝 Hive WhatsApp 单账号测试</h1>
            <p>专注于单个WhatsApp账号的登录、离线、重连、信息获取和消息发送功能</p>
        </div>

        <!-- 账号管理 -->
        <div class="section">
            <h3>📱 账号管理</h3>
            <div class="form-group">
                <label>当前会话ID:</label>
                <span id="currentSessionIdDisplay" style="font-weight: bold; color: #075E54;">无</span>
            </div>
            <div class="form-group">
                <label>账号手机号:</label>
                <span id="accountPhoneNumber" style="font-weight: bold;">N/A</span>
            </div>
            <div class="form-group">
                <label>账号昵称:</label>
                <span id="accountNickname" style="font-weight: bold;">N/A</span>
            </div>
            <div class="form-group">
                <label>账号状态:</label>
                <span id="accountStatus" class="status-display disconnected">未连接</span>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="createAccount()">创建新账号</button>
                <button class="btn btn-secondary" onclick="getSessionStatus()">获取状态</button>
                <button class="btn btn-warning" onclick="disconnectAccount()">离线</button>
                <button class="btn btn-primary" onclick="reconnectAccount()">重连</button>
                <button class="btn btn-info" onclick="checkAuthStatus()">检查认证状态</button>
            </div>
            <div id="qrContainer" class="qr-container" style="display: none;">
                <h3>扫描二维码登录WhatsApp</h3>
                <img id="qrCode" class="qr-code" alt="QR Code">
            </div>
        </div>

        <!-- 消息发送 -->
        <div class="section">
            <h3>💬 消息发送</h3>
            <div class="form-group">
                <label for="messageText">消息内容:</label>
                <textarea id="messageText" rows="3" placeholder="输入要发送的消息内容">hello</textarea>
            </div>
            
            <div class="form-group">
                <label for="recipientNumber">接收号码:</label>
                <input type="text" id="recipientNumber" placeholder="输入手机号（支持多种格式）">
                <small>留空则发送给自己。支持格式：+86 13800138000、13800138000、+852 12345678 等</small>
            </div>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="sendMessage()">发送消息</button>
            </div>
        </div>
        
        <!-- 日志 -->
        <div class="section">
            <h3>📋 操作日志</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/whatsapp';
        let currentSessionId = null;
        let pollingInterval = null;

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = `log-item log-${type}`;
            logItem.innerHTML = `[${timestamp}] ${message}`;
            logDiv.prepend(logItem); // Add to top
            if (logDiv.children.length > 50) { // Keep log clean
                logDiv.removeChild(logDiv.lastChild);
            }
        }

        // 更新UI状态
        function updateUI(session) {
            const sessionIdDisplay = document.getElementById('currentSessionIdDisplay');
            const phoneNumberDisplay = document.getElementById('accountPhoneNumber');
            const nicknameDisplay = document.getElementById('accountNickname');
            const statusDisplay = document.getElementById('accountStatus');
            const qrContainer = document.getElementById('qrContainer');
            const qrCodeImg = document.getElementById('qrCode');

            sessionIdDisplay.textContent = session.id || '无';
            phoneNumberDisplay.textContent = session.phoneNumber || 'N/A';
            nicknameDisplay.textContent = session.nickname || 'N/A';
            statusDisplay.textContent = getStatusText(session.status);
            statusDisplay.className = `status-display ${session.status}`;

            if (session.qrCode) {
                qrCodeImg.src = session.qrCode;
                qrContainer.style.display = 'block';
                log('请扫描二维码登录', 'warning');
            } else {
                qrContainer.style.display = 'none';
                qrCodeImg.src = '';
            }

            // Enable/disable buttons based on status
            const isConnected = session.status === 'connected';
            document.querySelector('button[onclick="sendMessage()"]').disabled = !isConnected;
            document.querySelector('button[onclick="disconnectAccount()"]').disabled = !isConnected;
            document.querySelector('button[onclick="reconnectAccount()"]').disabled = isConnected;
            document.querySelector('button[onclick="createAccount()"]').disabled = !!currentSessionId;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'connected': '已连接',
                'connecting': '连接中',
                'disconnected': '未连接',
                'offline': '离线',
                'error': '错误',
                'banned': '被封'
            };
            return statusMap[status] || status;
        }

        // 轮询会话状态
        function startPollingSessionStatus(sessionId) {
            if (pollingInterval) clearInterval(pollingInterval);
            pollingInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/sessions/${sessionId}`);
                    const result = await response.json();
                    if (result.success && result.data) {
                        updateUI(result.data);
                        if (result.data.status === 'connected') {
                            log(`账号 ${sessionId} 已连接`, 'success');
                            clearInterval(pollingInterval);
                        } else if (result.data.status === 'disconnected' || result.data.status === 'error' || result.data.status === 'banned') {
                            log(`账号 ${sessionId} 状态: ${getStatusText(result.data.status)}`, 'error');
                        }
                    } else {
                        log(`获取会话 ${sessionId} 状态失败: ${result.message}`, 'error');
                        clearInterval(pollingInterval);
                        currentSessionId = null;
                        updateUI({ id: null, status: 'disconnected' });
                    }
                } catch (error) {
                    log(`轮询会话 ${sessionId} 错误: ${error.message}`, 'error');
                    clearInterval(pollingInterval);
                    currentSessionId = null;
                    updateUI({ id: null, status: 'disconnected' });
                }
            }, 5000);
        }

        // 创建账号
        async function createAccount() {
            log('创建新WhatsApp账号...', 'info');
            try {
                const response = await fetch(`${API_BASE}/sessions`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                const result = await response.json();

                if (result.success) {
                    currentSessionId = result.data.id;
                    log(`✅ 账号创建成功: ${currentSessionId}`, 'success');
                    updateUI(result.data);
                    startPollingSessionStatus(currentSessionId);
                } else {
                    log(`❌ 账号创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求错误: ${error.message}`, 'error');
            }
        }

        // 获取会话状态
        async function getSessionStatus() {
            if (!currentSessionId) {
                log('❌ 没有活动的会话', 'error');
                return;
            }
            log(`获取会话 ${currentSessionId} 状态...`, 'info');
            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}`);
                const result = await response.json();
                if (result.success && result.data) {
                    log(`✅ 会话 ${currentSessionId} 状态获取成功: ${getStatusText(result.data.status)}`, 'success');
                    updateUI(result.data);
                    if (result.data.status !== 'connected') {
                        startPollingSessionStatus(currentSessionId);
                    }
                } else {
                    log(`❌ 获取会话 ${currentSessionId} 状态失败: ${result.message}`, 'error');
                    currentSessionId = null;
                    updateUI({ id: null, status: 'disconnected' });
                }
            } catch (error) {
                log(`❌ 获取状态错误: ${error.message}`, 'error');
            }
        }

        // 断开连接
        async function disconnectAccount() {
            if (!currentSessionId) {
                log('❌ 没有活动的会话', 'error');
                return;
            }
            log(`断开账号 ${currentSessionId} 连接...`, 'warning');
            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                if (result.success) {
                    log(`✅ 账号 ${currentSessionId} 已断开连接`, 'success');
                    clearInterval(pollingInterval);
                    currentSessionId = null;
                    updateUI({ id: null, status: 'disconnected' });
                } else {
                    log(`❌ 断开连接失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 断开连接错误: ${error.message}`, 'error');
            }
        }

        // 重连账号
        async function reconnectAccount() {
            if (!currentSessionId) {
                log('❌ 没有可重连的会话', 'error');
                return;
            }
            log(`重连账号 ${currentSessionId}...`, 'info');
            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}/reconnect`, {
                    method: 'POST'
                });
                const result = await response.json();
                if (result.success) {
                    log(`✅ 账号 ${currentSessionId} 重连请求已发送`, 'success');
                    updateUI(result.data);
                    startPollingSessionStatus(currentSessionId);
                } else {
                    log(`❌ 重连失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 重连错误: ${error.message}`, 'error');
            }
        }

        // 检查认证状态
        async function checkAuthStatus() {
            if (!currentSessionId) {
                log('❌ 没有活动的会话', 'error');
                return;
            }

            try {
                log(`检查账号 ${currentSessionId} 认证状态...`, 'info');
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}/auth-status`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    log(`📋 认证状态检查结果:`, 'info');
                    log(`   - 会话ID: ${data.sessionId}`, 'info');
                    log(`   - 有认证数据: ${data.hasAuthenticationData ? '✅ 是' : '❌ 否'}`, 'info');
                    log(`   - 当前状态: ${data.currentStatus}`, 'info');
                    log(`   - 可重连: ${data.canReconnect ? '✅ 是' : '❌ 否'}`, 'info');
                    
                    if (data.hasAuthenticationData) {
                        log('✅ 找到认证数据，可以重连', 'success');
                    } else {
                        log('❌ 没有认证数据，需要重新扫码', 'error');
                    }
                } else {
                    log(`❌ 检查认证状态失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 检查认证状态错误: ${error.message}`, 'error');
            }
        }

        // 发送消息
        async function sendMessage() {
            if (!currentSessionId) {
                log('❌ 没有活动的会话', 'error');
                return;
            }
            
            const message = document.getElementById('messageText').value;
            const recipient = document.getElementById('recipientNumber').value;
            
            if (!message) {
                log('❌ 请输入消息内容', 'error');
                return;
            }
            
            if (recipient) {
                log(`发送消息给 ${recipient}...`, 'info');
            } else {
                log(`发送消息给自己...`, 'info');
            }
            
            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}/messages`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        to: recipient || 'self',
                        message: message
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 消息发送成功`, 'success');
                } else {
                    log(`❌ 消息发送失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 发送错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Hive WhatsApp 测试页面已加载', 'info');
            log('点击"创建新账号"开始测试', 'info');
            updateUI({ id: null, status: 'disconnected' });
        });
    </script>
</body>
</html> 