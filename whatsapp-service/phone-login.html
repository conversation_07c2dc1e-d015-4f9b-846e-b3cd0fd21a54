<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐝 Hive WhatsApp 手机号登录</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #075E54;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .qr-container {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            display: none;
            border: 2px solid #e1e5e9;
        }
        .qr-container.show {
            display: block;
        }
        .qr-code {
            max-width: 250px;
            border: 8px solid #25D366;
            border-radius: 15px;
            padding: 15px;
        }
        .qr-title {
            color: #075E54;
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .phone-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1.1em;
            margin-bottom: 20px;
        }
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            width: 100%;
            margin-bottom: 10px;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status-section {
            margin-top: 30px;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 4px solid #25D366;
        }
        .message-section {
            margin-top: 30px;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 4px solid #128C7E;
        }
        .message-input-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .message-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            color: #333;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }
        .message-input::placeholder {
            color: #999;
        }
        .log-section {
            margin-top: 30px;
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
        }
        .log-item {
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 3px solid #ddd;
            background: #e3f2fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐝 Hive WhatsApp</h1>
            <p>手机号登录系统 - 智能会话管理</p>
        </div>

        <!-- 二维码区域 - 移到顶部 -->
        <div id="qrContainer" class="qr-container">
            <div class="qr-title">📱 请扫描二维码登录WhatsApp</div>
            <img id="qrCode" class="qr-code" src="" alt="QR Code">
            <div>请使用WhatsApp手机应用扫描上方二维码完成登录</div>
        </div>

        <div>
            <input type="text" id="phoneNumber" class="phone-input" 
                   placeholder="输入手机号（支持多种格式：15659989049、8615659989049等）" 
                   maxlength="20">
            <button id="loginBtn" class="btn" onclick="loginByPhone()">
                查找/登录账号
            </button>
        </div>

        <div class="status-section">
            <h3>账号状态</h3>
            <div>会话ID: <span id="sessionId">-</span></div>
            <div>手机号: <span id="phoneNumberDisplay">-</span></div>
            <div>状态: <span id="status">未连接</span></div>
            <button class="btn" onclick="getStatus()">刷新状态</button>
            <button class="btn" onclick="disconnectSession()">断开连接</button>
            <button class="btn" onclick="reconnectSession()">重新连接</button>
        </div>

        <div class="message-section">
            <h3>发送消息</h3>
            <div class="message-input-group">
                <input type="text" id="targetPhone" class="phone-input" 
                       placeholder="目标手机号（支持多种格式）" maxlength="20">
                <textarea id="messageContent" class="message-input" 
                          placeholder="输入要发送的消息内容" rows="3"></textarea>
                <button id="sendBtn" class="btn" onclick="sendMessage()">发送消息</button>
            </div>
        </div>

        <div class="log-section">
            <h3>操作日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/whatsapp';
        let currentSessionId = null;
        let pollingInterval = null;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `[${timestamp}] ${message}`;
            logElement.appendChild(logItem);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setButtonLoading(buttonId, loading, text) {
            const button = document.getElementById(buttonId);
            if (loading) {
                button.disabled = true;
                button.textContent = '处理中...';
            } else {
                button.disabled = false;
                button.textContent = text;
            }
        }

        function updateStatusDisplay(session) {
            if (session) {
                document.getElementById('sessionId').textContent = session.id || '-';
                document.getElementById('phoneNumberDisplay').textContent = session.phoneNumber || '-';
                document.getElementById('status').textContent = session.status || '未知';
                currentSessionId = session.id;
            }
        }

        async function loginByPhone() {
            const phoneNumber = document.getElementById('phoneNumber').value.trim();
            if (!phoneNumber) {
                log('请输入手机号码');
                return;
            }

            setButtonLoading('loginBtn', true, '登录中...');
            log(`开始登录手机号: ${phoneNumber}`);

            try {
                const response = await fetch(`${API_BASE}/sessions/phone/${phoneNumber}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    const session = result.data;
                    updateStatusDisplay(session);
                    
                    if (session.qrCode) {
                        showQRCode(session.qrCode);
                        log('需要扫描二维码登录');
                    } else {
                        hideQRCode();
                        log('登录成功，无需扫码');
                    }
                    
                    startPolling();
                } else {
                    log(`登录失败: ${result.message}`);
                }
            } catch (error) {
                log(`登录错误: ${error.message}`);
            } finally {
                setButtonLoading('loginBtn', false, '查找/登录账号');
            }
        }

        function showQRCode(qrCodeData) {
            const qrContainer = document.getElementById('qrContainer');
            const qrCode = document.getElementById('qrCode');
            
            qrCode.src = qrCodeData;
            qrContainer.classList.add('show');
        }

        function hideQRCode() {
            const qrContainer = document.getElementById('qrContainer');
            qrContainer.classList.remove('show');
        }

        async function getStatus() {
            if (!currentSessionId) {
                log('没有活动会话');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}`);
                const result = await response.json();
                
                if (result.success) {
                    const session = result.data;
                    updateStatusDisplay(session);
                    
                    if (session.qrCode) {
                        showQRCode(session.qrCode);
                    } else {
                        hideQRCode();
                    }
                    
                    log('状态已更新');
                } else {
                    log(`获取状态失败: ${result.message}`);
                }
            } catch (error) {
                log(`获取状态错误: ${error.message}`);
            }
        }

        async function disconnectSession() {
            if (!currentSessionId) {
                log('没有活动会话');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                
                if (result.success) {
                    log('会话已断开');
                    hideQRCode();
                    updateStatusDisplay({ id: currentSessionId, status: 'disconnected' });
                } else {
                    log(`断开连接失败: ${result.message}`);
                }
            } catch (error) {
                log(`断开连接错误: ${error.message}`);
            }
        }

        async function reconnectSession() {
            log('重连按钮被点击');
            
            if (!currentSessionId) {
                log('没有活动会话');
                return;
            }

            log(`开始重连会话: ${currentSessionId}`);

            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}/reconnect`, {
                    method: 'POST'
                });
                
                log(`重连API响应状态: ${response.status}`);
                
                const result = await response.json();
                log(`重连API响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    const session = result.data;
                    updateStatusDisplay(session);
                    
                    if (session.qrCode) {
                        showQRCode(session.qrCode);
                        log('需要重新扫描二维码');
                    } else {
                        hideQRCode();
                        log('重连成功');
                    }
                } else {
                    log(`重连失败: ${result.message}`);
                }
            } catch (error) {
                log(`重连错误: ${error.message}`);
                console.error('重连详细错误:', error);
            }
        }

        function startPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
            
            pollingInterval = setInterval(async () => {
                if (currentSessionId) {
                    try {
                        const response = await fetch(`${API_BASE}/sessions/${currentSessionId}`);
                        const result = await response.json();
                        
                        if (result.success) {
                            const session = result.data;
                            updateStatusDisplay(session);
                            
                            // 如果状态变为已连接，隐藏二维码
                            if (session.status === 'connected') {
                                hideQRCode();
                            }
                        }
                    } catch (error) {
                        // 静默处理轮询错误
                    }
                }
            }, 3000); // 每3秒轮询一次
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面已加载，请输入手机号开始登录');
        });

        // 回车键登录
        document.getElementById('phoneNumber').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loginByPhone();
            }
        });

        // 发送消息函数
        async function sendMessage() {
            if (!currentSessionId) {
                log('没有活动会话，请先登录');
                return;
            }

            const targetPhone = document.getElementById('targetPhone').value.trim();
            const messageContent = document.getElementById('messageContent').value.trim();

            if (!targetPhone) {
                log('请输入目标手机号');
                return;
            }

            if (!messageContent) {
                log('请输入要发送的消息内容');
                return;
            }

            setButtonLoading('sendBtn', true, '发送中...');
            log(`开始发送消息到: ${targetPhone}`);

            try {
                const response = await fetch(`${API_BASE}/sessions/${currentSessionId}/stranger-messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phoneNumber: targetPhone,
                        message: messageContent
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`消息发送成功: ${result.message}`);
                    // 清空输入框
                    document.getElementById('messageContent').value = '';
                } else {
                    log(`消息发送失败: ${result.message}`);
                }
            } catch (error) {
                log(`发送消息错误: ${error.message}`);
            } finally {
                setButtonLoading('sendBtn', false, '发送消息');
            }
        }
    </script>
</body>
</html>
