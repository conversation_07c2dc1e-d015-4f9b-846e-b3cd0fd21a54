import { Request, Response } from 'express'
import WhatsAppService from '../services/WhatsAppService'

export class WhatsAppController {
  private whatsappService: WhatsAppService

  constructor(whatsappService: WhatsAppService) {
    this.whatsappService = whatsappService
  }

  /**
   * 创建新的WhatsApp会话
   */
  async createSession(req: Request, res: Response): Promise<void> {
    try {
      // 自动生成sessionId，格式：hive-{timestamp}-{random}
      const timestamp = Date.now()
      const random = Math.random().toString(36).substring(2, 8)
      const sessionId = `hive-${timestamp}-${random}`

      const session = await this.whatsappService.createClient(sessionId)
      
      res.json({
        success: true,
        data: session,
        message: 'WhatsApp session created successfully'
      })
    } catch (error) {
      console.error('Error creating WhatsApp session:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 获取会话状态
   */
  async getSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params
      const session = this.whatsappService.getSession(sessionId)

      if (!session) {
        res.status(404).json({
          success: false,
          message: 'Session not found'
        })
        return
      }

      res.json({
        success: true,
        data: session
      })
    } catch (error) {
      console.error('Error getting session:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      })
    }
  }

  /**
   * 获取所有会话
   */
  async getAllSessions(req: Request, res: Response): Promise<void> {
    try {
      const sessions = this.whatsappService.getAllSessions()
      
      res.json({
        success: true,
        data: sessions
      })
    } catch (error) {
      console.error('Error getting all sessions:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      })
    }
  }

                /**
               * 断开会话
               */
              async disconnectSession(req: Request, res: Response): Promise<void> {
                try {
                  const { sessionId } = req.params
                  const success = await this.whatsappService.disconnectClient(sessionId)

                  if (!success) {
                    res.status(404).json({
                      success: false,
                      message: 'Session not found'
                    })
                    return
                  }

                  res.json({
                    success: true,
                    message: 'Session disconnected successfully'
                  })
                } catch (error) {
                  console.error('Error disconnecting session:', error)
                  res.status(500).json({
                    success: false,
                    message: 'Internal server error'
                  })
                }
              }

                /**
   * 重新连接会话
   */
  async reconnectSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params
      const session = await this.whatsappService.reconnectClient(sessionId)

      res.json({
        success: true,
        data: session,
        message: 'Session reconnection initiated successfully'
      })
    } catch (error) {
      console.error('Error reconnecting session:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 检查会话认证状态
   */
  async checkAuthStatus(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params
      const hasAuth = this.whatsappService.hasAuthenticationData(sessionId)
      const session = this.whatsappService.getSession(sessionId)

      res.json({
        success: true,
        data: {
          sessionId,
          hasAuthenticationData: hasAuth,
          currentStatus: session?.status || 'unknown',
          canReconnect: hasAuth && session?.status === 'disconnected'
        }
      })
    } catch (error) {
      console.error('Error checking auth status:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      })
    }
  }

  /**
   * 获取可重连的会话列表
   */
  async getReconnectableSessions(req: Request, res: Response): Promise<void> {
    try {
      const reconnectableSessions = this.whatsappService.getReconnectableSessions()

      res.json({
        success: true,
        data: {
          sessions: reconnectableSessions,
          count: reconnectableSessions.length
        }
      })
    } catch (error) {
      console.error('Error getting reconnectable sessions:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      })
    }
  }

  /**
   * 通过手机号查找会话
   */
  async findSessionByPhone(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber } = req.params
      const session = this.whatsappService.findSessionByPhone(phoneNumber)

      if (session) {
        res.json({
          success: true,
          data: session,
          message: 'Session found'
        })
      } else {
        res.status(404).json({
          success: false,
          message: 'Session not found for this phone number'
        })
      }
    } catch (error) {
      console.error('Error finding session by phone:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      })
    }
  }

  /**
   * 通过手机号查找或创建会话
   */
  async findOrCreateSessionByPhone(req: Request, res: Response): Promise<void> {
    try {
      const { phoneNumber } = req.params
      const session = await this.whatsappService.findOrCreateSessionByPhone(phoneNumber)

      res.json({
        success: true,
        data: session,
        message: session.phoneNumber ? 'Existing session found' : 'New session created'
      })
    } catch (error) {
      console.error('Error finding or creating session by phone:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

                /**
               * 发送消息
               */
              async sendMessage(req: Request, res: Response): Promise<void> {
                try {
                  const { sessionId } = req.params
                  const { to, message } = req.body

                  if (!to || !message) {
                    res.status(400).json({
                      success: false,
                      message: 'Recipient and message are required'
                    })
                    return
                  }

                  const success = await this.whatsappService.sendMessage(sessionId, to, message)

                  if (!success) {
                    res.status(400).json({
                      success: false,
                      message: 'Failed to send message'
                    })
                    return
                  }

                  res.json({
                    success: true,
                    message: 'Message sent successfully'
                  })
                } catch (error) {
                  console.error('Error sending message:', error)
                  res.status(500).json({
                    success: false,
                    message: 'Internal server error'
                  })
                }
              }

              /**
               * 发送消息给陌生人
               */
              async sendMessageToStranger(req: Request, res: Response): Promise<void> {
                try {
                  const { sessionId } = req.params
                  const { phoneNumber, message } = req.body

                  if (!phoneNumber || !message) {
                    res.status(400).json({
                      success: false,
                      message: 'Phone number and message are required'
                    })
                    return
                  }

                  const success = await this.whatsappService.sendMessageToStranger(sessionId, phoneNumber, message)

                  if (!success) {
                    res.status(400).json({
                      success: false,
                      message: 'Failed to send message to stranger'
                    })
                    return
                  }

                  res.json({
                    success: true,
                    message: 'Message sent to stranger successfully'
                  })
                } catch (error) {
                  console.error('Error sending message to stranger:', error)
                  res.status(500).json({
                    success: false,
                    message: 'Internal server error'
                  })
                }
              }

  /**
   * 获取客户端信息
   */
  async getClientInfo(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params
      const info = this.whatsappService.getClientInfo(sessionId)

      if (!info) {
        res.status(404).json({
          success: false,
          message: 'Client not found or not ready'
        })
        return
      }

      res.json({
        success: true,
        data: info
      })
    } catch (error) {
      console.error('Error getting client info:', error)
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      })
    }
  }

  /**
   * 获取聊天列表
   */
  async getChats(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params
      const limit = parseInt(req.query.limit as string) || 50

      const chats = await this.whatsappService.getChats(sessionId, limit)

      res.json({
        success: true,
        data: chats
      })
    } catch (error) {
      console.error('Error getting chats:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 获取特定聊天的消息记录
   */
  async getMessages(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId, chatId } = req.params
      const limit = parseInt(req.query.limit as string) || 50
      const fromMe = req.query.fromMe !== undefined ? req.query.fromMe === 'true' : undefined

      const messages = await this.whatsappService.getMessages(sessionId, chatId, limit, fromMe)

      res.json({
        success: true,
        data: messages
      })
    } catch (error) {
      console.error('Error getting messages:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 搜索消息
   */
  async searchMessages(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params
      const { query, ...options } = req.body

      if (!query) {
        res.status(400).json({
          success: false,
          message: 'Search query is required'
        })
        return
      }

      const messages = await this.whatsappService.searchMessages(sessionId, query, options)

      res.json({
        success: true,
        data: messages
      })
    } catch (error) {
      console.error('Error searching messages:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 获取联系人列表
   */
  async getContacts(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params

      const contacts = await this.whatsappService.getContacts(sessionId)

      res.json({
        success: true,
        data: contacts
      })
    } catch (error) {
      console.error('Error getting contacts:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 获取特定联系人信息
   */
  async getContactById(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId, contactId } = req.params

      const contact = await this.whatsappService.getContactById(sessionId, contactId)

      res.json({
        success: true,
        data: contact
      })
    } catch (error) {
      console.error('Error getting contact:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }

  /**
   * 获取聊天统计信息
   */
  async getChatStats(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId, chatId } = req.params

      const stats = await this.whatsappService.getChatStats(sessionId, chatId)

      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Error getting chat stats:', error)
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      })
    }
  }
}

export default WhatsAppController 