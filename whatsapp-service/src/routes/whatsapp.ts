import { Router, Request, Response } from 'express'
import WhatsAppController from '../controllers/WhatsAppController'
import WhatsAppService from '../services/WhatsAppService'

const router = Router()
const whatsappService = new WhatsAppService()
const whatsappController = new WhatsAppController(whatsappService)

// 创建新的WhatsApp会话
router.post('/sessions', (req: Request, res: Response) => whatsappController.createSession(req, res))

// 获取所有会话
router.get('/sessions', (req: Request, res: Response) => whatsappController.getAllSessions(req, res))

// 获取可重连的会话列表
router.get('/sessions/reconnectable', (req: Request, res: Response) => whatsappController.getReconnectableSessions(req, res))

// 通过手机号查找会话
router.get('/sessions/phone/:phoneNumber', (req: Request, res: Response) => whatsappController.findSessionByPhone(req, res))

// 通过手机号查找或创建会话
router.post('/sessions/phone/:phoneNumber', (req: Request, res: Response) => whatsappController.findOrCreateSessionByPhone(req, res))

// 获取特定会话状态
router.get('/sessions/:sessionId', (req: Request, res: Response) => whatsappController.getSession(req, res))

// 断开会话
router.delete('/sessions/:sessionId', (req: Request, res: Response) => whatsappController.disconnectSession(req, res))

// 重新连接会话
router.post('/sessions/:sessionId/reconnect', (req: Request, res: Response) => whatsappController.reconnectSession(req, res))

// 检查会话认证状态
router.get('/sessions/:sessionId/auth-status', (req: Request, res: Response) => whatsappController.checkAuthStatus(req, res))

// 获取客户端信息
router.get('/sessions/:sessionId/info', (req: Request, res: Response) => whatsappController.getClientInfo(req, res))

// 获取聊天列表
router.get('/sessions/:sessionId/chats', (req: Request, res: Response) => whatsappController.getChats(req, res))

// 获取特定聊天的消息记录
router.get('/sessions/:sessionId/chats/:chatId/messages', (req: Request, res: Response) => whatsappController.getMessages(req, res))

// 搜索消息
router.post('/sessions/:sessionId/search-messages', (req: Request, res: Response) => whatsappController.searchMessages(req, res))

// 获取联系人列表
router.get('/sessions/:sessionId/contacts', (req: Request, res: Response) => whatsappController.getContacts(req, res))

// 获取特定联系人信息
router.get('/sessions/:sessionId/contacts/:contactId', (req: Request, res: Response) => whatsappController.getContactById(req, res))

// 获取聊天统计信息
router.get('/sessions/:sessionId/chats/:chatId/stats', (req: Request, res: Response) => whatsappController.getChatStats(req, res))

// 发送消息
router.post('/sessions/:sessionId/messages', (req: Request, res: Response) => whatsappController.sendMessage(req, res))

// 发送消息给陌生人
router.post('/sessions/:sessionId/stranger-messages', (req: Request, res: Response) => whatsappController.sendMessageToStranger(req, res))

export default router 