import { Client, LocalAuth, ClientOptions } from 'whatsapp-web.js'
import QRCode from 'qrcode'
import { EventEmitter } from 'events'
import fs from 'fs'
import path from 'path'
import config from '../config'

export interface WhatsAppSession {
  id: string
  phoneNumber?: string
  status: 'disconnected' | 'connecting' | 'connected' | 'error' | 'banned' | 'offline'
  qrCode?: string
  error?: string
  lastActivity?: Date
  loginTime?: Date
}

export class WhatsAppService extends EventEmitter {
  private clients: Map<string, Client> = new Map()
  private sessions: Map<string, WhatsAppSession> = new Map()

  constructor() {
    super()
    this.ensureDirectories()
    // 启动时自动恢复所有有认证数据的会话
    this.initializeSessions()
  }

  private ensureDirectories() {
    const dirs = [config.whatsappSessionDir, config.whatsappDataDir]
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
    })
  }

  /**
   * 初始化时恢复所有有认证数据的会话
   */
  private async initializeSessions() {
    try {
      console.log('正在初始化WhatsApp会话...')
      const fs = require('fs')
      const path = require('path')
      
      if (!fs.existsSync(config.whatsappDataDir)) {
        console.log('数据目录不存在，跳过会话恢复')
        return
      }
      
      const sessionDirs = fs.readdirSync(config.whatsappDataDir)
      let restoredCount = 0
      
      for (const dir of sessionDirs) {
        if (dir.startsWith('session-')) {
          const sessionId = dir.replace('session-', '')
          const sessionDataPath = path.join(config.whatsappDataDir, dir)
          
          if (fs.existsSync(sessionDataPath)) {
            const localStoragePath = path.join(sessionDataPath, 'Default', 'Local Storage', 'leveldb')
            if (fs.existsSync(localStoragePath)) {
              console.log(`发现认证数据，尝试恢复会话: ${sessionId}`)
              try {
                await this.reconnectClient(sessionId)
                restoredCount++
                console.log(`成功恢复会话: ${sessionId}`)
              } catch (error) {
                console.log(`恢复会话 ${sessionId} 失败: ${error instanceof Error ? error.message : 'Unknown error'}`)
              }
            }
          }
        }
      }
      
      console.log(`会话初始化完成，恢复了 ${restoredCount} 个会话`)
    } catch (error) {
      console.error('初始化会话时出错:', error)
    }
  }

  /**
   * 创建新的WhatsApp客户端
   */
  async createClient(sessionId: string): Promise<WhatsAppSession> {
    try {
      // 检查是否已存在
      if (this.clients.has(sessionId)) {
        throw new Error(`Session ${sessionId} already exists`)
      }

      // 创建客户端配置
      const clientOptions: ClientOptions = {
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: config.whatsappDataDir
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--disable-javascript',
            '--disable-default-apps'
          ]
        }
      }

      // 创建客户端
      const client = new Client(clientOptions)
      
      // 初始化会话状态
      const session: WhatsAppSession = {
        id: sessionId,
        status: 'disconnected'
      }
      
      this.sessions.set(sessionId, session)
      this.clients.set(sessionId, client)

      // 设置事件监听器
      this.setupClientEvents(client, sessionId)

      // 初始化客户端
      await client.initialize()

      return session
    } catch (error) {
      console.error(`Error creating WhatsApp client for session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * 重新连接客户端（用于离线重连）
   */
  async reconnectClient(sessionId: string): Promise<WhatsAppSession> {
    try {
      // 检查是否已存在
      if (this.clients.has(sessionId)) {
        console.log(`Session ${sessionId} already exists, disconnecting first`)
        await this.disconnectClient(sessionId)
      }

      console.log(`Reconnecting client ${sessionId}...`)
      
      // 检查是否存在认证数据
      const fs = require('fs')
      const path = require('path')
      const sessionDataPath = path.join(config.whatsappDataDir, `session-${sessionId}`)
      
      if (!fs.existsSync(sessionDataPath)) {
        throw new Error(`No authentication data found for session ${sessionId}. Please scan QR code first.`)
      }
      
      console.log(`Found authentication data for session ${sessionId}`)
      
      // 创建客户端配置（与createClient相同）
      const clientOptions: ClientOptions = {
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: config.whatsappDataDir
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--disable-javascript',
            '--disable-default-apps'
          ]
        }
      }

      // 创建客户端
      const client = new Client(clientOptions)

      // 初始化会话状态
      const session: WhatsAppSession = {
        id: sessionId,
        status: 'connecting'
      }

      this.sessions.set(sessionId, session)
      this.clients.set(sessionId, client)

      // 设置事件监听器
      this.setupClientEvents(client, sessionId)

      // 初始化客户端
      await client.initialize()

      console.log(`Client ${sessionId} reconnection initiated`)
      return session
    } catch (error) {
      console.error(`Error reconnecting WhatsApp client for session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * 设置客户端事件监听器
   */
  private setupClientEvents(client: Client, sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

                    // 客户端准备就绪
                client.on('ready', () => {
                  console.log(`WhatsApp client ${sessionId} is ready`)
                  session.status = 'connected'
                  session.phoneNumber = client.info.wid.user
                  session.loginTime = new Date()
                  session.lastActivity = new Date()
                  session.qrCode = undefined
                  session.error = undefined

                  // 自动获取账号信息
                  this.updateAccountInfo(sessionId, client)

                  this.emit('client:ready', sessionId, session)
                })

    // 需要二维码
    client.on('qr', async (qr) => {
      // 避免重复处理相同的QR码
      if (session.qrCode) {
        console.log(`QR Code already exists for session ${sessionId}, skipping...`)
        return
      }
      
      console.log(`QR Code received for session ${sessionId}`)
      session.status = 'connecting'
      session.qrCode = await QRCode.toDataURL(qr)
      session.lastActivity = new Date()
      
      this.emit('client:qr', sessionId, session)
    })

    // 认证成功
    client.on('authenticated', () => {
      console.log(`Client ${sessionId} authenticated successfully`)
      session.status = 'connecting'
      session.lastActivity = new Date()
      session.qrCode = undefined // 清除QR码
      
      this.emit('client:authenticated', sessionId, session)
    })

    // 客户端准备就绪
    client.on('ready', () => {
      console.log(`WhatsApp client ${sessionId} is ready`)
      session.status = 'connected'
      session.lastActivity = new Date()
      
      // 更新账号信息（包括手机号）
      this.updateAccountInfo(sessionId, client)
      
      this.emit('client:ready', sessionId, session)
    })

    // 认证失败
    client.on('auth_failure', (msg) => {
      console.error(`Auth failure for session ${sessionId}:`, msg)
      session.status = 'error'
      session.error = msg
      session.lastActivity = new Date()
      
      this.emit('client:auth_failure', sessionId, session)
    })

    // 断开连接
    client.on('disconnected', (reason) => {
      console.log(`Client ${sessionId} disconnected:`, reason)
      session.status = 'disconnected'
      session.lastActivity = new Date()
      
      this.emit('client:disconnected', sessionId, session)
    })

    // 消息接收
    client.on('message', (msg) => {
      session.lastActivity = new Date()
      this.emit('client:message', sessionId, msg)
    })

    // 错误处理
    client.on('error', (error) => {
      console.error(`Error in client ${sessionId}:`, error)
      session.status = 'error'
      session.error = error.message
      session.lastActivity = new Date()
      
      this.emit('client:error', sessionId, session)
    })
  }

  /**
   * 获取会话状态
   */
  getSession(sessionId: string): WhatsAppSession | undefined {
    return this.sessions.get(sessionId)
  }

  /**
   * 获取所有会话
   */
  getAllSessions(): WhatsAppSession[] {
    return Array.from(this.sessions.values())
  }

  /**
   * 断开客户端连接
   */
  async disconnectClient(sessionId: string): Promise<boolean> {
    try {
      const client = this.clients.get(sessionId)
      if (!client) {
        return false
      }

      await client.destroy()
      this.clients.delete(sessionId)
      
      // 更新会话状态为断开，但保留会话信息（包括手机号）
      const session = this.sessions.get(sessionId)
      if (session) {
        session.status = 'disconnected'
        session.qrCode = undefined
        session.error = undefined
        session.lastActivity = new Date()
        console.log(`Session ${sessionId} marked as disconnected, phoneNumber: ${session.phoneNumber}`)
      }
      
      console.log(`Client ${sessionId} disconnected successfully`)
      return true
    } catch (error) {
      console.error(`Error disconnecting client ${sessionId}:`, error)
      return false
    }
  }

                /**
               * 发送消息
               */
              async sendMessage(sessionId: string, to: string, message: string): Promise<boolean> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client || !client.info) {
                    throw new Error('Client not ready')
                  }

                  // 检查客户端状态
                  if (client.pupPage && client.pupPage.isClosed()) {
                    throw new Error('Client page is closed')
                  }

                  let chatId: string
                  
                  if (to === 'self' || to === '') {
                    // 发送给自己
                    chatId = `${client.info.wid.user}@c.us`
                  } else {
                    // 格式化手机号
                    let phoneNumber = to.replace(/\D/g, '') // 移除所有非数字字符
                    
                    // 如果手机号以0开头，移除0
                    if (phoneNumber.startsWith('0')) {
                      phoneNumber = phoneNumber.substring(1)
                    }
                    
                    // 如果手机号以+开头，移除+
                    if (phoneNumber.startsWith('+')) {
                      phoneNumber = phoneNumber.substring(1)
                    }
                    
                    // 确保手机号格式正确
                    chatId = `${phoneNumber}@c.us`
                  }
                  
                  console.log(`Sending message to ${chatId} from session ${sessionId}`)
                  
                  // 使用try-catch包装发送消息
                  try {
                    await client.sendMessage(chatId, message)
                    console.log(`Message sent successfully to ${chatId}`)
                  } catch (sendError) {
                    console.error(`Send message error:`, sendError)
                    throw sendError
                  }
                  
                  const session = this.sessions.get(sessionId)
                  if (session) {
                    session.lastActivity = new Date()
                  }
                  
                  return true
                } catch (error) {
                  console.error(`Error sending message from session ${sessionId}:`, error)
                  return false
                }
              }

              /**
               * 发送消息给陌生人（支持多种手机号格式）
               */
              async sendMessageToStranger(sessionId: string, phoneNumber: string, message: string): Promise<boolean> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client || !client.info) {
                    throw new Error('Client not ready')
                  }

                  // 检查客户端状态
                  if (client.pupPage && client.pupPage.isClosed()) {
                    throw new Error('Client page is closed')
                  }

                  // 清理和格式化手机号
                  let cleanPhone = phoneNumber.replace(/\D/g, '') // 移除所有非数字字符
                  
                  // 处理不同国家的手机号格式
                  let countryCode = ''
                  let domain = '@s.whatsapp.net' // 默认使用国际域名
                  
                  if (cleanPhone.startsWith('86')) {
                    // 中国手机号
                    countryCode = '86'
                    cleanPhone = cleanPhone.substring(2)
                  } else if (cleanPhone.startsWith('852')) {
                    // 香港手机号
                    countryCode = '852'
                    cleanPhone = cleanPhone.substring(3)
                  } else if (cleanPhone.startsWith('853')) {
                    // 澳门手机号
                    countryCode = '853'
                    cleanPhone = cleanPhone.substring(3)
                  } else if (cleanPhone.startsWith('886')) {
                    // 台湾手机号
                    countryCode = '886'
                    cleanPhone = cleanPhone.substring(3)
                  } else if (cleanPhone.startsWith('1')) {
                    // 美国手机号
                    countryCode = '1'
                    cleanPhone = cleanPhone.substring(1)
                    domain = '@c.us' // 美国使用特殊域名
                  } else {
                    // 默认中国手机号
                    countryCode = '86'
                  }
                  
                  // 如果手机号以0开头，移除0
                  if (cleanPhone.startsWith('0')) {
                    cleanPhone = cleanPhone.substring(1)
                  }
                  
                  const chatId = `${countryCode}${cleanPhone}${domain}`
                  
                  console.log(`Sending message to stranger ${chatId} from session ${sessionId}`)
                  
                  // 使用try-catch包装发送消息
                  try {
                    await client.sendMessage(chatId, message)
                    console.log(`Message sent successfully to stranger ${chatId}`)
                  } catch (sendError) {
                    console.error(`Send message to stranger error:`, sendError)
                    throw sendError
                  }
                  
                  const session = this.sessions.get(sessionId)
                  if (session) {
                    session.lastActivity = new Date()
                  }
                  
                  return true
                } catch (error) {
                  console.error(`Error sending message to stranger from session ${sessionId}:`, error)
                  return false
                }
              }

                /**
               * 获取客户端信息
               */
              getClientInfo(sessionId: string) {
                const client = this.clients.get(sessionId)
                if (!client || !client.info) {
                  return null
                }

                return {
                  wid: client.info.wid,
                  platform: client.info.platform,
                  pushname: client.info.pushname
                }
              }

              /**
               * 获取聊天列表
               */
              async getChats(sessionId: string, limit: number = 50): Promise<any[]> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client) {
                    throw new Error(`Client not found for session ${sessionId}`)
                  }

                  const chats = await client.getChats()
                  return chats.slice(0, limit).map(chat => ({
                    id: chat.id._serialized,
                    name: chat.name,
                    isGroup: chat.isGroup,
                    isReadOnly: chat.isReadOnly,
                    unreadCount: chat.unreadCount,
                    lastMessage: chat.lastMessage ? {
                      id: chat.lastMessage.id._serialized,
                      body: chat.lastMessage.body,
                      timestamp: chat.lastMessage.timestamp,
                      fromMe: chat.lastMessage.fromMe
                    } : null,
                    timestamp: chat.timestamp
                  }))
                } catch (error) {
                  console.error(`Error getting chats for session ${sessionId}:`, error)
                  throw error
                }
              }

              /**
               * 获取特定聊天的消息记录
               */
              async getMessages(sessionId: string, chatId: string, limit: number = 50, fromMe?: boolean): Promise<any[]> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client) {
                    throw new Error(`Client not found for session ${sessionId}`)
                  }

                  const chat = await client.getChatById(chatId)
                  if (!chat) {
                    throw new Error(`Chat not found: ${chatId}`)
                  }

                  const options: any = { limit }
                  if (fromMe !== undefined) {
                    options.fromMe = fromMe
                  }

                  const messages = await chat.fetchMessages(options)
                  
                  return messages.map((msg: any) => ({
                    id: msg.id._serialized,
                    body: msg.body,
                    timestamp: msg.timestamp,
                    fromMe: msg.fromMe,
                    from: msg.from,
                    to: msg.to,
                    type: msg.type,
                    hasMedia: msg.hasMedia,
                    mediaType: msg.mediaType,
                    caption: msg.caption,
                    filename: msg.filename,
                    mimetype: msg.mimetype,
                    quotedMessageId: msg.quotedMessageId?._serialized,
                    quotedMessage: msg.quotedMessage ? {
                      id: msg.quotedMessage.id._serialized,
                      body: msg.quotedMessage.body,
                      fromMe: msg.quotedMessage.fromMe
                    } : null
                  }))
                } catch (error) {
                  console.error(`Error getting messages for chat ${chatId} in session ${sessionId}:`, error)
                  throw error
                }
              }

              /**
               * 搜索消息
               */
              async searchMessages(sessionId: string, query: string, options: any = {}): Promise<any[]> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client) {
                    throw new Error(`Client not found for session ${sessionId}`)
                  }

                  const messages = await client.searchMessages(query, options)
                  
                  return messages.map((msg: any) => ({
                    id: msg.id._serialized,
                    body: msg.body,
                    timestamp: msg.timestamp,
                    fromMe: msg.fromMe,
                    from: msg.from,
                    to: msg.to,
                    type: msg.type,
                    chatId: msg.chat?.id._serialized,
                    chatName: msg.chat?.name
                  }))
                } catch (error) {
                  console.error(`Error searching messages in session ${sessionId}:`, error)
                  throw error
                }
              }

              /**
               * 获取联系人列表
               */
              async getContacts(sessionId: string): Promise<any[]> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client) {
                    throw new Error(`Client not found for session ${sessionId}`)
                  }

                  const contacts = await client.getContacts()
                  
                  return contacts.map((contact: any) => ({
                    id: contact.id._serialized,
                    number: contact.number,
                    name: contact.name,
                    shortName: contact.shortName,
                    pushname: contact.pushname,
                    isBusiness: contact.isBusiness,
                    isEnterprise: contact.isEnterprise,
                    isMe: contact.isMe,
                    isMyContact: contact.isMyContact,
                    isUser: contact.isUser,
                    isWAContact: contact.isWAContact
                  }))
                } catch (error) {
                  console.error(`Error getting contacts for session ${sessionId}:`, error)
                  throw error
                }
              }

              /**
               * 获取特定联系人的信息
               */
              async getContactById(sessionId: string, contactId: string): Promise<any> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client) {
                    throw new Error(`Client not found for session ${sessionId}`)
                  }

                  const contact = await client.getContactById(contactId)
                  if (!contact) {
                    throw new Error(`Contact not found: ${contactId}`)
                  }

                  return {
                    id: contact.id._serialized,
                    number: contact.number,
                    name: contact.name,
                    shortName: contact.shortName,
                    pushname: contact.pushname,
                    isBusiness: contact.isBusiness,
                    isEnterprise: contact.isEnterprise,
                    isMe: contact.isMe,
                    isMyContact: contact.isMyContact,
                    isUser: contact.isUser,
                    isWAContact: contact.isWAContact
                  }
                } catch (error) {
                  console.error(`Error getting contact ${contactId} for session ${sessionId}:`, error)
                  throw error
                }
              }

              /**
               * 获取聊天统计信息
               */
              async getChatStats(sessionId: string, chatId: string): Promise<any> {
                try {
                  const client = this.clients.get(sessionId)
                  if (!client) {
                    throw new Error(`Client not found for session ${sessionId}`)
                  }

                  const chat = await client.getChatById(chatId)
                  if (!chat) {
                    throw new Error(`Chat not found: ${chatId}`)
                  }

                  // 获取最近的消息来计算统计信息
                  const messages = await chat.fetchMessages({ limit: 100 })
                  
                  const stats = {
                    totalMessages: messages.length,
                    fromMeCount: messages.filter((msg: any) => msg.fromMe).length,
                    fromOthersCount: messages.filter((msg: any) => !msg.fromMe).length,
                    mediaMessages: messages.filter((msg: any) => msg.hasMedia).length,
                    textMessages: messages.filter((msg: any) => !msg.hasMedia).length,
                    lastActivity: messages.length > 0 ? Math.max(...messages.map((msg: any) => msg.timestamp)) : null,
                    unreadCount: chat.unreadCount
                  }

                  return stats
                } catch (error) {
                  console.error(`Error getting chat stats for ${chatId} in session ${sessionId}:`, error)
                  throw error
                }
              }

              /**
               * 更新账号信息
               */
              private updateAccountInfo(sessionId: string, client: Client) {
                try {
                  const session = this.sessions.get(sessionId)
                  if (!session || !client.info) return

                  // 更新会话信息
                  session.phoneNumber = client.info.wid.user
                  session.lastActivity = new Date()

                  console.log(`账号信息更新 - Session: ${sessionId}, Phone: ${client.info.wid.user}, Name: ${client.info.pushname}`)
                } catch (error) {
                  console.error(`更新账号信息失败 - Session: ${sessionId}:`, error)
                }
              }

              /**
               * 检查会话是否有认证数据
               */
              hasAuthenticationData(sessionId: string): boolean {
                try {
                  const fs = require('fs')
                  const path = require('path')
                  const sessionDataPath = path.join(config.whatsappDataDir, `session-${sessionId}`)
                  return fs.existsSync(sessionDataPath)
                } catch (error) {
                  console.error(`Error checking authentication data for session ${sessionId}:`, error)
                  return false
                }
              }

              /**
               * 获取所有可重连的会话
               */
              getReconnectableSessions(): string[] {
                const reconnectableSessions: string[] = []
                
                for (const [sessionId, session] of this.sessions) {
                  if (session.status === 'disconnected' && this.hasAuthenticationData(sessionId)) {
                    reconnectableSessions.push(sessionId)
                  }
                }
                
                return reconnectableSessions
              }

              /**
               * 通过手机号查找会话
               */
              findSessionByPhone(phoneNumber: string): WhatsAppSession | undefined {
                // 生成多种可能的手机号格式
                const possiblePhones = this.generatePossiblePhoneFormats(phoneNumber)
                console.log(`查找手机号: ${phoneNumber} -> 可能的格式:`, possiblePhones)
                
                for (const [sessionId, session] of this.sessions) {
                  console.log(`检查会话 ${sessionId}: phoneNumber=${session.phoneNumber}, status=${session.status}`)
                  if (session.phoneNumber && possiblePhones.includes(session.phoneNumber)) {
                    console.log(`找到匹配会话: ${sessionId}`)
                    return session
                  }
                }
                
                console.log(`未找到匹配会话`)
                return undefined
              }

              /**
               * 通过手机号查找或创建会话
               */
              async findOrCreateSessionByPhone(phoneNumber: string): Promise<WhatsAppSession> {
                // 先查找现有会话
                const existingSession = this.findSessionByPhone(phoneNumber)
                if (existingSession) {
                  console.log(`找到现有会话: ${existingSession.id} for phone: ${phoneNumber}, status: ${existingSession.status}`)
                  
                  // 如果会话已断开但有认证数据，尝试重连
                  if (existingSession.status === 'disconnected' && this.hasAuthenticationData(existingSession.id)) {
                    console.log(`尝试重连现有会话: ${existingSession.id}`)
                    try {
                      return await this.reconnectClient(existingSession.id)
                    } catch (error) {
                      console.log(`重连失败，创建新会话: ${error instanceof Error ? error.message : 'Unknown error'}`)
                    }
                  }
                  
                  // 如果会话正在连接或已连接，直接返回
                  if (existingSession.status === 'connecting' || existingSession.status === 'connected') {
                    return existingSession
                  }
                }
                
                // 查找磁盘上有认证数据的会话
                console.log(`在磁盘上查找手机号 ${phoneNumber} 的认证数据...`)
                const fs = require('fs')
                const path = require('path')
                
                try {
                  const sessionDirs = fs.readdirSync(config.whatsappDataDir)
                  
                  for (const dir of sessionDirs) {
                    if (dir.startsWith('session-')) {
                      const sessionId = dir.replace('session-', '')
                      const sessionDataPath = path.join(config.whatsappDataDir, dir)
                      
                      if (fs.existsSync(sessionDataPath)) {
                        // 检查是否有认证数据
                        const localStoragePath = path.join(sessionDataPath, 'Default', 'Local Storage', 'leveldb')
                        if (fs.existsSync(localStoragePath)) {
                          console.log(`发现认证数据目录: ${sessionId}`)
                          
                          // 如果这个会话ID不在内存中，尝试恢复它
                          if (!this.sessions.has(sessionId)) {
                            console.log(`尝试恢复会话: ${sessionId}`)
                            try {
                              const session = await this.reconnectClient(sessionId)
                              
                              // 恢复成功后，检查手机号是否匹配
                              if (session.phoneNumber) {
                                const possiblePhones = this.generatePossiblePhoneFormats(phoneNumber)
                                if (possiblePhones.includes(session.phoneNumber)) {
                                  console.log(`成功恢复匹配的会话: ${sessionId} for phone: ${session.phoneNumber}`)
                                  return session
                                }
                              }
                              
                              // 如果手机号不匹配，断开这个会话
                              console.log(`会话 ${sessionId} 的手机号不匹配，断开连接`)
                              await this.disconnectClient(sessionId)
                            } catch (error) {
                              console.log(`恢复会话 ${sessionId} 失败: ${error instanceof Error ? error.message : 'Unknown error'}`)
                            }
                          }
                        }
                      }
                    }
                  }
                } catch (error) {
                  console.log(`读取磁盘会话数据失败: ${error instanceof Error ? error.message : 'Unknown error'}`)
                }
                
                // 创建新会话
                console.log(`为手机号 ${phoneNumber} 创建新会话`)
                return await this.createClient(`hive-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
              }

              /**
               * 生成多种可能的手机号格式
               */
              private generatePossiblePhoneFormats(phoneNumber: string): string[] {
                // 移除所有非数字字符
                let cleanPhone = phoneNumber.replace(/\D/g, '')
                const formats: string[] = []
                
                // 添加原始格式
                formats.push(cleanPhone)
                
                // 中国手机号格式
                if (cleanPhone.startsWith('86')) {
                  // 86开头 -> 去掉86
                  formats.push(cleanPhone.substring(2))
                  // 86开头 -> 保持原样
                  formats.push(cleanPhone)
                } else if (cleanPhone.length === 11 && !cleanPhone.startsWith('86')) {
                  // 11位非86开头 -> 添加86前缀
                  formats.push(`86${cleanPhone}`)
                  // 11位非86开头 -> 保持原样
                  formats.push(cleanPhone)
                }
                
                // 处理以0开头的情况
                if (cleanPhone.startsWith('0')) {
                  formats.push(cleanPhone.substring(1))
                } else if (cleanPhone.length === 10) {
                  formats.push(`0${cleanPhone}`)
                }
                
                // 去重并过滤空值
                const uniqueFormats = [...new Set(formats)].filter(f => f.length > 0)
                console.log(`生成的可能格式:`, uniqueFormats)
                return uniqueFormats
              }

              /**
               * 清理手机号格式
               */
              private cleanPhoneNumber(phoneNumber: string): string {
                // 移除所有非数字字符
                let cleanPhone = phoneNumber.replace(/\D/g, '')
                console.log(`清理前: ${phoneNumber} -> 清理后: ${cleanPhone}`)
                
                // 处理不同国家的手机号格式
                if (cleanPhone.startsWith('86')) {
                  // 中国手机号
                  cleanPhone = cleanPhone.substring(2)
                  console.log(`中国手机号处理: ${cleanPhone}`)
                } else if (cleanPhone.startsWith('852')) {
                  // 香港手机号
                  cleanPhone = cleanPhone.substring(3)
                } else if (cleanPhone.startsWith('853')) {
                  // 澳门手机号
                  cleanPhone = cleanPhone.substring(3)
                } else if (cleanPhone.startsWith('886')) {
                  // 台湾手机号
                  cleanPhone = cleanPhone.substring(3)
                } else if (cleanPhone.startsWith('1') && cleanPhone.length > 10) {
                  // 美国手机号（11位）
                  cleanPhone = cleanPhone.substring(1)
                }
                
                // 如果手机号以0开头，移除0
                if (cleanPhone.startsWith('0')) {
                  cleanPhone = cleanPhone.substring(1)
                }
                
                console.log(`最终清理结果: ${cleanPhone}`)
                return cleanPhone
              }
            }

            export default WhatsAppService 