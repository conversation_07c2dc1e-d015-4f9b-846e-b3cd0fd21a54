import express, { Application } from 'express'
import cors from 'cors'
import { createServer } from 'http'
import { Server } from 'socket.io'
import config from './config'
import whatsappRoutes from './routes/whatsapp'
import WhatsAppService from './services/WhatsAppService'

const app: Application = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    credentials: config.cors.credentials
  }
})

// 中间件
app.use(cors(config.cors))
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// 路由
app.use('/api/whatsapp', whatsappRoutes)

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'WhatsApp Service'
  })
})

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'WhatsApp Service API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      whatsapp: '/api/whatsapp'
    }
  })
})

// Socket.IO 连接处理
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id)

  // 监听WhatsApp事件
  socket.on('join-session', (sessionId: string) => {
    socket.join(`session:${sessionId}`)
    console.log(`Client ${socket.id} joined session ${sessionId}`)
  })

  socket.on('leave-session', (sessionId: string) => {
    socket.leave(`session:${sessionId}`)
    console.log(`Client ${socket.id} left session ${sessionId}`)
  })

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id)
  })
})

// 启动服务器
server.listen(config.port, () => {
  console.log(`🚀 WhatsApp Service is running on port ${config.port}`)
  console.log(`📱 Health check: http://localhost:${config.port}/health`)
  console.log(`🔗 API docs: http://localhost:${config.port}/`)
  console.log(`🌐 Environment: ${config.nodeEnv}`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  server.close(() => {
    console.log('Process terminated')
  })
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully')
  server.close(() => {
    console.log('Process terminated')
  })
})

export default app 