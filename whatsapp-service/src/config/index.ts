import dotenv from 'dotenv'
import path from 'path'

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../config.env') })

export const config = {
  // 服务器配置
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // 数据库配置
  dbPath: process.env.DB_PATH || '../database/hive.db',
  
  // WhatsApp配置
  whatsappSessionDir: process.env.WHATSAPP_SESSION_DIR || './sessions',
  whatsappDataDir: process.env.WHATSAPP_DATA_DIR || './data',
  
  // 日志配置
  logLevel: process.env.LOG_LEVEL || 'debug',
  
                // CORS配置
              cors: {
                origin: process.env.CORS_ORIGIN || true, // 开发环境允许所有origin
                credentials: true
              }
}

export default config 