version: '3.8'

services:
  hive-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "3000:3000"
    environment:
      - GIN_MODE=release
      - HIVE_DATABASE_URL=sqlite:/data/hive.db
      - HIVE_REDIS_URL=redis://redis:6379
    volumes:
      - ./data:/data
    depends_on:
      - redis
    networks:
      - hive-network

  hive-whatsapp-admin:
    build:
      context: .
      dockerfile: Dockerfile.whatsapp-admin
    environment:
      - GIN_MODE=release
    volumes:
      - ./services/whatsapp-service:/app/services/whatsapp-service
      - ./data/sessions:/app/data/sessions
    depends_on:
      - hive-api
    networks:
      - hive-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - hive-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    depends_on:
      - hive-api
    networks:
      - hive-network

volumes:
  redis-data:

networks:
  hive-network:
    driver: bridge 