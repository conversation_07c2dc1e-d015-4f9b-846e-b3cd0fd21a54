package main

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
)

func hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

func checkPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func main() {
	// 测试密码哈希
	password := "admin123"
	hash, err := hashPassword(password)
	if err != nil {
		fmt.Printf("Error hashing password: %v\n", err)
		return
	}
	
	fmt.Printf("Original password: %s\n", password)
	fmt.Printf("Hashed password: %s\n", hash)
	
	// 测试密码验证
	if checkPassword(password, hash) {
		fmt.Println("Password verification: SUCCESS")
	} else {
		fmt.Println("Password verification: FAILED")
	}
	
	// 测试错误的密码
	if checkPassword("wrongpassword", hash) {
		fmt.Println("Wrong password verification: SUCCESS (should be FAILED)")
	} else {
		fmt.Println("Wrong password verification: FAILED (correct)")
	}
} 