# 🏗️ Hive SaaS 项目结构

## 📁 项目目录结构

```
hive/
├── 📁 backend/                    # Go 后端服务
│   ├── 📁 api/                   # API 接口层
│   │   ├── 📁 auth/              # 认证相关 API
│   │   ├── 📁 user/              # 用户管理 API
│   │   ├── 📁 tenant/            # 租户管理 API
│   │   ├── 📁 customer-service/  # 客服管理 API
│   │   ├── 📁 whatsapp/          # WhatsApp 集成 API
│   │   ├── 📁 group-sending/     # 群发功能 API
│   │   └── 📁 system/            # 系统管理 API
│   ├── 📁 models/                # 数据模型
│   ├── 📁 middleware/            # 中间件
│   ├── 📁 utils/                 # 工具函数
│   ├── 📁 config/                # 配置文件
│   ├── 📄 main.go               # 主程序入口
│   ├── 📄 go.mod                # Go 模块定义
│   └── 📄 go.sum                # Go 依赖锁定
│
├── 📁 frontend/                   # Vue 3 前端应用
│   ├── 📁 src/                   # 源代码
│   │   ├── 📁 views/             # 页面组件
│   │   │   ├── 📁 user/          # 用户管理页面
│   │   │   ├── 📁 customer-service/ # 客服管理页面
│   │   │   ├── 📁 whatsapp/      # WhatsApp 管理页面
│   │   │   ├── 📁 marketing/     # 营销功能页面
│   │   │   ├── 📁 analytics/     # 数据分析页面
│   │   │   └── 📁 system/        # 系统管理页面
│   │   ├── 📁 components/        # 公共组件
│   │   ├── 📁 stores/            # Pinia 状态管理
│   │   ├── 📁 router/            # 路由配置
│   │   ├── 📁 utils/             # 工具函数
│   │   ├── 📁 types/             # TypeScript 类型定义
│   │   └── 📁 styles/            # 样式文件
│   ├── 📄 package.json          # 前端依赖配置
│   ├── 📄 vite.config.ts        # Vite 构建配置
│   └── 📄 tsconfig.json         # TypeScript 配置
│
├── 📁 whatsapp-node-service/     # WhatsApp Node.js 服务
│   ├── 📁 src/                   # 源代码
│   │   ├── 📁 services/          # 业务服务
│   │   ├── 📁 controllers/       # 控制器
│   │   ├── 📁 utils/             # 工具函数
│   │   └── 📄 index.js           # 主程序入口
│   ├── 📄 package.json          # Node.js 依赖配置
│   └── 📄 nodemon.json          # 开发配置
│
├── 📁 storage/                    # 数据存储目录
│   ├── 📁 database/              # 数据库文件
│   ├── 📁 uploads/               # 上传文件
│   │   ├── 📁 avatars/           # 头像文件
│   │   ├── 📁 documents/         # 文档文件
│   │   ├── 📁 images/            # 图片文件
│   │   ├── 📁 videos/            # 视频文件
│   │   ├── 📁 materials/         # 营销素材
│   │   ├── 📁 customer_lists/    # 客户列表
│   │   ├── 📁 whatsapp/          # WhatsApp 相关文件
│   │   └── 📁 general/           # 通用文件
│   ├── 📁 logs/                  # 日志文件
│   └── 📁 temp/                  # 临时文件
│
├── 📁 scripts/                    # 脚本文件
│   ├── 📁 tests/                 # 测试脚本
│   ├── 📁 deployment/            # 部署脚本
│   └── 📁 utils/                 # 工具脚本
│
├── 📁 docs/                       # 项目文档
│   ├── 📄 Project-Summary.md     # 项目总结
│   ├── 📄 Hive-Feature-Design.md # 功能设计文档
│   ├── 📄 WhatsApp-Integration-Design.md # WhatsApp 集成设计
│   └── 📄 *.md                   # 其他文档和报告
│
├── 📁 whatsapp-api-test/         # WhatsApp API 测试工具
├── 📄 docker-compose.yml         # Docker 编排配置
├── 📄 README.md                  # 项目说明
├── 📄 FINAL-STATUS.md            # 项目状态
└── 📄 PROJECT-STRUCTURE.md       # 项目结构说明（本文件）
```

## 🎯 目录说明

### 后端 (backend/)
- **api/**: 按功能模块组织的 API 接口
- **models/**: 数据库模型和业务模型
- **middleware/**: 认证、权限、日志等中间件
- **utils/**: 通用工具函数
- **config/**: 配置文件和环境变量

### 前端 (frontend/)
- **views/**: 按业务模块组织的页面组件
- **components/**: 可复用的 UI 组件
- **stores/**: Pinia 状态管理
- **router/**: Vue Router 路由配置
- **utils/**: 前端工具函数
- **types/**: TypeScript 类型定义

### 存储 (storage/)
- **database/**: SQLite 数据库文件
- **uploads/**: 按类型分类的上传文件
- **logs/**: 系统日志文件
- **temp/**: 临时文件存储

### 脚本 (scripts/)
- **tests/**: 功能测试和集成测试脚本
- **deployment/**: 部署和运维脚本
- **utils/**: 数据清理和维护脚本

## 🚀 快速启动

### 开发环境启动
```bash
# 1. 启动后端服务
cd backend && go run main.go

# 2. 启动前端服务
cd frontend && pnpm dev

# 3. 启动 WhatsApp 服务
cd whatsapp-node-service && npm start
```

### 服务端口
- **后端 API**: http://localhost:8081
- **前端应用**: http://localhost:5173
- **WhatsApp 服务**: http://localhost:3000

## 📝 注意事项

1. **数据库文件**: 位于 `storage/database/` 目录
2. **上传文件**: 统一存储在 `storage/uploads/` 目录
3. **日志文件**: 存储在 `storage/logs/` 目录
4. **测试脚本**: 移动到 `scripts/tests/` 目录
5. **文档资料**: 整理到 `docs/` 目录

## 🔧 配置更新

由于目录结构调整，需要更新以下配置：

1. **后端配置**: 更新数据库路径和上传路径
2. **前端配置**: 确认 API 接口路径
3. **部署配置**: 更新 Docker 和部署脚本路径

## 📚 相关文档

- [项目总结](docs/Project-Summary.md)
- [功能设计](docs/Hive-Feature-Design.md)
- [WhatsApp 集成](docs/WhatsApp-Integration-Design.md)
- [最终状态](FINAL-STATUS.md)
