# Hive SaaS - 多租户客户服务 SaaS 平台

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org/)
[![Vue Version](https://img.shields.io/badge/Vue-3.0+-green.svg)](https://vuejs.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

Hive SaaS 是一个现代化的多租户客户服务 SaaS 平台，采用前后端分离架构，提供完整的用户管理、权限控制、租户管理等功能。

## 📁 项目结构

详细的项目结构说明请查看 [PROJECT-STRUCTURE.md](PROJECT-STRUCTURE.md)

## 🚀 快速启动

### 一键启动所有服务
```bash
cd scripts
./start-all.sh
```

### 一键停止所有服务
```bash
cd scripts
./stop-all.sh
```

## 🚀 项目特性

### ✅ 核心功能
- **多租户架构**: 支持无限数量的租户，数据完全隔离
- **用户权限系统**: 基于角色的细粒度权限控制
- **租户切换功能**: 系统超级管理员可在租户间切换
- **现代化界面**: Vue 3 + Element Plus 构建的响应式界面
- **RESTful API**: 完整的 RESTful API 设计
- **安全认证**: JWT + bcrypt 的安全认证机制

### ✅ 技术栈
- **后端**: Go + Gin + GORM + SQLite
- **前端**: Vue 3 + TypeScript + Element Plus + Pinia
- **认证**: JWT + bcrypt
- **数据库**: SQLite (开发) / PostgreSQL (生产)

## 📋 功能模块

### 1. 多租户管理
- 租户创建和管理
- 租户数据隔离
- 租户切换功能
- 系统管理界面

### 2. 用户权限系统
- 用户创建、编辑、删除
- 角色管理和权限分配
- 基于角色的访问控制
- 租户级别的权限隔离

### 3. 认证授权
- JWT Token 认证
- 密码加密存储
- 会话管理
- 权限验证中间件

### 4. 前端界面
- 响应式用户界面
- 租户切换组件
- 用户管理界面
- 角色管理界面

## 🏗️ 项目结构

```
hive/
├── backend/                 # 后端服务
│   ├── main.go             # 应用入口
│   ├── auth.go             # 认证模块
│   ├── models.go           # 数据模型
│   ├── tenant_api.go       # 租户API
│   ├── role_api.go         # 角色API
│   ├── go.mod              # 依赖管理
│   └── hive.db             # SQLite数据库
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # 通用组件
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # 状态管理
│   │   ├── router/         # 路由配置
│   │   └── utils/          # 工具函数
│   └── package.json        # 依赖配置
├── docs/                   # 项目文档
├── test_comprehensive.sh   # 测试脚本
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- SQLite 3

### 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/your-username/hive.git
cd hive
```

#### 2. 启动后端服务
```bash
cd backend
go mod tidy
go run .
```

后端服务将在 `http://localhost:8081` 启动

#### 3. 启动前端服务
```bash
cd frontend
npm install
npm run dev
```

前端服务将在 `http://localhost:5173` 启动

#### 4. 访问系统
打开浏览器访问 `http://localhost:5173`

## 👥 默认账户

### 系统超级管理员
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 可以管理所有租户

### 租户管理员
- **用户名**: `admin_租户名`
- **密码**: `admin123`
- **权限**: 只能管理自己的租户

### 租户客服
- **用户名**: `customer_service_租户名`
- **密码**: `admin123`
- **权限**: 基础操作权限

## 📚 API 文档

### 认证接口
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 用户管理接口
```http
GET /api/users                    # 获取用户列表
POST /api/users                   # 创建用户
PUT /api/users/:id               # 更新用户
DELETE /api/users/:id            # 删除用户
```

### 角色管理接口
```http
GET /api/roles                   # 获取角色列表
POST /api/roles                  # 创建角色
PUT /api/roles/:id              # 更新角色
DELETE /api/roles/:id           # 删除角色
```

### 租户管理接口
```http
GET /api/tenant/current          # 获取当前租户
POST /api/tenant/switch          # 切换租户
GET /api/tenant/accessible       # 获取可访问租户
```

## 🧪 测试

### 运行测试脚本
```bash
chmod +x test_comprehensive.sh
./test_comprehensive.sh
```

### 测试覆盖
- ✅ 系统超级管理员功能测试
- ✅ 租户级管理员功能测试
- ✅ 租户级客服功能测试
- ✅ 权限隔离验证
- ✅ 错误处理测试

### 测试结果
- **总测试数**: 24
- **通过**: 24
- **失败**: 0
- **成功率**: 100%

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DB_TYPE=sqlite
DB_PATH=hive.db

# JWT 配置
JWT_SECRET=your-secret-key
JWT_EXPIRE_HOURS=24

# 服务配置
PORT=8081
ENV=development
```

### 前端配置
```javascript
// vite.config.ts
export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true
      }
    }
  }
})
```

## 📊 数据库设计

### 核心表结构
- **tenants**: 租户信息表
- **users**: 用户信息表
- **roles**: 角色信息表
- **permissions**: 权限信息表
- **role_permissions**: 角色权限关联表
- **tenant_switch_histories**: 租户切换历史表

### 数据隔离
- 所有关键表都包含 `tenant_id` 字段
- API 级别的数据过滤
- 系统超级管理员可访问所有数据

## 🔒 安全特性

### 认证安全
- JWT Token 认证
- bcrypt 密码加密
- Token 过期机制

### 权限控制
- 基于角色的权限控制 (RBAC)
- 租户级别的数据隔离
- API 级别的权限验证

### 数据安全
- SQL 注入防护
- XSS 攻击防护
- CSRF 防护

## 📈 性能优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池管理

### 前端优化
- 组件懒加载
- 状态管理优化
- 响应式设计

### 缓存策略
- JWT Token 缓存
- 用户信息缓存
- 租户信息缓存

## 🚀 部署

### 开发环境
```bash
# 后端
cd backend && go run .

# 前端
cd frontend && npm run dev
```

### 生产环境
```bash
# 编译后端
go build -o hive-server .

# 构建前端
npm run build

# 使用 Docker
docker-compose up -d
```

## 📝 开发指南

### 代码规范
- 遵循 Go 官方代码规范
- 使用 ESLint + Prettier 格式化前端代码
- 编写完整的单元测试

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建/工具
```

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目主页: [https://github.com/your-username/hive](https://github.com/your-username/hive)
- 问题反馈: [Issues](https://github.com/your-username/hive/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**更新时间**: 2025-07-29  
**版本**: v1.0.0  
**状态**: Phase1 完成 ✅ 