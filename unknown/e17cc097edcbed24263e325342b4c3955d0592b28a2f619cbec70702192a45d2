<template>
  <div id="app" :class="{ 'app-ready': appReady }">
    <!-- 使用路由级别的布局分离 -->
    <router-view v-slot="{ Component, route }">
      <!-- 登录页面使用独立布局 -->
      <template v-if="route.path === '/login'">
        <component :is="Component" />
      </template>

      <!-- 主应用布局 -->
      <template v-else>
        <el-container class="layout-container">
          <!-- 简洁顶部导航栏 -->
          <el-header class="header">
            <div class="header-content">
              <div class="logo-section slide-in-left">
                <!-- 移动端菜单按钮 -->
                <el-button v-if="isMobile" :icon="Menu" circle size="small" class="mobile-menu-btn"
                  @click="toggleSidebar" />

                <el-icon class="logo-icon" size="24">
                  <House />
                </el-icon>
                <h1 class="title">Hive SaaS</h1>
                <el-tag size="small" class="version-tag">v1.0</el-tag>
                <el-tag v-if="tenantStore.currentTenantName" size="small" type="info" class="tenant-tag">
                  {{ tenantStore.currentTenantName }}
                </el-tag>
              </div>
              <div class="header-actions slide-in-right">
                <!-- 租户切换组件 -->
                <TenantSwitcher />

                <!-- 主题切换按钮 -->
                <el-tooltip :content="themeStore.themeText" placement="bottom" effect="dark"
                  popper-class="custom-tooltip">
                  <el-button :icon="themeStore.isDark ? Sunny : Moon" circle class="theme-toggle"
                    @click="themeStore.toggleTheme" size="small" />
                </el-tooltip>

                <el-tooltip content="通知" placement="bottom" effect="dark" popper-class="custom-tooltip">
                  <el-badge :value="3" class="notification-badge">
                    <el-button :icon="Bell" circle size="small" />
                  </el-badge>
                </el-tooltip>

                <template v-if="authStore.isAuthenticated">
                  <el-dropdown class="user-dropdown">
                    <el-avatar :size="28" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item>
                          <div class="user-info">
                            <div class="username">{{ authStore.currentUser?.username }}</div>
                            <div class="role">{{ authStore.ismanager ? '管理员' : '普通用户' }}</div>
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item divided @click="goToProfile">个人中心</el-dropdown-item>
                        <el-dropdown-item @click="goToProfileAndChangePassword">修改密码</el-dropdown-item>
                        <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </div>
            </div>
          </el-header>

          <el-container class="main-container">
            <!-- 移动端遮罩层 -->
            <div v-if="showMobileMask" class="mobile-mask" @click="closeMobileSidebar"></div>

            <!-- 可折叠侧边栏 -->
            <el-aside class="sidebar" :class="{ collapsed: sidebarCollapsed, 'is-open': mobileMenuOpen }"
              :width="sidebarCollapsed ? '64px' : '220px'">
              <div class="sidebar-content">
                <el-menu :default-active="activeMenuIndex" class="sidebar-menu" :background-color="'transparent'"
                  :text-color="'var(--menu-text)'" :active-text-color="'var(--menu-active-text)'"
                  :collapse="sidebarCollapsed" :collapse-transition="false" router>
                  <el-menu-item index="/" title="首页">
                    <el-icon>
                      <HomeFilled />
                    </el-icon>
                    <template #title>首页</template>
                  </el-menu-item>

                  <!-- WhatsApp管理 - 移到首页下面 -->
                  <el-sub-menu v-if="showWhatsAppManagement" index="/whatsapp" title="WhatsApp管理">
                    <template #title>
                      <el-icon>
                        <ChatDotRound />
                      </el-icon>
                      <span>WhatsApp管理</span>
                    </template>
                    <el-menu-item index="/whatsapp/accounts">账号管理</el-menu-item>
                    <el-menu-item index="/whatsapp/groups">分组管理</el-menu-item>
                  </el-sub-menu>

                  <!-- 营销互动 -->
                  <el-sub-menu v-if="showMarketingManagement" index="/marketing" title="营销互动">
                    <template #title>
                      <el-icon>
                        <Promotion />
                      </el-icon>
                      <span>营销互动</span>
                    </template>
                    <el-menu-item index="/marketing/group-sending">陌生人群发</el-menu-item>
                  </el-sub-menu>

                  <el-sub-menu index="/users" title="用户管理">
                    <template #title>
                      <el-icon>
                        <User />
                      </el-icon>
                      <span>用户管理</span>
                    </template>
                    <el-menu-item index="/users">用户列表</el-menu-item>
                    <el-menu-item index="/users/roles">角色管理</el-menu-item>
                    <el-menu-item index="/users/permissions">权限配置</el-menu-item>
                  </el-sub-menu>

                  <el-sub-menu index="/customer-service" title="我的客服">
                    <template #title>
                      <el-icon>
                        <Service />
                      </el-icon>
                      <span>我的客服</span>
                    </template>
                    <el-menu-item index="/customer-service/accounts">客服账号</el-menu-item>
                    <el-menu-item index="/customer-service/groups">客服分组管理</el-menu-item>
                  </el-sub-menu>

                  <el-menu-item index="/analytics" title="数据分析">
                    <el-icon>
                      <DataAnalysis />
                    </el-icon>
                    <template #title>数据分析</template>
                  </el-menu-item>

                  <el-sub-menu index="/system" title="系统管理">
                    <template #title>
                      <el-icon>
                        <Setting />
                      </el-icon>
                      <span>系统管理</span>
                    </template>
                    <el-menu-item v-if="authStore.ismanager" index="/system/tenants">
                      租户管理
                    </el-menu-item>
                    <el-menu-item index="/system/config">系统配置</el-menu-item>
                    <el-menu-item index="/system/logs">操作日志</el-menu-item>
                    <el-menu-item index="/system/backup">数据备份</el-menu-item>
                  </el-sub-menu>
                </el-menu>

                <!-- 折叠按钮放在底部 -->
                <div class="sidebar-toggle" @click="toggleSidebar">
                  <el-icon>
                    <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
                  </el-icon>
                  <span v-if="!sidebarCollapsed" class="toggle-text">收起</span>
                </div>
              </div>
            </el-aside>

            <!-- 主内容区域 -->
            <el-main class="main-content">
              <transition name="fade-transform" mode="out-in">
                <keep-alive>
                  <component :is="Component" />
                </keep-alive>
              </transition>
            </el-main>
          </el-container>
        </el-container>
      </template>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useThemeStore } from './stores/theme'
import { useAuthStore } from './stores/auth'
import { useTenantStore } from './stores/tenant'
import TenantSwitcher from './components/TenantSwitcher.vue'
import {
  User,
  Setting,
  House,
  Bell,
  DataAnalysis,
  Moon,
  Sunny,
  HomeFilled,
  Loading,
  ChatDotRound,
  Promotion,
  Expand,
  Fold,
  Menu,
  Service
} from '@element-plus/icons-vue'

const themeStore = useThemeStore()
const authStore = useAuthStore()
const tenantStore = useTenantStore()
const route = useRoute()
const router = useRouter()

// 应用准备状态
const appReady = ref(false)

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)
const mobileMenuOpen = ref(false)

// 检测是否为移动端
const isMobile = computed(() => {
  if (typeof window === 'undefined') return false
  return window.innerWidth <= 768
})

// 显示移动端遮罩层
const showMobileMask = computed(() => {
  return isMobile.value && mobileMenuOpen.value
})

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  if (isMobile.value) {
    mobileMenuOpen.value = !mobileMenuOpen.value
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

// 关闭移动端侧边栏
const closeMobileSidebar = () => {
  mobileMenuOpen.value = false
}

// 计算当前活动的菜单项
const activeMenuIndex = computed(() => {
  return route.path
})

// 控制WhatsApp管理菜单的显示
const showWhatsAppManagement = computed(() => {
  // 如果用户不是管理员，不显示
  if (!authStore.ismanager) {
    return false
  }

  // 如果是系统超级管理员
  if (authStore.currentUser?.user_type === 'system' && authStore.currentUser?.role === 'super_admin') {
    // 在系统管理租户时不显示WhatsApp管理
    // 系统管理租户的is_system字段为true
    if (tenantStore.currentTenant?.is_system) {
      return false
    }
    // 在其他租户时显示WhatsApp管理
    return true
  }

  // 租户级管理员总是显示WhatsApp管理
  return true
})

// 控制营销互动菜单的显示
const showMarketingManagement = computed(() => {
  // 如果用户不是管理员，不显示
  if (!authStore.ismanager) {
    return false
  }

  // 如果是系统超级管理员
  if (authStore.currentUser?.user_type === 'system' && authStore.currentUser?.role === 'super_admin') {
    // 在系统管理租户时不显示营销互动
    if (tenantStore.currentTenant?.is_system) {
      return false
    }
    // 在其他租户时显示营销互动
    return true
  }

  // 租户级管理员总是显示营销互动
  return true
})

// 处理登出
async function handleLogout() {
  await authStore.logout()
  router.push('/login')
}

// 前往个人中心
function goToProfile() {
  router.push('/profile')
}

// 前往个人中心并打开修改密码对话框
function goToProfileAndChangePassword() {
  router.push('/profile?action=change-password')
}

onMounted(async () => {
  // 初始化主题和认证
  themeStore.initTheme()
  authStore.initAuth()

  // 确保DOM完全渲染后再显示
  await new Promise(resolve => {
    requestAnimationFrame(() => {
      setTimeout(() => {
        appReady.value = true
        resolve(undefined)
      }, 50)
    })
  })
})
</script>

<style scoped>
/* 应用加载状态 - 防止闪烁 */
#app {
  opacity: 0;
  transition: opacity 0.3s ease;
}

#app.app-ready {
  opacity: 1;
}

.layout-container {
  height: 100vh;
  width: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

/* 顶部导航栏样式 */
.header {
  background: var(--bg-header);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-xl);
  border-bottom: 1px solid var(--border-primary);
  height: 64px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
  width: 100% !important;
  margin: 0 !important;
  position: relative;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-menu-btn {
  margin-right: 8px;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.logo-icon {
  color: var(--accent-color);
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.version-tag {
  background: var(--hover-bg);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-toggle {
  transition: all 0.3s ease;
}

.notification-badge {
  margin-right: 4px;
}

.user-dropdown {
  cursor: pointer;
  margin-left: 4px;
  transition: transform 0.3s ease;
}

.user-dropdown:hover {
  transform: scale(1.05);
}

.user-info {
  padding: 4px 0;
}

.username {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.role {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
}

/* 主容器样式 */
.main-container {
  flex: 1;
  display: flex;
  background: transparent;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 侧边栏样式 */
.sidebar {
  background: var(--bg-sidebar);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-primary);
  transition: all var(--duration-normal) ease;
  position: relative;
  overflow: hidden;
  height: calc(100vh - 64px);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

/* 侧边栏内容容器 */
.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-md);
}

/* 侧边栏折叠按钮 - 放在底部 */
.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  margin-top: auto;
  margin-bottom: 0;
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--duration-normal) ease;
  color: var(--text-secondary);
  gap: var(--spacing-sm);
  border: 1px solid var(--border-color);
  background: var(--bg-card);
}

.sidebar-toggle:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.toggle-text {
  font-size: 14px;
  font-weight: 500;
}

.sidebar-menu {
  border: none;
  background: transparent;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 折叠状态下的菜单样式 */
.sidebar-menu.el-menu--collapse {
  width: 48px;
}

/* 强制覆盖Element Plus的默认样式 */
.sidebar-menu.el-menu--collapse :deep(.el-menu-item),
.sidebar-menu.el-menu--collapse :deep(.el-sub-menu > .el-sub-menu__title) {
  padding: 0 !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  margin: 4px auto !important;
  min-width: 48px !important;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item .el-icon),
.sidebar-menu.el-menu--collapse :deep(.el-sub-menu > .el-sub-menu__title .el-icon) {
  margin-right: 0 !important;
  margin-left: 0 !important;
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保折叠状态下的箭头图标隐藏 */
.sidebar-menu.el-menu--collapse :deep(.el-sub-menu > .el-sub-menu__title .el-sub-menu__icon-arrow) {
  display: none !important;
}

/* 折叠状态下图标的精确定位 */
.sidebar.collapsed .sidebar-menu :deep(.el-menu-item),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu > .el-sub-menu__title) {
  width: 48px !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 4px 0 !important;
  padding: 0 !important;
  position: relative !important;
}

.sidebar.collapsed .sidebar-menu :deep(.el-menu-item .el-icon),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu > .el-sub-menu__title .el-icon) {
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  font-size: 18px !important;
}

/* 隐藏折叠状态下的文字 */
.sidebar.collapsed .sidebar-menu :deep(.el-menu-item span),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu > .el-sub-menu__title span) {
  display: none !important;
}

/* 折叠状态下的tooltip */
.sidebar-menu.el-menu--collapse .el-menu-item,
.sidebar-menu.el-menu--collapse .el-sub-menu__title {
  position: relative;
}

/* 确保折叠时子菜单正常显示 */
.sidebar-menu.el-menu--collapse .el-sub-menu .el-menu {
  position: absolute;
  left: 48px;
  top: 0;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 160px;
  z-index: 1000;
  margin-left: 8px;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  height: 44px;
  line-height: 44px;
  border-radius: var(--radius-lg);
  margin: 4px 0;
  color: var(--text-secondary);
  transition: all var(--duration-normal) ease;
}

.sidebar-menu :deep(.el-menu-item .el-icon),
.sidebar-menu :deep(.el-sub-menu__title .el-icon) {
  margin-right: var(--spacing-md);
  font-size: var(--font-md);
  transition: all var(--duration-normal) ease;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.sidebar-menu :deep(.el-menu-item:hover .el-icon),
.sidebar-menu :deep(.el-sub-menu__title:hover .el-icon) {
  color: var(--primary-color);
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: var(--bg-selected);
  color: var(--primary-color);
  font-weight: var(--font-medium);
}

.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
  color: var(--primary-color);
}

.sidebar-menu :deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: var(--primary-color);
}

.sidebar-menu :deep(.el-sub-menu.is-active .el-sub-menu__title .el-icon) {
  color: var(--primary-color);
}

.sidebar-menu :deep(.el-sub-menu__title) {
  padding-left: var(--spacing-md) !important;
}

.sidebar-menu :deep(.el-menu-item) {
  padding-left: var(--spacing-md) !important;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  padding-left: calc(var(--spacing-md) + var(--spacing-xl)) !important;
  min-width: auto;
}

/* 主内容区域样式 */
.main-content {
  background: var(--bg-page);
  padding: var(--spacing-xl);
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
}

/* 折叠状态下的特殊处理 */
.sidebar.collapsed .sidebar-content {
  padding: var(--spacing-sm) 8px;
  align-items: center;
}

/* 折叠状态下的菜单容器居中 */
.sidebar.collapsed .sidebar-menu {
  width: 48px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

.sidebar.collapsed .sidebar-toggle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-top: var(--spacing-md);
  padding: 0;
}

.sidebar.collapsed .sidebar-toggle .toggle-text {
  display: none;
}

/* 折叠状态下菜单项居中对齐 - 进一步优化 */
.sidebar-menu.el-menu--collapse :deep(.el-menu-item),
.sidebar-menu.el-menu--collapse :deep(.el-sub-menu > .el-sub-menu__title) {
  height: 44px !important;
  line-height: 44px !important;
  border-radius: var(--radius-md) !important;
  margin: 4px auto !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
}

/* 折叠状态下移除所有内边距和外边距 */
.sidebar.collapsed .sidebar-menu :deep(.el-menu-item),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu > .el-sub-menu__title) {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 折叠状态下的hover tooltip效果 */
.sidebar-menu.el-menu--collapse .el-menu-item:hover::before,
.sidebar-menu.el-menu--collapse .el-sub-menu__title:hover::before {
  content: attr(title);
  position: absolute;
  left: 56px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--bg-card);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
  white-space: nowrap;
  z-index: 1000;
  font-size: 14px;
  pointer-events: none;
  opacity: 0;
  animation: tooltipFadeIn 0.2s ease forwards;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* 移动端遮罩层 */
.mobile-mask {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(4px);
  transition: all var(--duration-normal) ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -220px;
    top: 64px;
    bottom: 0;
    z-index: 1000;
    transition: left var(--duration-normal) ease;
    width: 220px !important;
  }

  .sidebar.is-open {
    left: 0;
  }

  .sidebar.collapsed {
    left: -64px;
  }

  .sidebar.collapsed.is-open {
    left: 0;
    width: 64px !important;
  }

  .main-content {
    margin-left: 0 !important;
  }

  /* 移动端折叠按钮样式调整 */
  .sidebar-toggle {
    margin-top: auto;
    margin-bottom: var(--spacing-sm);
  }
}

/* 桌面端折叠状态下主内容区域调整 */
@media (min-width: 769px) {
  .main-content {
    transition: margin-left var(--duration-normal) ease;
  }
}

@media (max-width: 480px) {
  .logo-section {
    gap: var(--spacing-sm);
  }

  .title {
    font-size: var(--font-sm);
  }

  .header-actions {
    gap: var(--spacing-xs);
  }

  .main-content {
    padding: var(--spacing-sm);
  }
}

/* 路由过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all var(--duration-normal) ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 移除页面内组件的重复动画 */
:deep(.fade-in),
:deep(.slide-in-left),
:deep(.slide-in-right),
:deep(.scale-in) {
  animation: none;
}

/* 自定义tooltip样式 */
:deep(.custom-tooltip) {
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.4;
  border-radius: 4px;
  z-index: 9999;
}
</style>
