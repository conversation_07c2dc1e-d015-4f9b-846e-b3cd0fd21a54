<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hive SaaS - 简单测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #337ecc;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .result.success {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .result.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #f6ffed;
            color: #52c41a;
        }
        .status.offline {
            background: #fff2f0;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hive SaaS - 多租户功能测试</h1>
        
        <div class="test-section">
            <h3>1. 服务状态检查</h3>
            <button class="button" onclick="checkServices()">检查服务状态</button>
            <div id="serviceStatus"></div>
        </div>

        <div class="test-section">
            <h3>2. 登录测试</h3>
            <button class="button" onclick="testLogin()">测试登录</button>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>3. 租户功能测试</h3>
            <button class="button" onclick="testTenantFeatures()">测试租户功能</button>
            <div id="tenantResult"></div>
        </div>

        <div class="test-section">
            <h3>4. 前端应用</h3>
            <button class="button" onclick="openFrontend()">打开前端应用</button>
            <p>前端地址: <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8081/api';
        let currentToken = null;

        // 检查服务状态
        async function checkServices() {
            const resultDiv = document.getElementById('serviceStatus');
            resultDiv.innerHTML = '<div class="result">检查中...</div>';

            try {
                // 检查后端服务
                const backendResponse = await fetch(`${API_BASE_URL}/health`);
                const backendStatus = backendResponse.ok ? 'online' : 'offline';
                
                // 检查前端服务
                const frontendResponse = await fetch('http://localhost:5173');
                const frontendStatus = frontendResponse.ok ? 'online' : 'offline';

                resultDiv.innerHTML = `
                    <div class="result success">
                        <div><span class="status ${backendStatus}">后端服务</span> ${backendStatus === 'online' ? '✅ 运行中' : '❌ 未运行'}</div>
                        <div><span class="status ${frontendStatus}">前端服务</span> ${frontendStatus === 'online' ? '✅ 运行中' : '❌ 未运行'}</div>
                        <div>后端地址: ${API_BASE_URL}</div>
                        <div>前端地址: http://localhost:5173</div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">检查失败: ${error.message}</div>`;
            }
        }

        // 测试登录
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<div class="result">登录测试中...</div>';

            try {
                const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'manager', password: 'admin123' })
                });
                
                const loginResult = await loginResponse.json();
                
                if (loginResult.code === 200) {
                    currentToken = loginResult.data.token;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 登录成功
                            
                            用户: ${loginResult.data.user_info.username}
                            角色: ${loginResult.data.user_info.role}
                            租户: ${loginResult.data.user_info.tenant?.name || '未知'}
                            Token: ${currentToken.substring(0, 20)}...
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">登录失败: ${loginResult.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">登录测试失败: ${error.message}</div>`;
            }
        }

        // 测试租户功能
        async function testTenantFeatures() {
            const resultDiv = document.getElementById('tenantResult');
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="result error">请先执行登录测试</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result">租户功能测试中...</div>';

            try {
                // 获取当前租户信息
                const currentResponse = await fetch(`${API_BASE_URL}/tenant/current`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const currentResult = await currentResponse.json();

                // 获取可访问租户
                const accessibleResponse = await fetch(`${API_BASE_URL}/tenant/accessible`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const accessibleResult = await accessibleResponse.json();

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 租户功能测试通过
                        
                        当前租户: ${currentResult.data?.name || '未知'}
                        可访问租户数: ${accessibleResult.data?.length || 0}
                        
                        租户列表:
                        ${accessibleResult.data?.map(t => `- ${t.name} (${t.plan_type})`).join('\n') || '无'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">租户功能测试失败: ${error.message}</div>`;
            }
        }

        // 打开前端应用
        function openFrontend() {
            window.open('http://localhost:5173', '_blank');
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html> 