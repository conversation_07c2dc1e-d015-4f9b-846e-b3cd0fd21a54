// 环境变量配置
export const config = {
  // API配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081',
  NODE_SERVICE_URL: import.meta.env.VITE_NODE_SERVICE_URL || 'http://localhost:3000',
  
  // 开发环境配置
  DEV_MODE: import.meta.env.VITE_DEV_MODE === 'true',
  
  // 默认配置
  DEFAULT_TIMEOUT: 30000,
  MAX_RETRY_COUNT: 3,
}

// 获取API URL的辅助函数
export const getApiUrl = (path) => {
  const baseUrl = config.API_BASE_URL
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

// 获取Node服务URL的辅助函数
export const getNodeServiceUrl = (path) => {
  const baseUrl = config.NODE_SERVICE_URL
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
} 