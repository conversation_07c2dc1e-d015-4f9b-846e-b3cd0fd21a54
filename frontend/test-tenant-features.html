<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hive SaaS - 多租户功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #337ecc;
        }
        .button.danger {
            background: #f56c6c;
        }
        .button.danger:hover {
            background: #e45656;
        }
        .button.success {
            background: #67c23a;
        }
        .button.success:hover {
            background: #5daf34;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .result.success {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .result.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #f6ffed;
            color: #52c41a;
        }
        .status.offline {
            background: #fff2f0;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hive SaaS - 多租户功能测试</h1>
        
        <div class="info">
            <h3>测试说明</h3>
            <p>此页面用于测试Hive SaaS的多租户功能，包括：</p>
            <ul>
                <li>租户切换功能</li>
                <li>数据隔离验证</li>
                <li>权限控制测试</li>
                <li>API接口测试</li>
            </ul>
            <p><strong>注意：</strong>请确保后端服务已启动（端口8081），前端服务已启动（端口5173）</p>
        </div>

        <div class="test-section">
            <h3>1. 服务状态检查</h3>
            <button class="button" onclick="checkServices()">检查服务状态</button>
            <div id="serviceStatus"></div>
        </div>

        <div class="test-section">
            <h3>2. 租户管理测试</h3>
            <button class="button" onclick="testTenantManagement()">测试租户管理</button>
            <button class="button success" onclick="testTenantSwitch()">测试租户切换</button>
            <button class="button" onclick="testDataIsolation()">测试数据隔离</button>
            <div id="tenantTestResult"></div>
        </div>

        <div class="test-section">
            <h3>3. 用户管理测试</h3>
            <button class="button" onclick="testUserManagement()">测试用户管理</button>
            <button class="button" onclick="testUserIsolation()">测试用户隔离</button>
            <div id="userTestResult"></div>
        </div>

        <div class="test-section">
            <h3>4. 权限控制测试</h3>
            <button class="button" onclick="testPermissions()">测试权限控制</button>
            <button class="button" onclick="testmanagerFeatures()">测试管理员功能</button>
            <div id="permissionTestResult"></div>
        </div>

        <div class="test-section">
            <h3>5. 前端功能测试</h3>
            <button class="button" onclick="openFrontend()">打开前端应用</button>
            <button class="button" onclick="testFrontendFeatures()">测试前端功能</button>
            <div id="frontendTestResult"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8081/api';
        let currentToken = null;
        let currentTenantId = null;

        // 检查服务状态
        async function checkServices() {
            const resultDiv = document.getElementById('serviceStatus');
            resultDiv.innerHTML = '<div class="result">检查中...</div>';

            try {
                // 检查后端服务
                const backendResponse = await fetch(`${API_BASE_URL}/health`);
                const backendStatus = backendResponse.ok ? 'online' : 'offline';
                
                // 检查前端服务
                const frontendResponse = await fetch('http://localhost:5173');
                const frontendStatus = frontendResponse.ok ? 'online' : 'offline';

                resultDiv.innerHTML = `
                    <div class="result success">
                        <div><span class="status ${backendStatus}">后端服务</span> ${backendStatus === 'online' ? '✅ 运行中' : '❌ 未运行'}</div>
                        <div><span class="status ${frontendStatus}">前端服务</span> ${frontendStatus === 'online' ? '✅ 运行中' : '❌ 未运行'}</div>
                        <div>后端地址: ${API_BASE_URL}</div>
                        <div>前端地址: http://localhost:5173</div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">检查失败: ${error.message}</div>`;
            }
        }

        // 测试租户管理
        async function testTenantManagement() {
            const resultDiv = document.getElementById('tenantTestResult');
            resultDiv.innerHTML = '<div class="result">测试中...</div>';

            try {
                // 1. 登录获取token
                const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'manager', password: 'admin123' })
                });
                
                const loginResult = await loginResponse.json();
                if (loginResult.code !== 200) {
                    throw new Error('登录失败: ' + loginResult.message);
                }

                currentToken = loginResult.data.token;
                currentTenantId = loginResult.data.user_info.tenant_id;

                // 2. 获取租户列表
                const tenantsResponse = await fetch(`${API_BASE_URL}/tenants`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const tenantsResult = await tenantsResponse.json();

                // 3. 获取可访问租户
                const accessibleResponse = await fetch(`${API_BASE_URL}/tenant/accessible`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const accessibleResult = await accessibleResponse.json();

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 租户管理测试通过
                        
                        当前用户: ${loginResult.data.user_info.username}
                        当前租户: ${loginResult.data.user_info.tenant?.name || '未知'}
                        租户总数: ${tenantsResult.data?.length || 0}
                        可访问租户: ${accessibleResult.data?.length || 0}
                        
                        Token: ${currentToken.substring(0, 20)}...
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">租户管理测试失败: ${error.message}</div>`;
            }
        }

        // 测试租户切换
        async function testTenantSwitch() {
            const resultDiv = document.getElementById('tenantTestResult');
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="result error">请先执行租户管理测试</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result">切换测试中...</div>';

            try {
                // 获取可访问租户
                const accessibleResponse = await fetch(`${API_BASE_URL}/tenant/accessible`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const accessibleResult = await accessibleResponse.json();

                if (!accessibleResult.data || accessibleResult.data.length < 2) {
                    resultDiv.innerHTML = '<div class="result error">需要至少2个租户才能测试切换功能</div>';
                    return;
                }

                // 选择非当前租户进行切换
                const targetTenant = accessibleResult.data.find(t => t.id !== currentTenantId);
                if (!targetTenant) {
                    resultDiv.innerHTML = '<div class="result error">没有可切换的租户</div>';
                    return;
                }

                // 执行租户切换
                const switchResponse = await fetch(`${API_BASE_URL}/tenant/switch`, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}` 
                    },
                    body: JSON.stringify({
                        tenant_id: targetTenant.id,
                        reason: '功能测试切换'
                    })
                });
                const switchResult = await switchResponse.json();

                if (switchResult.code === 200) {
                    currentToken = switchResult.data.new_token;
                    currentTenantId = targetTenant.id;
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 租户切换测试通过
                            
                            切换到租户: ${targetTenant.name}
                            新Token: ${currentToken.substring(0, 20)}...
                            切换时间: ${new Date().toLocaleString()}
                        </div>
                    `;
                } else {
                    throw new Error(switchResult.message);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">租户切换测试失败: ${error.message}</div>`;
            }
        }

        // 测试数据隔离
        async function testDataIsolation() {
            const resultDiv = document.getElementById('tenantTestResult');
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="result error">请先执行租户管理测试</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result">隔离测试中...</div>';

            try {
                // 获取当前租户的用户列表
                const usersResponse = await fetch(`${API_BASE_URL}/users`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const usersResult = await usersResponse.json();

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 数据隔离测试通过
                        
                        当前租户ID: ${currentTenantId}
                        用户数量: ${usersResult.data?.length || 0}
                        所有用户都属于当前租户: ${usersResult.data?.every(u => u.tenant_id === currentTenantId) ? '是' : '否'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">数据隔离测试失败: ${error.message}</div>`;
            }
        }

        // 测试用户管理
        async function testUserManagement() {
            const resultDiv = document.getElementById('userTestResult');
            resultDiv.innerHTML = '<div class="result">用户管理测试中...</div>';

            try {
                if (!currentToken) {
                    throw new Error('请先登录');
                }

                // 获取用户列表
                const usersResponse = await fetch(`${API_BASE_URL}/users`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const usersResult = await usersResponse.json();

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 用户管理测试通过
                        
                        用户总数: ${usersResult.data?.length || 0}
                        当前租户用户: ${usersResult.data?.filter(u => u.tenant_id === currentTenantId).length || 0}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">用户管理测试失败: ${error.message}</div>`;
            }
        }

        // 测试用户隔离
        async function testUserIsolation() {
            const resultDiv = document.getElementById('userTestResult');
            resultDiv.innerHTML = '<div class="result">用户隔离测试中...</div>';

            try {
                if (!currentToken) {
                    throw new Error('请先登录');
                }

                // 获取所有租户的用户
                const allUsersResponse = await fetch(`${API_BASE_URL}/users`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const allUsersResult = await allUsersResponse.json();

                // 检查是否所有用户都属于当前租户
                const allUsersBelongToCurrentTenant = allUsersResult.data?.every(u => u.tenant_id === currentTenantId);

                resultDiv.innerHTML = `
                    <div class="result ${allUsersBelongToCurrentTenant ? 'success' : 'error'}">
                        ${allUsersBelongToCurrentTenant ? '✅' : '❌'} 用户隔离测试${allUsersBelongToCurrentTenant ? '通过' : '失败'}
                        
                        当前租户ID: ${currentTenantId}
                        用户总数: ${allUsersResult.data?.length || 0}
                        所有用户都属于当前租户: ${allUsersBelongToCurrentTenant ? '是' : '否'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">用户隔离测试失败: ${error.message}</div>`;
            }
        }

        // 测试权限控制
        async function testPermissions() {
            const resultDiv = document.getElementById('permissionTestResult');
            resultDiv.innerHTML = '<div class="result">权限测试中...</div>';

            try {
                if (!currentToken) {
                    throw new Error('请先登录');
                }

                // 测试管理员功能访问
                const managerFeatures = [
                    { name: '租户管理', url: '/tenant/accessible' },
                    { name: '用户管理', url: '/users' },
                    { name: '系统配置', url: '/system/config' }
                ];

                const results = [];
                for (const feature of managerFeatures) {
                    try {
                        const response = await fetch(`${API_BASE_URL}${feature.url}`, {
                            headers: { 'Authorization': `Bearer ${currentToken}` }
                        });
                        results.push(`${feature.name}: ${response.ok ? '✅' : '❌'}`);
                    } catch (error) {
                        results.push(`${feature.name}: ❌`);
                    }
                }

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 权限控制测试完成
                        
                        ${results.join('\n')}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">权限控制测试失败: ${error.message}</div>`;
            }
        }

        // 测试管理员功能
        async function testmanagerFeatures() {
            const resultDiv = document.getElementById('permissionTestResult');
            resultDiv.innerHTML = '<div class="result">管理员功能测试中...</div>';

            try {
                if (!currentToken) {
                    throw new Error('请先登录');
                }

                // 测试租户切换历史
                const historyResponse = await fetch(`${API_BASE_URL}/tenant/switch-history`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });
                const historyResult = await historyResponse.json();

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 管理员功能测试通过
                        
                        切换历史记录数: ${historyResult.data?.length || 0}
                        当前用户权限: 管理员
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">管理员功能测试失败: ${error.message}</div>`;
            }
        }

        // 打开前端应用
        function openFrontend() {
            window.open('http://localhost:5173', '_blank');
        }

        // 测试前端功能
        function testFrontendFeatures() {
            const resultDiv = document.getElementById('frontendTestResult');
            resultDiv.innerHTML = `
                <div class="result success">
                    ✅ 前端功能测试指南
                    
                    请在浏览器中打开 http://localhost:5173 进行以下测试：
                    
                    1. 登录功能测试
                       - 使用 manager/admin123 登录
                       - 验证租户信息显示
                    
                    2. 租户切换测试
                       - 点击顶部租户选择器
                       - 选择不同租户进行切换
                       - 验证数据隔离效果
                    
                    3. 用户管理测试
                       - 访问用户管理页面
                       - 创建、编辑、删除用户
                       - 验证用户数据隔离
                    
                    4. 租户管理测试（管理员）
                       - 访问系统管理 > 租户管理
                       - 创建、编辑、删除租户
                       - 测试租户切换功能
                    
                    5. 权限控制测试
                       - 验证不同角色用户权限
                       - 测试管理员专属功能
                </div>
            `;
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html> 