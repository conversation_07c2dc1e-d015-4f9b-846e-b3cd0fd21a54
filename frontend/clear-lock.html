<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除登录锁定状态</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #337ecc;
        }
        .success {
            color: #67c23a;
            font-weight: bold;
        }
        .error {
            color: #f56c6c;
            font-weight: bold;
        }
        .info {
            color: #909399;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>清除登录锁定状态</h1>
        <p class="info">如果你被登录锁定，点击下面的按钮来清除锁定状态：</p>
        
        <button class="button" onclick="clearLockStatus()">清除登录锁定</button>
        <button class="button" onclick="clearAllAuthData()">清除所有认证数据</button>
        <button class="button" onclick="checkStatus()">检查当前状态</button>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>说明：</h3>
            <ul>
                <li><strong>清除登录锁定</strong>：只清除登录失败次数和锁定时间</li>
                <li><strong>清除所有认证数据</strong>：清除所有登录相关的本地存储数据</li>
                <li><strong>检查当前状态</strong>：查看当前存储的认证数据</li>
            </ul>
        </div>
    </div>

    <script>
        function clearLockStatus() {
            try {
                localStorage.removeItem('loginAttempts');
                localStorage.removeItem('loginLockoutUntil');
                document.getElementById('result').innerHTML = '<p class="success">✅ 登录锁定状态已清除！</p>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<p class="error">❌ 清除失败：' + error.message + '</p>';
            }
        }

        function clearAllAuthData() {
            try {
                localStorage.removeItem('loginAttempts');
                localStorage.removeItem('loginLockoutUntil');
                localStorage.removeItem('token');
                localStorage.removeItem('userInfo');
                localStorage.removeItem('currentTenant');
                localStorage.removeItem('rememberedUsername');
                document.getElementById('result').innerHTML = '<p class="success">✅ 所有认证数据已清除！</p>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<p class="error">❌ 清除失败：' + error.message + '</p>';
            }
        }

        function checkStatus() {
            const loginAttempts = localStorage.getItem('loginAttempts');
            const loginLockoutUntil = localStorage.getItem('loginLockoutUntil');
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            const currentTenant = localStorage.getItem('currentTenant');
            const rememberedUsername = localStorage.getItem('rememberedUsername');

            let status = '<h3>当前状态：</h3><ul>';
            status += '<li>登录尝试次数: ' + (loginAttempts || '0') + '</li>';
            status += '<li>锁定截止时间: ' + (loginLockoutUntil ? new Date(parseInt(loginLockoutUntil)).toLocaleString() : '无') + '</li>';
            status += '<li>Token: ' + (token ? '已存储' : '无') + '</li>';
            status += '<li>用户信息: ' + (userInfo ? '已存储' : '无') + '</li>';
            status += '<li>当前租户: ' + (currentTenant ? '已存储' : '无') + '</li>';
            status += '<li>记住的用户名: ' + (rememberedUsername || '无') + '</li>';
            status += '</ul>';

            document.getElementById('result').innerHTML = status;
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html> 