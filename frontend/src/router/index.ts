import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '@/stores/auth'
import { useTenantStore } from '@/stores/tenant'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '仪表盘', requiresAuth: true, requiresTenant: true }
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
      meta: { title: '关于' }
    },
    // 个人中心路由
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { title: '个人中心', requiresAuth: true, requiresTenant: true }
    },
    // 用户管理路由
    {
      path: '/users',
      name: 'users',
      children: [
        {
          path: '',
          name: 'user-list',
          component: () => import('../views/user/UserList.vue'),
          meta: { title: '用户列表', requiresAuth: true, requiresTenant: true }
        },
        {
          path: ':id',
          name: 'user-detail',
          component: () => import('../views/user/UserDetail.vue'),
          meta: { title: '用户详情', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'roles',
          name: 'roles',
          component: () => import('../views/user/RoleManagement.vue'),
          meta: { title: '角色管理', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'permissions',
          name: 'permissions',
          component: () => import('../views/user/PermissionConfig.vue'),
          meta: { title: '权限配置', requiresAuth: true, requiresTenant: true }
        }
      ]
    },
    // 系统管理路由
    {
      path: '/system',
      name: 'system',
      children: [
        {
          path: 'tenants',
          name: 'tenant-management',
          component: () => import('../views/system/TenantManagement.vue'),
          meta: { title: '租户管理', requiresAuth: true, requiresTenant: true, requiresmanager: true }
        },
        {
          path: 'config',
          name: 'system-config',
          component: () => import('../views/system/SystemConfig.vue'),
          meta: { title: '系统配置', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'logs',
          name: 'operation-logs',
          component: () => import('../views/system/OperationLogs.vue'),
          meta: { title: '操作日志', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'backup',
          name: 'data-backup',
          component: () => import('../views/system/DataBackup.vue'),
          meta: { title: '数据备份', requiresAuth: true, requiresTenant: true }
        }
      ]
    },
    // WhatsApp管理路由
    {
      path: '/whatsapp',
      name: 'whatsapp',
      redirect: '/whatsapp/accounts',
      children: [
        {
          path: 'accounts',
          name: 'whatsapp-accounts',
          component: () => import('../views/whatsapp/WhatsAppAccountList.vue'),
          meta: { title: 'WhatsApp账号管理', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'accounts/:id',
          name: 'whatsapp-account-detail',
          component: () => import('../views/whatsapp/WhatsAppAccountDetail.vue'),
          meta: { title: '账号详情', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'accounts/:id/send-message',
          name: 'whatsapp-account-send-message',
          component: () => import('../views/whatsapp/WhatsAppAccountSendMessage.vue'),
          meta: { title: '发送消息测试', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'groups',
          name: 'whatsapp-groups',
          component: () => import('../views/whatsapp/WhatsAppGroupList.vue'),
          meta: { title: 'WhatsApp分组管理', requiresAuth: true, requiresTenant: true }
        }
      ]
    },
    // 营销互动路由
    {
      path: '/marketing',
      name: 'marketing',
      redirect: '/marketing/group-sending',
      children: [
        {
          path: 'group-sending',
          name: 'group-sending',
          component: () => import('../views/marketing/GroupSendingList.vue'),
          meta: { title: '陌生人群发', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'group-sending/create',
          name: 'group-sending-create',
          component: () => import('../views/marketing/GroupSendingCreate.vue'),
          meta: { title: '新建群发任务', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'group-sending/:id',
          name: 'group-sending-detail',
          component: () => import('../views/marketing/GroupSendingDetail.vue'),
          meta: { title: '群发任务详情', requiresAuth: true, requiresTenant: true }
        }
      ]
    },
    // 客服管理路由
    {
      path: '/customer-service',
      name: 'customer-service',
      redirect: '/customer-service/accounts',
      children: [
        {
          path: 'accounts',
          name: 'customer-service-accounts',
          component: () => import('../views/customer-service/CustomerServiceList.vue'),
          meta: { title: '我的客服', requiresAuth: true, requiresTenant: true }
        },
        {
          path: 'groups',
          name: 'customer-service-groups',
          component: () => import('../views/customer-service/CustomerServiceGroupList.vue'),
          meta: { title: '客服分组管理', requiresAuth: true, requiresTenant: true }
        }
      ]
    },
    // 数据分析路由
    {
      path: '/analytics',
      name: 'analytics',
      component: () => import('../views/analytics/DataAnalytics.vue'),
      meta: { title: '数据分析', requiresAuth: true, requiresTenant: true }
    },
    // 登录页面
    {
      path: '/login',
      name: 'login',
      component: () => import('../layouts/AuthLayout.vue'),
      meta: { title: '登录', requiresGuest: true },
      children: [
        {
          path: '',
          name: 'login-form',
          component: () => import('../views/LoginView.vue')
        }
      ]
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFound.vue'),
      meta: { title: '页面未找到' }
    }
  ],
})

// 路由守卫 - 认证检查、租户检查和页面标题
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const tenantStore = useTenantStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Hive SaaS`
  } else {
    document.title = 'Hive SaaS'
  }

  // 直接用localStorage判断登录态
  const localToken = localStorage.getItem('token')
  const isAuthenticated = !!localToken
  const requiresAuth = to.meta?.requiresAuth === true  // 明确指定需要认证的路由
  const requiresGuest = to.meta?.requiresGuest === true // 仅游客访问（如登录页）
  const requiresTenant = to.meta?.requiresTenant === true // 需要租户信息的路由

  // 调试日志
  console.log('[路由守卫]', { 
    path: to.path, 
    isAuthenticated, 
    requiresAuth, 
    requiresGuest,
    requiresTenant,
    hasTenant: !!tenantStore.currentTenant
  })

  // 已登录用户访问游客页面（如登录页）
  if (requiresGuest && isAuthenticated) {
    next('/')
    return
  }

  // 未登录用户访问需要认证的页面
  if (requiresAuth && !isAuthenticated) {
    next('/login')
    return
  }

  // 已认证用户但需要租户信息的页面
  if (requiresTenant && isAuthenticated && !tenantStore.currentTenant) {
    try {
      // 尝试初始化租户信息
      await tenantStore.initTenant()
      
      // 如果还是没有租户信息，检查是否为系统管理员
      if (!tenantStore.currentTenant) {
        // 检查用户是否为系统管理员
        const userInfo = authStore.userInfo
        if (userInfo && (userInfo.role === 'super_admin' || userInfo.user_type === 'system')) {
          console.log('[路由守卫] 系统管理员无需租户信息，允许访问')
          next()
          return
        }
        
        console.warn('[路由守卫] 用户已认证但无租户信息，清除登录状态')
        authStore.logout()
        next('/login')
        return
      }
    } catch (error) {
      console.error('[路由守卫] 初始化租户信息失败:', error)
      authStore.logout()
      next('/login')
      return
    }
  }

  // 其他情况正常放行
  next()
})

export default router
