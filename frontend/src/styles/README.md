# Hive SaaS 设计系统

这是 Hive SaaS 项目的统一设计系统，确保所有页面和组件具有一致的视觉风格和用户体验。

## 设计原则

### 1. 一致性
- 统一的颜色系统
- 一致的间距规范
- 标准化的组件样式

### 2. 可访问性
- 高对比度色彩搭配
- 清晰的视觉层次
- 响应式设计

### 3. 现代化
- 简洁的界面设计
- 流畅的交互动画
- 直观的用户体验

## 文件结构

```
src/styles/
├── design-system.css    # 设计系统核心样式
├── components.css       # 组件样式
├── utilities.css        # 工具类样式
├── animations.css       # 动画效果
└── README.md           # 设计系统文档
```

## 使用指南

### 1. 颜色系统
- 主色调：蓝色系 (#667eea)
- 辅助色：绿色系 (#48bb78)
- 警告色：橙色系 (#ed8936)
- 错误色：红色系 (#f56565)

### 2. 间距规范
- 基础间距：8px
- 组件间距：16px
- 区块间距：24px
- 页面间距：32px

### 3. 字体规范
- 主标题：24px, 600
- 副标题：18px, 500
- 正文：14px, 400
- 辅助文字：12px, 400

## 组件样式

### 按钮样式
```css
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}
```

### 卡片样式
```css
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: all 0.3s ease;
}
```

## 响应式设计

### 断点定义
- 移动端：< 768px
- 平板端：768px - 1024px
- 桌面端：> 1024px

### 媒体查询
```css
@media (max-width: 768px) {
  /* 移动端样式 */
}

@media (min-width: 768px) and (max-width: 1024px) {
  /* 平板端样式 */
}

@media (min-width: 1024px) {
  /* 桌面端样式 */
}
```

## 动画效果

### 过渡动画
```css
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
```

### 悬停效果
```css
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}
```

## 深色模式

### 深色模式变量
```css
:root {
  --bg-primary: #ffffff;
  --text-primary: #1a202c;
}

html.dark {
  --bg-primary: #1a202c;
  --text-primary: #f7fafc;
}
```

## 最佳实践

1. **使用CSS变量**：便于主题切换和维护
2. **组件化样式**：提高代码复用性
3. **渐进增强**：确保基础功能可用
4. **性能优化**：避免过度使用动画效果

## 更新日志

### v2.0 (2024-01-20)
- 新增深色模式支持
- 优化响应式设计
- 增加动画效果库
- 完善组件样式系统

### v1.0 (2024-01-01)
- 初始版本发布
- 基础设计系统建立
- 核心组件样式定义 