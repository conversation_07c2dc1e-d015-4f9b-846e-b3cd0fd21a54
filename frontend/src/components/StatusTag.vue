<template>
  <el-tag 
    :type="getTagType(status)" 
    :size="size"
    :effect="effect"
  >
    <el-icon v-if="showIcon" class="status-icon">
      <component :is="getStatusIcon(status)" />
    </el-icon>
    {{ getStatusText(status) }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Clock,
  VideoPlay,
  VideoPause,
  CircleCheck,
  CircleClose,
  User,
  Warning,
  Connection
} from '@element-plus/icons-vue'

interface Props {
  status: string
  size?: 'large' | 'default' | 'small'
  effect?: 'dark' | 'light' | 'plain'
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  effect: 'light',
  showIcon: false
})

const getTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    // 群发任务状态
    'pending': 'info',
    'running': 'success',
    'paused': 'warning',
    'completed': 'success',
    'terminated': 'danger',
    'failed': 'danger',
    // WhatsApp账号状态
    'normal': 'success',
    'banned': 'danger',
    'suspended': 'warning',
    // 连接状态
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'info',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    // 群发任务状态
    'pending': '待开始',
    'running': '运行中',
    'paused': '暂停',
    'completed': '已完成',
    'terminated': '已终止',
    'failed': '失败',
    // WhatsApp账号状态
    'normal': '正常',
    'banned': '已封禁',
    'suspended': '已暂停',
    // 连接状态
    'connected': '已连接',
    'connecting': '连接中',
    'disconnected': '未连接',
    'error': '连接错误'
  }
  return textMap[status] || status
}

const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    // 群发任务状态
    'pending': Clock,
    'running': VideoPlay,
    'paused': VideoPause,
    'completed': CircleCheck,
    'terminated': CircleClose,
    'failed': CircleClose,
    // WhatsApp账号状态
    'normal': CircleCheck,
    'banned': CircleClose,
    'suspended': Warning,
    // 连接状态
    'connected': CircleCheck,
    'connecting': Clock,
    'disconnected': CircleClose,
    'error': Warning
  }
  return iconMap[status] || Clock
}
</script>

<style scoped>
.status-icon {
  margin-right: 4px;
}
</style> 