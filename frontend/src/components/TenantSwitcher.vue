<template>
  <div class="tenant-switcher">
    <!-- 租户切换下拉菜单 -->
    <el-dropdown 
      v-if="authStore.isAdmin && tenantStore.isMultiTenant" 
      @command="handleTenantSwitch"
      trigger="click"
    >
      <div class="tenant-selector">
        <el-icon><OfficeBuilding /></el-icon>
        <span class="tenant-name">{{ tenantStore.currentTenantName || '选择租户' }}</span>
        <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
      </div>
      
      <template #dropdown>
        <el-dropdown-menu>
          <!-- 系统级超级管理员显示"系统管理"选项 -->
          <el-dropdown-item 
            v-if="authStore.isSystemUser"
            :command="0"
            :disabled="!authStore.currentTenantId"
          >
            <div class="tenant-option">
              <div class="tenant-info">
                <span class="tenant-name">系统管理</span>
                <span class="tenant-plan">系统级</span>
              </div>
              <el-tag 
                v-if="!authStore.currentTenantId" 
                size="small" 
                type="success"
              >
                当前
              </el-tag>
            </div>
          </el-dropdown-item>
          
          <!-- 租户列表 -->
          <el-dropdown-item 
            v-for="tenant in tenantStore.tenants" 
            :key="tenant.id"
            :command="tenant.id"
            :disabled="tenant.id === authStore.currentTenantId"
          >
            <div class="tenant-option">
              <div class="tenant-info">
                <span class="tenant-name">{{ tenant.name }}</span>
                <span class="tenant-plan">{{ getPlanDisplayName(tenant.plan_type) }}</span>
              </div>
              <el-tag 
                v-if="tenant.id === authStore.currentTenantId" 
                size="small" 
                type="success"
              >
                当前
              </el-tag>
            </div>
          </el-dropdown-item>
          
          <el-dropdown-item divided command="history">
            <div class="tenant-option">
              <el-icon><Clock /></el-icon>
              <span>切换历史</span>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 租户信息显示（非管理员） -->
    <div v-else-if="tenantStore.currentTenant" class="tenant-info-display">
      <el-icon><OfficeBuilding /></el-icon>
      <span class="tenant-name">{{ tenantStore.currentTenantName }}</span>
      <el-tag size="small" :type="getPlanTagType(tenantStore.currentTenantPlan)">
        {{ getPlanDisplayName(tenantStore.currentTenantPlan) }}
      </el-tag>
    </div>

    <!-- 切换历史对话框 -->
    <el-dialog 
      v-model="historyDialogVisible" 
      title="租户切换历史" 
      width="600px"
      center
    >
      <div class="history-list">
        <el-timeline>
          <el-timeline-item
            v-for="record in switchHistory"
            :key="record.id"
            :timestamp="formatDate(record.switch_time)"
            :type="getTimelineItemType(record)"
          >
            <div class="history-item">
              <div class="history-header">
                <span class="from-tenant">{{ record.from_tenant?.name || '未知租户' }}</span>
                <el-icon><ArrowRight /></el-icon>
                <span class="to-tenant">{{ record.to_tenant?.name || '未知租户' }}</span>
              </div>
              <div class="history-details">
                <span class="reason">{{ record.reason || '无切换原因' }}</span>
                <span class="ip">{{ record.ip_address }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  OfficeBuilding, 
  ArrowDown, 
  ArrowRight, 
  Clock
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { useTenantStore } from '../stores/tenant'

const authStore = useAuthStore()
const tenantStore = useTenantStore()

// 状态
const historyDialogVisible = ref(false)
const switchHistory = ref<any[]>([])

// 处理租户切换
async function handleTenantSwitch(tenantId: number | string) {
  if (tenantId === 'history') {
    await showSwitchHistory()
    return
  }

  try {
    const tenantName = getTenantName(tenantId as number)
    // 确认切换
    await ElMessageBox.confirm(
      `确定要切换到${tenantId === 0 ? '系统管理' : `租户 "${tenantName}"`} 吗？`,
      '确认切换租户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 执行切换
    const result = await tenantStore.switchTenant(tenantId as number, '用户主动切换租户')
    
    if (result) {
      // 刷新页面数据
      window.location.reload()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('切换租户失败')
    }
  }
}

// 显示切换历史
async function showSwitchHistory() {
  try {
    const history = await tenantStore.fetchSwitchHistory()
    switchHistory.value = history
    historyDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取切换历史失败')
  }
}

// 获取租户名称
function getTenantName(tenantId: number): string {
  if (tenantId === 0) {
    return '系统管理'
  }
  const tenant = tenantStore.tenants.find(t => t.id === tenantId)
  return tenant?.name || '未知租户'
}

// 获取计划类型显示名称
function getPlanDisplayName(planType: string): string {
  const planMap: Record<string, string> = {
    'enterprise': '企业版',
    'professional': '专业版',
    'basic': '基础版'
  }
  return planMap[planType] || planType
}

// 获取计划类型标签类型
function getPlanTagType(planType: string): string {
  const typeMap: Record<string, string> = {
    'enterprise': 'danger',
    'professional': 'warning',
    'basic': 'primary'
  }
  return typeMap[planType] || 'info'
}

// 获取时间线项目类型
function getTimelineItemType(record: any): string {
  if (record.from_tenant_id === record.to_tenant_id) {
    return 'warning' // 同一租户内切换
  }
  return 'primary' // 跨租户切换
}

// 格式化日期
function formatDate(dateString: string): string {
  if (!dateString) return '--'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '--'
  }
}

// 生命周期
onMounted(async () => {
  // 初始化租户信息
  await tenantStore.initTenant()
})
</script>

<style scoped>
.tenant-switcher {
  display: flex;
  align-items: center;
}

.tenant-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  background: var(--bg-hover);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.tenant-selector:hover {
  background: var(--bg-selected);
  border-color: var(--primary-color);
}

.tenant-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.tenant-selector:hover .dropdown-icon {
  transform: rotate(180deg);
}

.tenant-info-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  background: var(--bg-hover);
  border: 1px solid var(--border-color);
}

.tenant-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.tenant-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tenant-plan {
  font-size: 12px;
  color: var(--text-secondary);
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  padding: 8px 0;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.from-tenant,
.to-tenant {
  font-weight: 500;
  color: var(--text-primary);
}

.history-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-secondary);
}

.reason {
  flex: 1;
}

.ip {
  color: var(--text-tertiary);
}
</style> 