import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import type { TenantInfo } from './auth'

// 简化的租户管理store
export const useTenantStore = defineStore('tenant', () => {
  // 状态
  const currentTenant = ref<TenantInfo | null>(null)
  const tenants = ref<TenantInfo[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isMultiTenant = computed(() => {
    // 系统级超级管理员总是可以切换租户
    // 检查当前用户是否为系统级超级管理员（通过检查currentTenant是否为系统租户）
    if (currentTenant.value?.is_system) {
      return tenants.value.length > 0
    }
    // 普通用户需要多个租户才能切换
    return tenants.value.length > 1
  })
  const currentTenantName = computed(() => currentTenant.value?.name || '系统管理')
  const currentTenantPlan = computed(() => currentTenant.value?.plan_type || 'system')

  // API基础配置
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api'

  // 设置认证头
  function getAuthHeaders() {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    }
  }

  // 显示错误消息
  function showError(message: string) {
    error.value = message
    ElMessage.error(message)
  }

  // 获取租户列表
  async function fetchTenants() {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`${API_BASE_URL}/admin/tenants`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      const result = await response.json()
      
      if (result.code === 200) {
        tenants.value = result.data
        return result.data
      } else {
        showError(result.message || '获取租户列表失败')
        return []
      }
    } catch (err) {
      showError('网络错误')
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取当前租户信息
  async function fetchCurrentTenant() {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`${API_BASE_URL}/tenant/current`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      const result = await response.json()
      
      if (result.code === 200) {
        currentTenant.value = result.data
        return result.data
      } else {
        showError(result.message || '获取当前租户信息失败')
        return null
      }
    } catch (err) {
      showError('网络错误')
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取可访问的租户列表
  async function fetchAccessibleTenants() {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`${API_BASE_URL}/tenant/accessible`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      const result = await response.json()
      
      if (result.code === 200) {
        tenants.value = result.data
        return result.data
      } else {
        showError(result.message || '获取可访问租户列表失败')
        return []
      }
    } catch (err) {
      showError('网络错误')
      return []
    } finally {
      loading.value = false
    }
  }

  // 切换租户
  async function switchTenant(tenantId: number, reason?: string) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`${API_BASE_URL}/tenant/switch`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          tenant_id: tenantId,
          reason: reason || '用户主动切换租户'
        }),
      })

      const result = await response.json()
      
      if (result.code === 200) {
        // 更新当前租户信息
        currentTenant.value = result.data.tenant_info
        
        // 更新本地存储的token
        const newToken = result.data.new_token
        localStorage.setItem('token', newToken)
        
        // 更新用户信息中的租户ID
        const { useAuthStore } = await import('./auth')
        const authStore = useAuthStore()
        if (authStore.userInfo) {
          authStore.userInfo.tenant_id = tenantId
          authStore.userInfo.tenant = result.data.tenant_info
          localStorage.setItem('userInfo', JSON.stringify(authStore.userInfo))
        }
        
        // 保存租户状态到本地存储
        saveTenantState(result.data.tenant_info)
        
        ElMessage.success('租户切换成功')
        return result.data
      } else {
        showError(result.message || '切换租户失败')
        return null
      }
    } catch (err) {
      showError('网络错误')
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取租户切换历史
  async function fetchSwitchHistory() {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`${API_BASE_URL}/tenant/switch-history`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      const result = await response.json()
      
      if (result.code === 200) {
        return result.data
      } else {
        showError(result.message || '获取切换历史失败')
        return []
      }
    } catch (err) {
      showError('网络错误')
      return []
    } finally {
      loading.value = false
    }
  }

  // 保存租户状态到本地存储
  function saveTenantState(tenant: TenantInfo) {
    localStorage.setItem('currentTenant', JSON.stringify(tenant))
  }

  // 从本地存储加载租户状态
  function loadTenantState(): TenantInfo | null {
    const savedTenant = localStorage.getItem('currentTenant')
    if (savedTenant) {
      try {
        return JSON.parse(savedTenant)
      } catch (err) {
        console.error('Failed to parse saved tenant info:', err)
        localStorage.removeItem('currentTenant')
      }
    }
    return null
  }

  // 初始化租户信息
  async function initTenant() {
    // 先尝试从本地存储加载
    const savedTenant = loadTenantState()
    if (savedTenant) {
      currentTenant.value = savedTenant
    }
    
    // 然后从服务器获取最新信息
    await fetchCurrentTenant()
    await fetchAccessibleTenants()
  }

  // 清除租户状态
  function clearTenant() {
    currentTenant.value = null
    tenants.value = []
    error.value = null
    localStorage.removeItem('currentTenant')
  }

  return {
    // 状态
    currentTenant,
    tenants,
    loading,
    error,
    
    // Getters
    isMultiTenant,
    currentTenantName,
    currentTenantPlan,
    
    // Actions
    fetchTenants,
    fetchCurrentTenant,
    fetchAccessibleTenants,
    switchTenant,
    fetchSwitchHistory,
    initTenant,
    clearTenant,
    saveTenantState,
    loadTenantState,
  }
}) 