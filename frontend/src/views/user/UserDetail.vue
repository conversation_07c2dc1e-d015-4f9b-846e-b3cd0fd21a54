<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header fade-in">
      <div>
        <h2 class="page-title">用户详情</h2>
        <p class="page-subtitle">查看用户详细信息和所属租户</p>
      </div>
      <div class="page-actions">
        <el-button @click="$router.back()" :icon="ArrowLeft">
          返回
        </el-button>
        <el-button type="primary" :icon="Edit" @click="handleEdit">
          编辑用户
        </el-button>
      </div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="user-info-section fade-in" style="animation-delay: 0.1s">
      <el-card class="user-card">
        <template #header>
          <div class="card-header">
            <div class="user-avatar">
              <el-avatar 
                :size="64" 
                :src="`https://api.dicebear.com/7.x/initials/svg?seed=${userInfo?.username}`" 
              />
            </div>
            <div class="user-basic-info">
              <h3 class="user-name">{{ userInfo?.username }}</h3>
              <p class="user-email">{{ userInfo?.email }}</p>
              <div class="user-tags">
                <el-tag 
                  :type="userInfo?.role === 'manager' ? 'danger' : 'primary'"
                  size="small"
                >
                  {{ getRoleDisplayName(userInfo?.role) }}
                </el-tag>
                <el-tag 
                  :type="userInfo?.status ? 'success' : 'danger'"
                  size="small"
                >
                  {{ userInfo?.status ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label>用户ID：</label>
              <span>{{ userInfo?.id }}</span>
            </div>
            <div class="info-item">
              <label>用户名：</label>
              <span>{{ userInfo?.username }}</span>
            </div>
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ userInfo?.email }}</span>
            </div>
            <div class="info-item">
              <label>角色：</label>
              <span>{{ getRoleDisplayName(userInfo?.role) }}</span>
            </div>
            <div class="info-item">
              <label>状态：</label>
              <span>{{ userInfo?.status ? '启用' : '禁用' }}</span>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(userInfo?.createdAt) }}</span>
            </div>
            <div class="info-item">
              <label>最后登录：</label>
              <span>{{ formatDate(userInfo?.last_login) }}</span>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="info-item">
              <label>所属租户：</label>
              <span v-if="userInfo?.tenant">
                <el-tag 
                  :type="getTenantTagType(userInfo.tenant.plan_type)" 
                  size="small"
                >
                  {{ userInfo.tenant.name }}
                </el-tag>
              </span>
              <span v-else style="color: var(--text-tertiary);">未知租户</span>
            </div>
            <div class="info-item" v-if="userInfo?.tenant">
              <label>租户域名：</label>
              <span>{{ userInfo.tenant.domain || '--' }}</span>
            </div>
            <div class="info-item" v-if="userInfo?.tenant">
              <label>租户计划：</label>
              <span>{{ getPlanTypeDisplayName(userInfo.tenant.plan_type) }}</span>
            </div>
            <div class="info-item" v-if="userInfo?.tenant">
              <label>最大账户数：</label>
              <span>{{ userInfo.tenant.max_accounts }}</span>
            </div>
            <div class="info-item" v-if="userInfo?.tenant">
              <label>最大存储：</label>
              <span>{{ formatStorage(userInfo.tenant.max_storage) }}</span>
            </div>
            <div class="info-item" v-if="userInfo?.tenant">
              <label>租户状态：</label>
              <el-tag 
                :type="userInfo.tenant.status === 'active' ? 'success' : 'danger'"
                size="small"
              >
                {{ getTenantStatusDisplayName(userInfo.tenant.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 租户切换功能（仅管理员可见） -->
    <div v-if="authStore.ismanager" class="tenant-switch-section fade-in" style="animation-delay: 0.2s">
      <el-card class="tenant-switch-card">
        <template #header>
          <div class="card-header">
            <span>租户管理</span>
          </div>
        </template>
        
        <div class="tenant-switch-content">
          <p class="switch-description">管理员可以为用户切换所属租户：</p>
          <el-form :inline="true">
            <el-form-item label="目标租户">
              <el-select 
                v-model="targetTenantId" 
                placeholder="请选择目标租户"
                style="width: 200px"
              >
                <el-option 
                  v-for="tenant in tenantStore.tenants" 
                  :key="tenant.id" 
                  :label="tenant.name" 
                  :value="tenant.id"
                  :disabled="tenant.id === userInfo?.tenant_id"
                >
                  <span style="float: left">{{ tenant.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ getPlanTypeDisplayName(tenant.plan_type) }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button 
                type="warning" 
                :icon="Switch"
                @click="handleSwitchTenant"
                :disabled="!targetTenantId || targetTenantId === userInfo?.tenant_id"
                :loading="switching"
              >
                切换租户
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 编辑用户表单 -->
    <UserForm
      v-model:visible="formVisible"
      mode="edit"
      :user-data="userInfo"
      :loading="loading"
      @submit="handleFormSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Edit, Switch } from '@element-plus/icons-vue'
import { useUserStore, type User } from '../../stores/user'
import { useAuthStore } from '../../stores/auth'
import { useTenantStore } from '../../stores/tenant'
import UserForm from '../../components/UserForm.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const authStore = useAuthStore()
const tenantStore = useTenantStore()

// 状态
const userInfo = ref<User | null>(null)
const loading = ref(false)
const switching = ref(false)
const formVisible = ref(false)
const targetTenantId = ref<number | null>(null)

// 生命周期
onMounted(async () => {
  await tenantStore.initTenant()
  await loadUserDetail()
})

// 方法
async function loadUserDetail() {
  const userId = route.params.id
  if (!userId) {
    ElMessage.error('用户ID不存在')
    router.push('/users')
    return
  }

  loading.value = true
  try {
    const user = await userStore.getUserById(Number(userId))
    userInfo.value = user
  } catch (error) {
    ElMessage.error('加载用户详情失败')
    router.push('/users')
  } finally {
    loading.value = false
  }
}

function handleEdit() {
  formVisible.value = true
}

async function handleFormSubmit(formData: any) {
  try {
    await userStore.updateUser(userInfo.value!.id, formData)
    ElMessage.success('用户更新成功')
    formVisible.value = false
    await loadUserDetail() // 重新加载用户详情
  } catch (error) {
    ElMessage.error('更新用户失败')
  }
}

async function handleSwitchTenant() {
  if (!targetTenantId.value || !userInfo.value) return

  try {
    await ElMessageBox.confirm(
      `确定要将用户 "${userInfo.value.username}" 切换到租户 "${getTenantName(targetTenantId.value)}" 吗？`,
      '确认切换租户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    switching.value = true
    // 这里需要调用后端API来切换用户租户
    // await userStore.switchUserTenant(userInfo.value.id, targetTenantId.value)
    
    ElMessage.success('租户切换成功')
    await loadUserDetail() // 重新加载用户详情
    targetTenantId.value = null
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('租户切换失败')
    }
  } finally {
    switching.value = false
  }
}

function getRoleDisplayName(role?: string) {
  if (!role) return '--'
  const roleMap: Record<string, string> = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'customer_service': '客服'
  }
  return roleMap[role] || role
}

function getTenantTagType(planType?: string) {
  if (!planType) return 'info'
  const typeMap: Record<string, string> = {
    'enterprise': 'danger',
    'professional': 'warning',
    'basic': 'primary'
  }
  return typeMap[planType] || 'info'
}

function getPlanTypeDisplayName(planType?: string) {
  if (!planType) return '--'
  const planMap: Record<string, string> = {
    'enterprise': '企业版',
    'professional': '专业版',
    'basic': '基础版'
  }
  return planMap[planType] || planType
}

function getTenantStatusDisplayName(status?: string) {
  if (!status) return '--'
  const statusMap: Record<string, string> = {
    'active': '活跃',
    'suspended': '暂停',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

function getTenantName(tenantId: number) {
  const tenant = tenantStore.tenants.find(t => t.id === tenantId)
  return tenant?.name || '未知租户'
}

function formatDate(dateString?: string) {
  if (!dateString) return '--'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '--'
  }
}

function formatStorage(bytes?: number) {
  if (!bytes) return '--'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}
</script>

<style scoped>
.user-info-section {
  margin-bottom: 24px;
}

.user-card {
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  display: flex;
  align-items: center;
}

.user-basic-info {
  flex: 1;
}

.user-name {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.user-email {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.user-tags {
  display: flex;
  gap: 8px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 100px;
  margin-right: 8px;
}

.info-item span {
  color: var(--text-primary);
}

.tenant-switch-section {
  margin-bottom: 24px;
}

.tenant-switch-card {
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.switch-description {
  margin: 0 0 16px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.tenant-switch-content {
  padding: 16px 0;
}
</style> 