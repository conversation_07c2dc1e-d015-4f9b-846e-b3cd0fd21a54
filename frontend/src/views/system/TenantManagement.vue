<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header fade-in">
      <div>
        <h2 class="page-title">租户管理</h2>
        <p class="page-subtitle">管理系统租户和权限配置</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新增租户
        </el-button>
        <el-button :icon="Refresh" @click="loadTenants" style="margin-left: 8px;">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section fade-in" style="animation-delay: 0.1s">
      <el-form :inline="true" class="search-form">
        <el-form-item label="租户名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="请输入租户名称" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="计划类型">
          <el-select 
            v-model="searchForm.plan_type" 
            placeholder="请选择计划类型" 
            clearable
            style="width: 150px"
          >
            <el-option label="基础版" value="basic" />
            <el-option label="专业版" value="professional" />
            <el-option label="企业版" value="enterprise" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择状态" 
            clearable
            style="width: 120px"
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section fade-in" style="animation-delay: 0.2s">
      <el-table 
        :data="filteredTenants" 
        :loading="tenantStore.loading"
        stripe
        style="width: 100%"
        empty-text="暂无租户数据"
        v-loading="tenantStore.loading"
        element-loading-text="正在加载租户数据..."
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="name" label="租户名称" min-width="150">
          <template #default="{ row }">
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-icon><OfficeBuilding /></el-icon>
              <span style="font-weight: 500;">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="domain" label="域名" min-width="150">
          <template #default="{ row }">
            <span style="color: var(--text-secondary);">{{ row.domain || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plan_type" label="计划类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getPlanTagType(row.plan_type)" 
              size="small"
            >
              {{ getPlanDisplayName(row.plan_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="限制" width="200" align="center">
          <template #default="{ row }">
            <div style="display: flex; flex-direction: column; gap: 4px;">
              <span style="font-size: 12px; color: var(--text-secondary);">
                账户: {{ row.max_accounts || '无限制' }}
              </span>
              <span style="font-size: 12px; color: var(--text-secondary);">
                存储: {{ formatStorage(row.max_storage) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            <span style="color: var(--text-tertiary); font-size: 13px;">
              {{ formatDate(row.created_at) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="{ row }">
            <div style="display: flex; justify-content: center; gap: 8px;">
              <el-button 
                size="small" 
                type="info" 
                :icon="View"
                @click="handleView(row)"
                plain
              >
                详情
              </el-button>
              <el-button 
                size="small" 
                type="primary" 
                :icon="Edit"
                @click="handleEdit(row)"
                plain
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                :icon="Switch"
                @click="handleSwitch(row)"
                plain
                v-if="row.id !== authStore.currentTenantId"
              >
                切换
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                :icon="Delete"
                @click="handleDelete(row)"
                plain
                :disabled="row.id === 1"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="tenantStore.error"
      :title="tenantStore.error"
      type="error"
      :closable="false"
      style="margin-top: 20px"
      class="fade-in"
    />

    <!-- 租户表单弹窗 -->
    <el-dialog 
      v-model="formVisible" 
      :title="formMode === 'create' ? '新增租户' : '编辑租户'"
      width="600px"
      center
    >
      <el-form 
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="租户名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入租户名称" />
        </el-form-item>
        <el-form-item label="域名" prop="domain">
          <el-input v-model="formData.domain" placeholder="请输入域名（可选）" />
        </el-form-item>
        <el-form-item label="计划类型" prop="plan_type">
          <el-select v-model="formData.plan_type" placeholder="请选择计划类型" style="width: 100%">
            <el-option label="基础版" value="basic" />
            <el-option label="专业版" value="professional" />
            <el-option label="企业版" value="enterprise" />
          </el-select>
        </el-form-item>
        <el-form-item label="最大账户数" prop="max_accounts">
          <el-input-number 
            v-model="formData.max_accounts" 
            :min="0" 
            :max="10000"
            style="width: 100%"
            placeholder="0表示无限制"
          />
        </el-form-item>
        <el-form-item label="最大存储(GB)" prop="max_storage">
          <el-input-number 
            v-model="formData.max_storage" 
            :min="0" 
            :max="100000"
            style="width: 100%"
            placeholder="0表示无限制"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button type="primary" :loading="tenantStore.loading" @click="handleFormSubmit">
          {{ formMode === 'create' ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 租户详情弹窗 -->
    <el-dialog 
      v-model="detailVisible" 
      title="租户详情"
      width="800px"
      center
    >
      <div v-if="currentTenant" class="tenant-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="租户ID">{{ currentTenant.id }}</el-descriptions-item>
          <el-descriptions-item label="租户名称">{{ currentTenant.name }}</el-descriptions-item>
          <el-descriptions-item label="域名">{{ currentTenant.domain || '--' }}</el-descriptions-item>
          <el-descriptions-item label="计划类型">
            <el-tag :type="getPlanTagType(currentTenant.plan_type)">
              {{ getPlanDisplayName(currentTenant.plan_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最大账户数">{{ currentTenant.max_accounts || '无限制' }}</el-descriptions-item>
          <el-descriptions-item label="最大存储">{{ formatStorage(currentTenant.max_storage) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentTenant.status === 'active' ? 'success' : 'danger'">
              {{ currentTenant.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentTenant.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Edit, 
  Delete, 
  Refresh, 
  View, 
  Switch,
  OfficeBuilding
} from '@element-plus/icons-vue'
import { useTenantStore } from '../../stores/tenant'
import { useAuthStore } from '../../stores/auth'
import type { TenantInfo } from '../../stores/auth'

const tenantStore = useTenantStore()
const authStore = useAuthStore()

// 搜索表单
const searchForm = ref({
  name: '',
  plan_type: '',
  status: ''
})

// 表单相关
const formVisible = ref(false)
const detailVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentTenant = ref<TenantInfo | null>(null)
const formRef = ref()

// 表单数据
const formData = ref({
  name: '',
  domain: '',
  plan_type: 'basic',
  max_accounts: 0,
  max_storage: 0,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入租户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '租户名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  plan_type: [
    { required: true, message: '请选择计划类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 过滤后的租户列表
const filteredTenants = computed(() => {
  let tenants = tenantStore.tenants

  if (searchForm.value.name) {
    tenants = tenants.filter(t => 
      t.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }

  if (searchForm.value.plan_type) {
    tenants = tenants.filter(t => t.plan_type === searchForm.value.plan_type)
  }

  if (searchForm.value.status) {
    tenants = tenants.filter(t => t.status === searchForm.value.status)
  }

  return tenants
})

// 生命周期
onMounted(async () => {
  await loadTenants()
})

// 方法
async function loadTenants() {
  try {
    await tenantStore.fetchTenants()
  } catch (error) {
    ElMessage.error('加载租户列表失败')
  }
}

function handleAdd() {
  formMode.value = 'create'
  currentTenant.value = null
  formData.value = {
    name: '',
    domain: '',
    plan_type: 'basic',
    max_accounts: 0,
    max_storage: 0,
    status: 'active'
  }
  formVisible.value = true
}

function handleEdit(row: TenantInfo) {
  formMode.value = 'edit'
  currentTenant.value = row
  formData.value = {
    name: row.name,
    domain: row.domain || '',
    plan_type: row.plan_type,
    max_accounts: row.max_accounts || 0,
    max_storage: row.max_storage || 0,
    status: row.status
  }
  formVisible.value = true
}

function handleView(row: TenantInfo) {
  currentTenant.value = row
  detailVisible.value = true
}

async function handleSwitch(row: TenantInfo) {
  try {
    await ElMessageBox.confirm(
      `确定要切换到租户 "${row.name}" 吗？`,
      '确认切换租户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await tenantStore.switchTenant(row.id, '管理员主动切换租户')
    
    if (result) {
      ElMessage.success('租户切换成功')
      window.location.reload()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('切换租户失败')
    }
  }
}

async function handleDelete(row: TenantInfo) {
  try {
    await ElMessageBox.confirm(
      `确定要删除租户 "${row.name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await tenantStore.deleteTenant(row.id)
    ElMessage.success('删除成功')
    await loadTenants()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

async function handleFormSubmit() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    if (formMode.value === 'create') {
      await tenantStore.createTenant(formData.value)
      ElMessage.success('租户创建成功')
    } else {
      await tenantStore.updateTenant(currentTenant.value!.id, formData.value)
      ElMessage.success('租户更新成功')
    }
    
    formVisible.value = false
    await loadTenants()
  } catch (error) {
    ElMessage.error(formMode.value === 'create' ? '创建租户失败' : '更新租户失败')
  }
}

function handleSearch() {
  // 搜索功能通过计算属性自动实现
}

function handleReset() {
  searchForm.value = {
    name: '',
    plan_type: '',
    status: ''
  }
}

function getPlanDisplayName(planType: string): string {
  const planMap: Record<string, string> = {
    'enterprise': '企业版',
    'professional': '专业版',
    'basic': '基础版'
  }
  return planMap[planType] || planType
}

function getPlanTagType(planType: string): string {
  const typeMap: Record<string, string> = {
    'enterprise': 'danger',
    'professional': 'warning',
    'basic': 'primary'
  }
  return typeMap[planType] || 'info'
}

function formatStorage(storage: number): string {
  if (!storage || storage === 0) return '无限制'
  if (storage < 1024) return `${storage} GB`
  return `${(storage / 1024).toFixed(1)} TB`
}

function formatDate(dateString: string): string {
  if (!dateString) return '--'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '--'
  }
}
</script>

<style scoped>
/* 页面特定样式可以在这里添加，但要使用设计系统的变量 */
.search-form .el-form-item {
  margin-bottom: 0;
}

.table-section .el-table {
  margin-bottom: 0;
}

.tenant-detail {
  padding: 20px 0;
}
</style> 