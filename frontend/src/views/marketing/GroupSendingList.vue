<template>
  <div class="group-sending-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>群发任务管理</h2>
      <div class="header-actions">
        <!-- 批量操作按钮 -->
        <el-dropdown v-if="selectedTasks.length > 0" @command="handleBatchCommand" trigger="click">
          <el-button type="warning" :icon="Operation">
            批量操作 ({{ selectedTasks.length }})
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="canBatchStart" command="batch-start" :icon="VideoPlay">
                批量启动
              </el-dropdown-item>
              <el-dropdown-item v-if="canBatchPause" command="batch-pause" :icon="VideoPause">
                批量暂停
              </el-dropdown-item>
              <el-dropdown-item v-if="canBatchTerminate" command="batch-terminate" :icon="CircleClose">
                批量终止
              </el-dropdown-item>
              <el-dropdown-item v-if="canBatchRestart" command="batch-restart" :icon="Refresh">
                批量重新开始
              </el-dropdown-item>
              <el-dropdown-item command="batch-delete" :icon="Delete" divided>
                批量删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button type="primary" @click="createTask" :icon="Plus">
          新建任务
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="任务名称">
          <el-input v-model="searchForm.taskName" placeholder="请输入任务名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="待开始" value="pending" />
            <el-option label="运行中" value="running" />
            <el-option label="暂停" value="paused" />
            <el-option label="已完成" value="completed" />
            <el-option label="已终止" value="terminated" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 任务列表 -->
    <div class="table-section">
      <el-table :data="filteredTasks" v-loading="loading" style="width: 100%" border stripe
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="序号" width="60" align="center" />

        <el-table-column prop="task_name" label="任务名称" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="task-name">{{ row.task_name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="task_id" label="任务ID" width="100" align="center" />

        <el-table-column label="客户触达进度" width="180" align="center">
          <template #default="{ row }">
            <div class="progress-info">
              <div class="progress-text">{{ row.reached_count }}/{{ row.customer_count }}</div>
              <el-progress :percentage="getProgressPercentage(row)" :stroke-width="8" :show-text="false" />
            </div>
            <el-tooltip content="客户触达进度说明" placement="top">
              <el-icon class="info-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="发送情况" width="200" align="center">
          <template #default="{ row }">
            <div class="sending-info">
              <div class="sending-item">
                <span class="label">待发送条数:</span>
                <span class="value">{{ row.pending_messages }}</span>
              </div>
              <div class="sending-item">
                <span class="label">可用发送条数:</span>
                <span class="value available">{{ row.available_messages }}</span>
              </div>
              <div class="sending-item">
                <span class="label">补号:</span>
                <el-button link size="small" @click="replenishNumbers(row)">补号</el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="sending_method" label="发送方式" width="120" align="center">
          <template #default="{ row }">
            <el-tag size="small" type="info">
              {{ getSendingMethodText(row.sending_method) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="sentence_interval" label="语句间隔" width="100" align="center">
          <template #default="{ row }">
            {{ row.sentence_interval }}秒
          </template>
        </el-table-column>

        <el-table-column label="发送次数" width="100" align="center">
          <template #default="{ row }">
            {{ getSendingTimesText(row) }}
          </template>
        </el-table-column>

        <el-table-column label="任务状态" width="100" align="center">
          <template #default="{ row }">
            <StatusTag :status="row.status" size="small" :show-icon="true" />
          </template>
        </el-table-column>

        <el-table-column prop="customer_interval" label="客户间隔" width="100" align="center">
          <template #default="{ row }">
            {{ row.customer_interval }}秒
          </template>
        </el-table-column>

        <el-table-column label="预约时间" width="160" align="center">
          <template #default="{ row }">
            <div>{{ formatTime(row.scheduled_time) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div>{{ formatTime(row.created_at) }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="creator_name" label="创建" width="100" align="center" />

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="viewDetail(row)">查看详情</el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button link size="small">
                更多<el-icon>
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <!-- 启动/继续任务 -->
                  <el-dropdown-item v-if="['pending', 'paused'].includes(row.status)" :command="`start-${row.task_id}`">
                    {{ row.status === 'pending' ? '启动任务' : '继续任务' }}
                  </el-dropdown-item>

                  <!-- 暂停任务 -->
                  <el-dropdown-item v-if="row.status === 'running'" :command="`pause-${row.task_id}`">
                    暂停任务
                  </el-dropdown-item>

                  <!-- 终止任务 -->
                  <el-dropdown-item v-if="['running', 'paused'].includes(row.status)"
                    :command="`terminate-${row.task_id}`">
                    终止任务
                  </el-dropdown-item>

                  <!-- 编辑任务 - 允许所有状态编辑 -->
                  <el-dropdown-item :command="`edit-${row.task_id}`">
                    编辑任务
                  </el-dropdown-item>

                  <!-- 重新开始任务 - 已完成的任务可以重新开始 -->
                  <el-dropdown-item v-if="['completed', 'terminated'].includes(row.status)"
                    :command="`restart-${row.task_id}`">
                    重新开始
                  </el-dropdown-item>

                  <!-- 查看日志 -->
                  <el-dropdown-item :command="`logs-${row.task_id}`">
                    查看日志
                  </el-dropdown-item>

                  <!-- 删除任务 - 允许所有状态删除 -->
                  <el-dropdown-item :command="`delete-${row.task_id}`" divided>
                    删除任务
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  QuestionFilled,
  ArrowDown,
  Operation,
  VideoPlay,
  VideoPause,
  CircleClose,
  Delete
} from '@element-plus/icons-vue'
import StatusTag from '@/components/StatusTag.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedTasks = ref<any[]>([])

// 搜索表单
const searchForm = ref({
  taskName: '',
  status: ''
})

// 计算属性
const filteredTasks = computed(() => {
  let result = tasks.value

  if (searchForm.value.taskName) {
    result = result.filter(task =>
      task.task_name.toLowerCase().includes(searchForm.value.taskName.toLowerCase())
    )
  }

  if (searchForm.value.status) {
    result = result.filter(task => task.status === searchForm.value.status)
  }

  return result
})

// 方法
const fetchTasks = async () => {
  try {
    console.log('fetchTasks - 开始获取任务列表')
    loading.value = true
    const response = await fetch('/api/group-sending/tasks', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    console.log('fetchTasks - API响应:', result)

    if (result.code === 200) {
      tasks.value = result.data.tasks
      total.value = result.data.total
      console.log('fetchTasks - 成功获取任务列表，任务数量:', tasks.value.length)
    } else {
      console.error('fetchTasks - API错误:', result.message)
      ElMessage.error(result.message || '获取任务列表失败')
    }
  } catch (error) {
    console.error('fetchTasks - 网络错误:', error)
    ElMessage.error('网络错误')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchTasks()
}

const handleReset = () => {
  searchForm.value = {
    taskName: '',
    status: ''
  }
  currentPage.value = 1
  fetchTasks()
}

const createTask = () => {
  router.push('/marketing/group-sending/create')
}

const viewDetail = (row: any) => {
  router.push(`/marketing/group-sending/${row.task_id}`)
}

const handleCommand = async (command: string) => {
  // 修复：使用正确的分割方法，只分割第一个连字符
  const firstDashIndex = command.indexOf('-')
  const action = command.substring(0, firstDashIndex)
  const id = command.substring(firstDashIndex + 1)

  try {
    switch (action) {
      case 'start':
        await startTask(id)
        break
      case 'pause':
        await pauseTask(id)
        break
      case 'terminate':
        await terminateTask(id)
        break
      case 'edit':
        router.push(`/marketing/group-sending/create?id=${id}`)
        break
      case 'logs':
        router.push(`/marketing/group-sending/${id}?tab=logs`)
        break
      case 'delete':
        await deleteTask(id)
        break
      case 'restart':
        await restartTask(id)
        break
    }
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const handleBatchCommand = async (command: string) => {
  const selectedIds = selectedTasks.value.map(task => task.task_id)

  try {
    let confirmMessage = ''
    let apiEndpoint = ''

    switch (command) {
      case 'batch-start':
        confirmMessage = `确定要批量启动选中的 ${selectedIds.length} 个任务吗？`
        apiEndpoint = '/api/group-sending/tasks/batch/start'
        break
      case 'batch-pause':
        confirmMessage = `确定要批量暂停选中的 ${selectedIds.length} 个任务吗？`
        apiEndpoint = '/api/group-sending/tasks/batch/pause'
        break
      case 'batch-terminate':
        confirmMessage = `确定要批量终止选中的 ${selectedIds.length} 个任务吗？`
        apiEndpoint = '/api/group-sending/tasks/batch/terminate'
        break
      case 'batch-restart':
        confirmMessage = `确定要批量重新开始选中的 ${selectedIds.length} 个任务吗？`
        apiEndpoint = '/api/group-sending/tasks/batch/restart'
        break
      case 'batch-delete':
        confirmMessage = `确定要批量删除选中的 ${selectedIds.length} 个任务吗？删除后无法恢复`
        apiEndpoint = '/api/group-sending/tasks/batch/delete'
        break
    }

    await ElMessageBox.confirm(confirmMessage, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        task_ids: selectedIds
      })
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(result.data.message)
      // 清空选择
      selectedTasks.value = []
      // 刷新列表
      fetchTasks()
    } else {
      ElMessage.error(result.message || '批量操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('网络错误')
    }
  }
}

const startTask = async (id: string) => {
  try {
    const response = await fetch(`/api/group-sending/tasks/${id}/start`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('任务启动成功')
      fetchTasks()
    } else {
      ElMessage.error(result.message || '启动任务失败')
    }
  } catch (error) {
    ElMessage.error('网络错误')
  }
}

const pauseTask = async (id: string) => {
  try {
    const response = await fetch(`/api/group-sending/tasks/${id}/pause`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('任务暂停成功')
      fetchTasks()
    } else {
      ElMessage.error(result.message || '暂停任务失败')
    }
  } catch (error) {
    ElMessage.error('网络错误')
  }
}

const terminateTask = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要终止此任务吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(`/api/group-sending/tasks/${id}/stop`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('任务终止成功')
      fetchTasks()
    } else {
      ElMessage.error(result.message || '终止任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('网络错误')
    }
  }
}

const deleteTask = async (id: string) => {
  try {
    // 获取任务信息以显示状态
    const task = tasks.value.find((t: any) => t.task_id === id)
    const statusText = task ? getStatusText(task.status) : '任务'

    let confirmMessage = '确定要删除此任务吗？删除后无法恢复'
    if (task) {
      if (['running', 'paused'].includes(task.status)) {
        confirmMessage = `确定要删除此${statusText}吗？删除后任务将立即停止，已发送的消息无法恢复`
      } else if (task.status === 'completed') {
        confirmMessage = `确定要删除此${statusText}吗？删除后将无法查看发送历史`
      }
    }

    await ElMessageBox.confirm(confirmMessage, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(`/api/group-sending/tasks/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('任务删除成功')
      fetchTasks()
    } else {
      ElMessage.error(result.message || '删除任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('网络错误')
    }
  }
}

const restartTask = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要重新开始此任务吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(`/api/group-sending/tasks/${id}/restart`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('任务重新开始成功')
      fetchTasks()
    } else {
      ElMessage.error(result.message || '重新开始任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('网络错误')
    }
  }
}

const replenishNumbers = (row: any) => {
  ElMessage.info('补号功能开发中...')
}

const getProgressPercentage = (row: any) => {
  if (row.customer_count === 0) return 0
  return Math.round((row.reached_count / row.customer_count) * 100)
}

const getSendingMethodText = (method: string) => {
  const methodMap = {
    'one_by_one': '逐条发送',
    'after_reply': '对方回复后发送'
  }
  return methodMap[method] || method
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待启动任务',
    'running': '运行中任务',
    'paused': '暂停中任务',
    'completed': '已完成任务',
    'terminated': '已终止任务'
  }
  return statusMap[status] || status
}

const getSendingTimesText = (row: any) => {
  if (row.sending_times_type === 'unlimited') {
    return '不限'
  }
  return `${row.sending_times}次/天`
}

const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchTasks()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchTasks()
}

const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

const canBatchStart = computed(() => {
  return selectedTasks.value.some(task => ['pending', 'paused'].includes(task.status))
})

const canBatchPause = computed(() => {
  return selectedTasks.value.some(task => task.status === 'running')
})

const canBatchTerminate = computed(() => {
  return selectedTasks.value.some(task => ['running', 'paused'].includes(task.status))
})

const canBatchRestart = computed(() => {
  return selectedTasks.value.some(task => ['completed', 'terminated'].includes(task.status))
})

// 监听路由变化，自动刷新列表
watch(() => route.query.refresh, async (newVal) => {
  if (newVal === 'true') {
    console.log('GroupSendingList watch - 检测到刷新参数，执行刷新')
    // 清除刷新参数
    await router.replace({ query: {} })
    // 等待DOM更新完成后刷新任务列表
    await nextTick()
    fetchTasks()
  }
}, { immediate: false })

// 生命周期
onMounted(() => {
  console.log('GroupSendingList onMounted - 开始加载任务列表')

  // 检查是否需要自动刷新
  if (route.query.refresh === 'true') {
    console.log('GroupSendingList onMounted - 检测到刷新参数，清除参数')
    // 清除刷新参数
    router.replace({ query: {} })
  }

  // 统一调用一次fetchTasks
  fetchTasks()
})
</script>

<style scoped>
.group-sending-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  background: var(--el-bg-color);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
}

.task-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--el-text-color-regular);
  min-width: 40px;
}

.info-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  cursor: help;
}

.sending-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sending-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.sending-item .label {
  color: var(--el-text-color-regular);
}

.sending-item .value {
  font-weight: 500;
}

.sending-item .value.available {
  color: var(--el-color-success);
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-light);
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-progress-bar__outer) {
  background-color: var(--el-fill-color-lighter);
}

:deep(.el-progress-bar__inner) {
  background-color: var(--el-color-primary);
}
</style>