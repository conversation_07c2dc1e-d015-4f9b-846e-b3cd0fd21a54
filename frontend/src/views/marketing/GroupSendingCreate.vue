<template>
  <div class="group-sending-create">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>{{ isEditMode ? '编辑陌生人群发任务' : '新建陌生人群发任务' }}</h2>
      <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="create-form">
        <!-- 任务名称 -->
        <el-form-item label="*任务名称" prop="task_name">
          <el-input v-model="form.task_name" placeholder="请输入任务名称" style="width: 400px" />
        </el-form-item>

        <!-- 使用账号 -->
        <el-form-item label="*使用账号" prop="account_usage">
          <el-radio-group v-model="form.account_usage">
            <el-radio value="auto">
              自动分配账号 (可用发送次数{{ availableMessages }})
            </el-radio>
            <el-radio value="manual">
              选择账号 (可用发送次数{{ availableMessages }})
            </el-radio>
          </el-radio-group>

          <!-- 选择账号下拉框 -->
          <div v-if="form.account_usage === 'manual'" class="account-select">
            <el-select v-model="form.selected_account_id" placeholder="请选择账号" style="width: 300px" clearable>
              <el-option v-for="account in availableAccounts" :key="account.id"
                :label="`${account.account_name} (${account.phone_number})`" :value="account.id" />
            </el-select>
          </div>
        </el-form-item>

        <!-- 客户资源 -->
        <el-form-item label="*客户资源" prop="customer_file">
          <el-upload class="upload-demo" action="#" :auto-upload="false" :on-change="handleFileChange"
            :file-list="fileList" accept=".txt" :limit="1">
            <el-button type="primary" :icon="Upload">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传不超过10MB的txt文件,文件格式:一行一个号码
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 客户间隔 -->
        <el-form-item label="*客户间隔" prop="customer_interval">
          <el-input-number v-model="form.customer_interval" :min="1" :max="3600" controls-position="right"
            style="width: 200px" />
          <span class="unit-text">秒</span>
        </el-form-item>

        <!-- 发送次数 -->
        <el-form-item label="*发送次数" prop="sending_times_type">
          <el-radio-group v-model="form.sending_times_type">
            <el-radio value="unlimited">不限</el-radio>
            <el-radio value="limited">
              最多
              <el-input-number v-if="form.sending_times_type === 'limited'" v-model="form.sending_times" :min="1"
                :max="100" controls-position="right" style="width: 100px; margin-left: 8px" />
              <span class="unit-text">次/天</span>
            </el-radio>
          </el-radio-group>
          <div class="hint-text">
            为避免风控,每个账号每天最多给多少个号码发送信息
          </div>
        </el-form-item>

        <!-- 发送方式 -->
        <el-form-item label="*发送方式" prop="sending_method">
          <el-radio-group v-model="form.sending_method">
            <el-radio value="one_by_one">逐条发送</el-radio>
            <el-radio value="after_reply">对方回复后发送</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 语句间隔 -->
        <el-form-item label="语句间隔" prop="sentence_interval">
          <el-input-number v-model="form.sentence_interval" :min="1" :max="60" controls-position="right"
            style="width: 200px" />
          <span class="unit-text">秒</span>
        </el-form-item>

        <!-- 群发语句 -->
        <el-form-item label="*群发语句" prop="statements">
          <div class="statements-container">
            <div class="statements-actions">
              <el-button type="primary" size="small" @click="addStatement('call')">
                插入拨打电话动作
              </el-button>
              <el-button type="primary" size="small" @click="addStatement('text')">
                添加文字语句
              </el-button>
              <el-button type="primary" size="small" @click="openMediaUpload">
                上传图片/视频
              </el-button>
              <el-button type="primary" size="small" @click="openMaterialLibrary">
                打开素材库
              </el-button>
            </div>

            <!-- 隐藏的文件输入 -->
            <input
              ref="mediaFileInput"
              type="file"
              accept=".jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.mkv"
              style="display: none"
              @change="handleDirectMediaUpload"
            />

            <el-table :data="form.statements" style="width: 100%; margin-top: 16px">
              <el-table-column prop="order" label="顺序" width="80" align="center" />
              <el-table-column prop="type" label="类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatementTypeTag(row.type)">
                    {{ getStatementTypeText(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="material_name" label="素材名称" width="150" />
              <el-table-column prop="content" label="内容" show-overflow-tooltip>
                <template #default="{ row }">
                  <div v-if="row.type === 'text'">{{ row.content }}</div>
                  <div v-else-if="row.type === 'image'" class="media-content">
                    <el-icon><Picture /></el-icon>
                    <span>{{ row.material_name || '图片文件' }}</span>
                    <el-tag size="small" type="warning">图片</el-tag>
                  </div>
                  <div v-else-if="row.type === 'video'" class="media-content">
                    <el-icon><VideoPlay /></el-icon>
                    <span>{{ row.material_name || '视频文件' }}</span>
                    <el-tag size="small" type="danger">视频</el-tag>
                  </div>
                  <div v-else-if="row.type === 'call'" class="media-content">
                    <el-icon><Phone /></el-icon>
                    <span>拨打电话动作</span>
                    <el-tag size="small" type="info">电话</el-tag>
                  </div>
                  <div v-else>{{ row.content }}</div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="{ row, $index }">
                  <el-button link size="small" @click="editStatement($index)">
                    编辑
                  </el-button>
                  <el-button link size="small" @click="removeStatement($index)" style="color: #f56c6c">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>

        <!-- 号码检测 -->
        <el-form-item label="号码检测">
          <el-radio-group v-model="form.number_detection">
            <el-radio :value="false">不检测</el-radio>
            <el-radio :value="true">检测</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 预约时间 -->
        <el-form-item label="预约时间">
          <el-date-picker v-model="form.scheduled_time" type="datetime" placeholder="选择预约时间"
            format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DDTHH:mm:ssZ" style="width: 300px" />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEditMode ? '更新任务' : '创建任务' }}
          </el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 编辑语句弹框 -->
    <el-dialog v-model="editDialogVisible" title="编辑语句" width="600px" :close-on-click-modal="false">
      <el-form :model="editingStatement" label-width="100px">
        <el-form-item label="语句类型">
          <el-select v-model="editingStatement.type" placeholder="请选择语句类型">
            <el-option label="文字" value="text" />
            <el-option label="图片" value="image" />
            <el-option label="视频" value="video" />
            <el-option label="拨打电话" value="call" />
          </el-select>
        </el-form-item>

        <el-form-item label="素材名称">
          <el-input v-model="editingStatement.material_name" placeholder="请输入素材名称" />
        </el-form-item>

        <el-form-item label="内容" v-if="editingStatement.type === 'text'">
          <el-input v-model="editingStatement.content" type="textarea" :rows="4" placeholder="请输入文字内容" />
        </el-form-item>

        <el-form-item label="文件信息" v-if="['image', 'video'].includes(editingStatement.type)">
          <div class="media-info-display">
            <p><strong>文件名:</strong> {{ editingStatement.material_name || '未选择文件' }}</p>
            <p><strong>类型:</strong> {{ editingStatement.type === 'image' ? '图片' : '视频' }}</p>
            <p v-if="editingStatement.file_url"><strong>状态:</strong>
              <el-tag type="success" size="small">已上传</el-tag>
            </p>
            <p v-else><strong>状态:</strong>
              <el-tag type="warning" size="small">未上传</el-tag>
            </p>
          </div>
        </el-form-item>

        <el-form-item label="描述文字" v-if="['image', 'video'].includes(editingStatement.type)">
          <el-input v-model="editingStatement.content" type="textarea" :rows="3"
            placeholder="可选：为图片或视频添加描述文字" />
        </el-form-item>

        <el-form-item label="持续时间" v-if="editingStatement.type === 'video'">
          <el-input-number v-model="editingStatement.duration" :min="1" :max="300" controls-position="right"
            style="width: 200px" />
          <span class="unit-text">秒</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 素材库弹框 -->
    <el-dialog v-model="materialDialogVisible" title="素材库" width="800px" :before-close="closeMaterialDialog">
      <div class="material-library">
        <div class="material-tabs">
          <el-tabs v-model="activeMaterialTab">
            <el-tab-pane label="文字素材" name="text">
              <div class="material-list">
                <div v-for="material in textMaterials" :key="material.id" class="material-item"
                  @click="selectMaterial(material)">
                  <div class="material-content">{{ material.content }}</div>
                  <div class="material-info">
                    <span>{{ material.name }}</span>
                    <span>{{ formatTime(material.created_at) }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="图片素材" name="image">
              <div class="material-list">
                <div v-for="material in imageMaterials" :key="material.id" class="material-item"
                  @click="selectMaterial(material)">
                  <img :src="material.url" :alt="material.name" class="material-image" />
                  <div class="material-info">
                    <span>{{ material.name }}</span>
                    <span>{{ formatTime(material.created_at) }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Upload,
  Picture,
  VideoPlay,
  Phone
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 编辑模式判断
const isEditMode = computed(() => !!route.query.id)
const taskId = computed(() => route.query.id as string)

// 响应式数据
const loading = ref(false)
const formRef = ref()
const materialDialogVisible = ref(false)
const activeMaterialTab = ref('text')
const fileList = ref([])
const availableMessages = ref(0)
const availableAccounts = ref([])

// 编辑语句相关
const editDialogVisible = ref(false)
const editingIndex = ref(-1)
const editingStatement = ref({
  order: 1,
  type: 'text',
  material_name: '',
  content: '',
  file_url: '',
  duration: 0
})
const mediaFileList = ref([])
const uploadingMedia = ref(false)
const mediaUploadStatus = ref('') // 'uploading', 'success', 'failed'
const mediaPreviewUrl = ref('')
const mediaFileInput = ref()

// 表单数据
const form = reactive({
  task_name: '',
  account_usage: 'auto',
  selected_account_id: null,
  customer_file: '',
  customer_interval: 60,
  sending_times: 1,
  sending_times_type: 'limited',
  sending_method: 'one_by_one',
  sentence_interval: 1,
  number_detection: false,
  scheduled_time: '',
  statements: []
})

// 验证规则
const rules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  account_usage: [
    { required: true, message: '请选择账号使用方式', trigger: 'change' }
  ],
  customer_file: [
    { required: true, message: '请上传客户文件', trigger: 'change' }
  ],
  customer_interval: [
    { required: true, message: '请设置客户间隔', trigger: 'blur' }
  ],
  sending_times_type: [
    { required: true, message: '请选择发送次数类型', trigger: 'change' }
  ],
  sending_method: [
    { required: true, message: '请选择发送方式', trigger: 'change' }
  ]
}

// 模拟素材数据
const textMaterials = ref([
  { id: 1, name: '欢迎语', content: '您好，欢迎使用我们的服务！', created_at: '2025-07-30 10:00:00' },
  { id: 2, name: '产品介绍', content: '我们提供优质的产品和服务，欢迎咨询！', created_at: '2025-07-30 11:00:00' }
])

const imageMaterials = ref([
  { id: 1, name: '产品图片1', url: 'https://via.placeholder.com/150', created_at: '2025-07-30 10:00:00' },
  { id: 2, name: '产品图片2', url: 'https://via.placeholder.com/150', created_at: '2025-07-30 11:00:00' }
])

// 方法
const goBack = () => {
  router.back()
}

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!isEditMode.value) return

  try {
    loading.value = true
    const response = await fetch(`/api/group-sending/tasks/${taskId.value}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      const task = result.data.task
      const statements = result.data.statements

      // 加载表单数据
      form.task_name = task.task_name
      form.account_usage = task.account_usage
      form.selected_account_id = task.selected_account_id
      form.customer_file = task.customer_file
      form.customer_interval = task.customer_interval
      form.sending_times = task.sending_times
      form.sending_times_type = task.sending_times_type
      form.sending_method = task.sending_method
      form.sentence_interval = task.sentence_interval
      form.number_detection = task.number_detection
      form.scheduled_time = task.scheduled_time

      // 处理statements
      if (statements && statements.length > 0) {
        // 如果后端返回的是statements数组，直接使用
        form.statements = statements
      } else if (task.statements) {
        // 如果task中有statements字段，尝试解析JSON
        try {
          form.statements = JSON.parse(task.statements)
        } catch (error) {
          console.error('解析statements失败:', error)
          form.statements = []
        }
      } else {
        form.statements = []
      }

      // 设置文件列表
      if (task.customer_file) {
        fileList.value = [{
          name: task.customer_file,
          status: 'success'
        }]
      }

      ElMessage.success('任务数据加载成功')
    } else {
      ElMessage.error(result.message || '加载任务数据失败')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('网络错误')
  } finally {
    loading.value = false
  }
}

const handleFileChange = async (file: any) => {
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }

  if (!file.name.endsWith('.txt')) {
    ElMessage.error('只能上传txt文件')
    return false
  }

  try {
    // 上传文件到后端
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('category', 'customer_list')

    const response = await fetch('/api/files/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData
    })

    const result = await response.json()

    if (result.code === 200) {
      // 保存上传后的文件路径
      form.customer_file = result.data.file.file_path
      ElMessage.success('文件上传成功')
      console.log('客户文件上传成功:', result.data.file.file_path)
    } else {
      ElMessage.error(result.message || '文件上传失败')
      return false
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
    return false
  }

  return true
}

const addStatement = (type: string) => {
  const newStatement = {
    order: form.statements.length + 1,
    type: type,
    material_name: '',
    content: '',
    file_url: '',
    duration: 0
  }

  form.statements.push(newStatement)

  // 如果是文字类型，直接打开编辑弹框
  if (type === 'text') {
    editingIndex.value = form.statements.length - 1
    editingStatement.value = { ...newStatement }
    editDialogVisible.value = true
  }
}

// 打开媒体文件选择器
const openMediaUpload = () => {
  if (mediaFileInput.value) {
    mediaFileInput.value.click()
  }
}

// 处理直接媒体文件上传
const handleDirectMediaUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 验证文件类型和大小
  const isValidType = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/avi', 'video/mov', 'video/mkv'
  ].includes(file.type)

  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isValidType) {
    ElMessage.error('只能上传图片(jpg,png,gif,webp)或视频(mp4,avi,mov,mkv)格式的文件!')
    target.value = '' // 清空文件输入
    return
  }

  if (!isLt50M) {
    ElMessage.error('文件大小不能超过 50MB!')
    target.value = '' // 清空文件输入
    return
  }

  try {
    // 显示上传中状态
    const loadingMessage = ElMessage({
      message: '正在上传文件...',
      type: 'info',
      duration: 0
    })

    // 上传文件到服务器
    const formData = new FormData()
    formData.append('file', file)

    // 根据文件类型确定分类
    const isImage = file.type.startsWith('image/')
    formData.append('category', isImage ? 'image' : 'video')

    const response = await fetch('/api/files/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData
    })

    const result = await response.json()

    loadingMessage.close()

    if (result.code === 200) {
      // 创建新的语句并添加到列表
      const newStatement = {
        order: form.statements.length + 1,
        type: isImage ? 'image' : 'video',
        material_name: result.data.file.original_name,
        content: result.data.file.original_name,
        file_url: result.data.file.file_path,
        duration: isImage ? 0 : 10 // 视频默认10秒
      }

      form.statements.push(newStatement)

      ElMessage.success(`${isImage ? '图片' : '视频'}上传成功并已添加到语句列表`)
      console.log('媒体文件上传成功:', result.data.file.file_path)
    } else {
      ElMessage.error(result.message || '文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  } finally {
    // 清空文件输入，允许重复选择同一文件
    target.value = ''
  }
}

const editStatement = (index: number) => {
  editingIndex.value = index
  editingStatement.value = { ...form.statements[index] }
  editDialogVisible.value = true
}

const cancelEdit = () => {
  editDialogVisible.value = false
  editingIndex.value = -1
  editingStatement.value = {
    order: 1,
    type: 'text',
    material_name: '',
    content: '',
    file_url: '',
    duration: 0
  }
}

const saveEdit = () => {
  if (editingIndex.value >= 0) {
    form.statements[editingIndex.value] = { ...editingStatement.value }
  }
  editDialogVisible.value = false
  editingIndex.value = -1
  ElMessage.success('语句编辑成功')
}



const removeStatement = (index: number) => {
  form.statements.splice(index, 1)
  // 重新排序
  form.statements.forEach((item, idx) => {
    item.order = idx + 1
  })
}

const openMaterialLibrary = () => {
  materialDialogVisible.value = true
}

const closeMaterialDialog = () => {
  materialDialogVisible.value = false
}

const selectMaterial = (material: any) => {
  const newStatement = {
    order: form.statements.length + 1,
    type: activeMaterialTab.value === 'text' ? 'text' : 'image',
    material_name: material.name,
    content: material.content || '',
    file_url: material.url || '',
    duration: 0
  }

  form.statements.push(newStatement)
  closeMaterialDialog()
  ElMessage.success('素材已添加到语句列表')
}

const getStatementTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    'text': 'success',
    'image': 'warning',
    'video': 'danger',
    'call': 'info'
  }
  return typeMap[type] || 'info'
}

const getStatementTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'text': '文字',
    'image': '图片',
    'video': '视频',
    'call': '拨打电话'
  }
  return typeMap[type] || type
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

const fetchAvailableAccounts = async () => {
  try {
    const response = await fetch('/api/whatsapp/accounts', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      availableAccounts.value = result.data.accounts.filter((account: any) =>
        account.status === 'connected'
      )
    }
  } catch (error) {
    console.error('获取可用账号失败:', error)
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    loading.value = true

    // 准备提交数据，确保statements是JSON字符串
    const submitData = {
      ...form,
      statements: JSON.stringify(form.statements)
    }

    const url = isEditMode.value
      ? `/api/group-sending/tasks/${taskId.value}`
      : '/api/group-sending/tasks'

    const method = isEditMode.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(submitData)
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(isEditMode.value ? '群发任务更新成功' : '群发任务创建成功')

      // 延迟一下再跳转，确保后端数据已经保存
      setTimeout(() => {
        // 跳转到列表页面并传递刷新参数
        router.push({
          path: '/marketing/group-sending',
          query: { refresh: 'true', t: Date.now() } // 添加时间戳确保参数变化
        })
      }, 500) // 延迟500ms
    } else {
      ElMessage.error(result.message || (isEditMode.value ? '更新任务失败' : '创建任务失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('网络错误')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchAvailableAccounts()
  if (isEditMode.value) {
    fetchTaskDetail()
  }
})
</script>

<style scoped>
.group-sending-create {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.form-container {
  background: var(--el-bg-color);
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.create-form {
  max-width: 800px;
}

.account-select {
  margin-top: 12px;
}

.unit-text {
  margin-left: 8px;
  color: var(--el-text-color-regular);
}

.hint-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.statements-container {
  width: 100%;
}

.statements-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.material-library {
  height: 400px;
}

.material-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
}

.material-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.material-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.material-content {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  word-break: break-all;
}

.material-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 8px;
}

.material-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-radio__label) {
  font-size: 14px;
}

.media-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.media-content .el-icon {
  color: var(--el-color-primary);
}

.media-content span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 媒体信息显示样式 */
.media-info-display {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.media-info-display p {
  margin: 6px 0;
  font-size: 14px;
  color: #606266;
}

.media-info-display strong {
  color: #303133;
  margin-right: 8px;
}
</style>