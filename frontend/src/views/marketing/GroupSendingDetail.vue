<template>
  <div class="group-sending-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>群发任务详情</h2>
      <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="task" class="detail-container">
      <!-- 任务基本信息 -->
      <el-card class="task-info-card">
        <template #header>
          <div class="card-header">
            <span>任务基本信息</span>
            <div class="header-actions">
              <StatusTag :status="task.status" size="small" />
            </div>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">{{ task.task_name }}</el-descriptions-item>
          <el-descriptions-item label="任务ID">{{ task.task_id }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ task.creator_name }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(task.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="账号使用方式">
            {{ task.account_usage === 'auto' ? '自动分配' : '选择账号' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户间隔">{{ task.customer_interval }}秒</el-descriptions-item>
          <el-descriptions-item label="发送方式">
            {{ getSendingMethodText(task.sending_method) }}
          </el-descriptions-item>
          <el-descriptions-item label="语句间隔">{{ task.sentence_interval }}秒</el-descriptions-item>
          <el-descriptions-item label="发送次数">
            {{ getSendingTimesText(task) }}
          </el-descriptions-item>
          <el-descriptions-item label="号码检测">
            {{ task.number_detection ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="预约时间">
            {{ formatTime(task.scheduled_time) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 任务统计 -->
      <el-card class="task-stats-card">
        <template #header>
          <span>任务统计</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ task.customer_count }}</div>
              <div class="stat-label">客户总数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ task.reached_count }}</div>
              <div class="stat-label">已触达客户</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ task.sent_messages }}</div>
              <div class="stat-label">已发送消息</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ task.failed_messages }}</div>
              <div class="stat-label">失败消息</div>
            </div>
          </el-col>
        </el-row>
        
        <el-progress 
          :percentage="getProgressPercentage(task)" 
          :stroke-width="20"
          :show-text="false"
          style="margin-top: 20px"
        />
        <div class="progress-text">
          进度: {{ task.reached_count }}/{{ task.customer_count }} ({{ getProgressPercentage(task) }}%)
        </div>
      </el-card>

      <!-- 群发语句 -->
      <el-card class="statements-card">
        <template #header>
          <span>群发语句</span>
        </template>
        
        <el-table :data="statements" style="width: 100%">
          <el-table-column prop="order" label="顺序" width="80" align="center" />
          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatementTypeTag(row.type)">
                {{ getStatementTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="material_name" label="素材名称" width="150" />
          <el-table-column prop="content" label="内容" show-overflow-tooltip />
          <el-table-column prop="file_url" label="文件" width="100">
            <template #default="{ row }">
              <el-link v-if="row.file_url" :href="row.file_url" target="_blank">
                查看
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 任务操作 -->
      <el-card class="task-actions-card">
        <template #header>
          <span>任务操作</span>
        </template>
        
        <div class="actions-container">
          <el-button 
            v-if="task.status === 'pending'" 
            type="primary" 
            @click="startTask"
            :loading="actionLoading"
          >
            启动任务
          </el-button>
          <el-button 
            v-if="task.status === 'running'" 
            type="warning" 
            @click="pauseTask"
            :loading="actionLoading"
          >
            暂停任务
          </el-button>
          <el-button 
            v-if="task.status === 'paused'" 
            type="primary" 
            @click="startTask"
            :loading="actionLoading"
          >
            继续任务
          </el-button>
          <el-button 
            v-if="['running', 'paused'].includes(task.status)" 
            type="danger" 
            @click="terminateTask"
            :loading="actionLoading"
          >
            终止任务
          </el-button>
          <el-button 
            v-if="task.status === 'pending'" 
            type="info" 
            @click="editTask"
          >
            编辑任务
          </el-button>
          <el-button 
            type="success" 
            @click="viewLogs"
          >
            查看日志
          </el-button>
        </div>
      </el-card>

      <!-- 发送日志 -->
      <el-card v-if="showLogs" class="logs-card">
        <template #header>
          <span>发送日志</span>
        </template>
        
        <el-table :data="logs" style="width: 100%" v-loading="logsLoading">
          <el-table-column prop="phone_number" label="手机号" width="150" />
          <el-table-column prop="message" label="消息内容" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getLogStatusType(row.status)">
                {{ getLogStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="error_msg" label="错误信息" show-overflow-tooltip />
          <el-table-column prop="sent_at" label="发送时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.sent_at) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <div v-else class="error-container">
      <el-empty description="任务不存在或已被删除" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import StatusTag from '@/components/StatusTag.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const actionLoading = ref(false)
const logsLoading = ref(false)
const task = ref(null)
const statements = ref([])
const logs = ref([])
const showLogs = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 计算属性
const taskId = computed(() => route.params.id)

// 方法
const goBack = () => {
  router.back()
}

const fetchTaskDetail = async () => {
  try {
    loading.value = true
    const response = await fetch(`/api/group-sending/tasks/${taskId.value}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    
    if (result.code === 200) {
      task.value = result.data.task
      statements.value = result.data.statements || []
    } else {
      ElMessage.error(result.message || '获取任务详情失败')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('网络错误')
  } finally {
    loading.value = false
  }
}

const fetchLogs = async () => {
  try {
    logsLoading.value = true
    const response = await fetch(`/api/group-sending/tasks/${taskId.value}/logs`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    
    if (result.code === 200) {
      logs.value = result.data.logs
      total.value = result.data.total
    } else {
      ElMessage.error(result.message || '获取日志失败')
    }
  } catch (error) {
    console.error('获取日志失败:', error)
    ElMessage.error('网络错误')
  } finally {
    logsLoading.value = false
  }
}

const startTask = async () => {
  try {
    actionLoading.value = true
    const response = await fetch(`/api/group-sending/tasks/${taskId.value}/start`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('任务启动成功')
      fetchTaskDetail()
    } else {
      ElMessage.error(result.message || '启动任务失败')
    }
  } catch (error) {
    ElMessage.error('网络错误')
  } finally {
    actionLoading.value = false
  }
}

const pauseTask = async () => {
  try {
    actionLoading.value = true
    const response = await fetch(`/api/group-sending/tasks/${taskId.value}/pause`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('任务暂停成功')
      fetchTaskDetail()
    } else {
      ElMessage.error(result.message || '暂停任务失败')
    }
  } catch (error) {
    ElMessage.error('网络错误')
  } finally {
    actionLoading.value = false
  }
}

const terminateTask = async () => {
  try {
    await ElMessageBox.confirm('确定要终止此任务吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    actionLoading.value = true
    const response = await fetch(`/api/group-sending/tasks/${taskId.value}/terminate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('任务终止成功')
      fetchTaskDetail()
    } else {
      ElMessage.error(result.message || '终止任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('网络错误')
    }
  } finally {
    actionLoading.value = false
  }
}

const editTask = () => {
  router.push(`/marketing/group-sending/create?id=${task.value.task_id}`)
}

const viewLogs = () => {
  showLogs.value = !showLogs.value
  if (showLogs.value && logs.value.length === 0) {
    fetchLogs()
  }
}

const getSendingMethodText = (method: string) => {
  const methodMap = {
    'one_by_one': '逐条发送',
    'after_reply': '对方回复后发送'
  }
  return methodMap[method] || method
}

const getSendingTimesText = (task: any) => {
  if (task.sending_times_type === 'unlimited') {
    return '不限'
  }
  return `${task.sending_times}次/天`
}

const getProgressPercentage = (task: any) => {
  if (task.customer_count === 0) return 0
  return Math.round((task.reached_count / task.customer_count) * 100)
}

const getStatementTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    'text': 'success',
    'image': 'warning',
    'video': 'danger',
    'call': 'info'
  }
  return typeMap[type] || 'info'
}

const getStatementTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'text': '文字',
    'image': '图片',
    'video': '视频',
    'call': '拨打电话'
  }
  return typeMap[type] || type
}

const getLogStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

const getLogStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'success': '成功',
    'failed': '失败',
    'pending': '待发送'
  }
  return statusMap[status] || status
}

const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchLogs()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchLogs()
}

// 生命周期
onMounted(() => {
  fetchTaskDetail()
})
</script>

<style scoped>
.group-sending-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.loading-container {
  padding: 20px;
}

.detail-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.task-info-card,
.task-stats-card,
.statements-card,
.task-actions-card,
.logs-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: var(--el-fill-color-light);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.actions-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-card__header) {
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
}
</style> 