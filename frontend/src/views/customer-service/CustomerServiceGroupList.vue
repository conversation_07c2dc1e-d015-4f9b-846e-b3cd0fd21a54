<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header fade-in">
      <div>
        <h2 class="page-title">客服分组管理</h2>
        <p class="page-subtitle">管理客服分组和分配策略</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          创建分组
        </el-button>
        <el-dropdown @command="handleBatchAction" style="margin-left: 8px;">
          <el-button :icon="Operation">
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="set_daily_limit">设置每日自动分配上限</el-dropdown-item>
              <el-dropdown-item command="enable_assign">开启分配新会话</el-dropdown-item>
              <el-dropdown-item command="disable_assign">关闭分配新会话</el-dropdown-item>
              <el-dropdown-item command="delete" divided>批量删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section fade-in" style="animation-delay: 0.1s">
      <el-form :model="searchForm" inline>
        <el-form-item label="分组名称">
          <el-input 
            v-model="searchForm.groupName" 
            placeholder="请输入分组名称"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            查询
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section fade-in" style="animation-delay: 0.2s">
      <el-table 
        :data="filteredGroups" 
        :loading="loading"
        stripe
        style="width: 100%"
        empty-text="暂无分组数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="group_name" label="分组名称" min-width="150">
          <template #default="{ row }">
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-icon><UserFilled /></el-icon>
              <span style="font-weight: 500;">{{ row.group_name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="service_count" label="客服数量" width="100" align="center">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              @click="viewGroupServices(row)"
            >
              {{ row.service_count }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="daily_assign_limit" label="每日分配上限" width="120" align="center">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              @click="editDailyLimit(row)"
            >
              {{ row.daily_assign_limit }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="is_assign_new_session" label="分配新会话" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_assign_new_session ? 'success' : 'info'" size="small">
              {{ row.is_assign_new_session ? '开启' : '关闭' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200">
          <template #default="{ row }">
            <span class="text-gray-600">{{ row.description || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="150" align="center">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleRowAction(command, row)">
              <el-button size="small" :icon="MoreFilled">
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`view_services_${row.id}`">
                    查看客服
                  </el-dropdown-item>
                  <el-dropdown-item :command="`edit_limit_${row.id}`">
                    设置分配上限
                  </el-dropdown-item>
                  <el-dropdown-item :command="`toggle_assign_${row.id}`">
                    {{ row.is_assign_new_session ? '关闭' : '开启' }}分配新会话
                  </el-dropdown-item>
                  <el-dropdown-item :command="`delete_${row.id}`" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredGroups.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 分组表单对话框 -->
    <CustomerServiceGroupForm
      v-model:visible="formVisible"
      :mode="formMode"
      :group="currentGroup"
      @success="handleFormSuccess"
    />

    <!-- 批量操作对话框 -->
    <BatchGroupActionDialog
      v-model:visible="batchDialogVisible"
      :action="batchAction"
      :selected-groups="selectedGroups"
      @success="handleBatchSuccess"
    />

    <!-- 分组客服列表对话框 -->
    <GroupServicesDialog
      v-model:visible="servicesDialogVisible"
      :group="selectedGroup"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Operation, 
  ArrowDown,
  MoreFilled,
  UserFilled
} from '@element-plus/icons-vue'
import CustomerServiceGroupForm from './components/CustomerServiceGroupForm.vue'
import BatchGroupActionDialog from './components/BatchGroupActionDialog.vue'
import GroupServicesDialog from './components/GroupServicesDialog.vue'
import { useAuthStore } from '@/stores/auth'
import { formatTime } from '@/utils/date'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const groups = ref([])
const selectedGroups = ref([])

// 搜索表单
const searchForm = reactive({
  groupName: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 表单相关
const formVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentGroup = ref(null)

// 批量操作
const batchDialogVisible = ref(false)
const batchAction = ref('')

// 分组客服列表
const servicesDialogVisible = ref(false)
const selectedGroup = ref(null)

// 计算属性
const filteredGroups = computed(() => {
  let filtered = groups.value

  if (searchForm.groupName) {
    filtered = filtered.filter(group => 
      group.group_name?.toLowerCase().includes(searchForm.groupName.toLowerCase())
    )
  }

  return filtered
})

// 生命周期
onMounted(() => {
  loadGroups()
})

// 方法
async function loadGroups() {
  try {
    loading.value = true
    const response = await fetch('/api/admin/customer-service-groups', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      groups.value = result.data.groups || []
    } else {
      ElMessage.error(result.message || '加载分组列表失败')
    }
  } catch (error) {
    ElMessage.error('加载分组列表失败')
  } finally {
    loading.value = false
  }
}

function handleAdd() {
  formMode.value = 'create'
  currentGroup.value = null
  formVisible.value = true
}

function handleEdit(row: any) {
  formMode.value = 'edit'
  currentGroup.value = row
  formVisible.value = true
}

function handleSearch() {
  currentPage.value = 1
}

function handleReset() {
  searchForm.groupName = ''
  currentPage.value = 1
}

function handleSelectionChange(selection: any[]) {
  selectedGroups.value = selection
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
}

function handleCurrentChange(page: number) {
  currentPage.value = page
}

function handleBatchAction(command: string) {
  if (selectedGroups.value.length === 0) {
    ElMessage.warning('请先选择要操作的分组')
    return
  }
  
  batchAction.value = command
  batchDialogVisible.value = true
}

function handleRowAction(command: string, row: any) {
  const [action, id] = command.split('_')
  
  switch (action) {
    case 'view':
      viewGroupServices(row)
      break
    case 'edit':
      editDailyLimit(row)
      break
    case 'toggle':
      toggleAssignSession(row)
      break
    case 'delete':
      deleteGroup(row)
      break
  }
}

function viewGroupServices(group: any) {
  selectedGroup.value = group
  servicesDialogVisible.value = true
}

function editDailyLimit(group: any) {
  // TODO: 实现编辑每日分配上限
  ElMessage.info('功能开发中')
}

async function toggleAssignSession(group: any) {
  const newStatus = !group.is_assign_new_session
  const action = newStatus ? '开启' : '关闭'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}分组 "${group.group_name}" 的分配新会话功能吗？`,
      `确认${action}`,
      { type: 'warning' }
    )
    
    const response = await fetch(`/api/admin/customer-service-groups/${group.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...group,
        is_assign_new_session: newStatus
      })
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success(`${action}成功`)
      loadGroups()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

async function deleteGroup(group: any) {
  if (group.service_count > 0) {
    ElMessage.warning('分组下还有客服，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除分组 "${group.group_name}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const response = await fetch(`/api/admin/customer-service-groups/${group.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('删除成功')
      loadGroups()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

function handleFormSuccess() {
  formVisible.value = false
  loadGroups()
}

function handleBatchSuccess() {
  batchDialogVisible.value = false
  selectedGroups.value = []
  loadGroups()
}
</script>

<style scoped>
.page-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.page-title {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.page-subtitle {
  margin: 4px 0 0 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.search-section {
  background: var(--el-bg-color);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-section {
  background: var(--el-bg-color);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
