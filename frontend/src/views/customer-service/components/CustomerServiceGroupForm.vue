<template>
  <el-dialog
    :model-value="visible"
    :title="mode === 'create' ? '创建分组' : '编辑分组'"
    width="500px"
    @close="handleClose"
    @update:model-value="(value) => emit('update:visible', value)"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="分组名称" prop="group_name">
        <el-input
          v-model="formData.group_name"
          placeholder="请输入分组名称"
        />
      </el-form-item>
      
      <el-form-item label="分组描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入分组描述（可选）"
        />
      </el-form-item>
      
      <el-form-item label="每日分配上限" prop="daily_assign_limit">
        <el-input-number
          v-model="formData.daily_assign_limit"
          :min="1"
          :max="10000"
          placeholder="每日分配上限"
        />
        <div class="form-tip">
          分组下所有客服的每日分配总上限
        </div>
      </el-form-item>
      
      <el-form-item label="分配新会话">
        <el-switch
          v-model="formData.is_assign_new_session"
          active-text="开启"
          inactive-text="关闭"
        />
        <div class="form-tip">
          是否允许分组下的客服接收新的会话分配
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitting" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
  mode: 'create' | 'edit'
  group?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  group: null
})

const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()

// 表单相关
const formRef = ref<FormInstance>()
const submitting = ref(false)

const formData = reactive({
  group_name: '',
  description: '',
  daily_assign_limit: 100,
  is_assign_new_session: true
})

const formRules: FormRules = {
  group_name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  daily_assign_limit: [
    { required: true, message: '请输入每日分配上限', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '每日分配上限必须在 1 到 10000 之间', trigger: 'blur' }
  ]
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    if (props.mode === 'edit' && props.group) {
      nextTick(() => {
        Object.assign(formData, {
          group_name: props.group.group_name || '',
          description: props.group.description || '',
          daily_assign_limit: props.group.daily_assign_limit || 100,
          is_assign_new_session: props.group.is_assign_new_session ?? true
        })
      })
    }
  }
})

function resetForm() {
  Object.assign(formData, {
    group_name: '',
    description: '',
    daily_assign_limit: 100,
    is_assign_new_session: true
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

function handleClose() {
  emit('update:visible', false)
}

async function handleSubmit() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const url = props.mode === 'create' 
      ? '/api/admin/customer-service-groups'
      : `/api/admin/customer-service-groups/${props.group.id}`
    
    const method = props.mode === 'create' ? 'POST' : 'PUT'
    
    const response = await fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
      emit('success')
    } else {
      ElMessage.error(result.message || (props.mode === 'create' ? '创建失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}
</style>
