<template>
  <el-dialog
    :model-value="visible"
    :title="getDialogTitle()"
    width="500px"
    @close="handleClose"
    @update:model-value="(value) => emit('update:visible', value)"
  >
    <div class="batch-info">
      <p>已选择 <strong>{{ selectedGroups.length }}</strong> 个分组</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 每日分配上限 -->
      <el-form-item 
        v-if="action === 'set_daily_limit'" 
        label="每日分配上限" 
        prop="daily_assign_limit"
      >
        <el-input-number
          v-model="formData.daily_assign_limit"
          :min="1"
          :max="10000"
          placeholder="每日分配上限"
        />
        <div class="form-tip">
          将应用到所选分组的每日分配上限设置
        </div>
      </el-form-item>

      <!-- 删除确认 -->
      <div v-if="action === 'delete'" class="delete-warning">
        <el-alert
          title="警告"
          type="warning"
          :closable="false"
          show-icon
        >
          <p>您即将删除选中的分组，此操作不可恢复。</p>
          <p>只有没有客服的分组才能被删除。</p>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        :type="action === 'delete' ? 'danger' : 'primary'" 
        :loading="submitting" 
        @click="handleSubmit"
      >
        确认操作
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
  action: string
  selectedGroups: any[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()

// 表单相关
const formRef = ref<FormInstance>()
const submitting = ref(false)

const formData = reactive({
  daily_assign_limit: 100
})

const formRules: FormRules = {
  daily_assign_limit: [
    { required: true, message: '请输入每日分配上限', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '每日分配上限必须在 1 到 10000 之间', trigger: 'blur' }
  ]
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

function resetForm() {
  Object.assign(formData, {
    daily_assign_limit: 100
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

function getDialogTitle() {
  const actionMap = {
    'set_daily_limit': '设置每日分配上限',
    'enable_assign': '开启分配新会话',
    'disable_assign': '关闭分配新会话',
    'delete': '批量删除分组'
  }
  return actionMap[props.action] || '批量操作'
}

function handleClose() {
  emit('update:visible', false)
}

async function handleSubmit() {
  if (!formRef.value) return
  
  // 根据操作类型验证表单
  const needsValidation = ['set_daily_limit'].includes(props.action)
  
  if (needsValidation) {
    try {
      await formRef.value.validate()
    } catch (error) {
      return
    }
  }
  
  try {
    submitting.value = true
    
    const groupIds = props.selectedGroups.map(group => group.id)
    const requestData: any = {
      group_ids: groupIds,
      action: getApiAction(),
      data: getActionData()
    }
    
    const response = await fetch('/api/admin/customer-service-groups/batch', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('批量操作成功')
      emit('success')
    } else {
      ElMessage.error(result.message || '批量操作失败')
    }
  } catch (error) {
    ElMessage.error('批量操作失败')
  } finally {
    submitting.value = false
  }
}

function getApiAction() {
  const actionMap = {
    'set_daily_limit': 'set_daily_limit',
    'enable_assign': 'enable_assign_session',
    'disable_assign': 'disable_assign_session',
    'delete': 'delete'
  }
  return actionMap[props.action]
}

function getActionData() {
  switch (props.action) {
    case 'set_daily_limit':
      return { daily_assign_limit: formData.daily_assign_limit }
    default:
      return {}
  }
}
</script>

<style scoped>
.batch-info {
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 20px;
}

.batch-info p {
  margin: 0;
  color: var(--el-color-primary);
}

.delete-warning {
  margin-bottom: 20px;
}

.delete-warning p {
  margin: 8px 0;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
