<template>
  <el-dialog
    :model-value="visible"
    :title="getDialogTitle()"
    width="500px"
    @close="handleClose"
    @update:model-value="(value) => emit('update:visible', value)"
  >
    <div class="batch-info">
      <p>已选择 <strong>{{ selectedServices.length }}</strong> 个客服</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 禁用原因 -->
      <el-form-item 
        v-if="action === 'disable'" 
        label="禁用原因" 
        prop="reason"
      >
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入禁用原因"
        />
      </el-form-item>

      <!-- 每日分配上限 -->
      <el-form-item 
        v-if="action === 'set_daily_limit'" 
        label="每日分配上限" 
        prop="daily_assign_limit"
      >
        <el-input-number
          v-model="formData.daily_assign_limit"
          :min="1"
          :max="1000"
          placeholder="每日分配上限"
        />
      </el-form-item>

      <!-- 分配新会话 -->
      <el-form-item 
        v-if="action === 'set_assign_session'" 
        label="分配新会话"
      >
        <el-radio-group v-model="formData.is_assign_new_session">
          <el-radio :value="true">开启</el-radio>
          <el-radio :value="false">关闭</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 移动到分组 -->
      <el-form-item 
        v-if="action === 'move_to_group'" 
        label="目标分组" 
        prop="group_id"
      >
        <el-select 
          v-model="formData.group_id" 
          placeholder="请选择目标分组"
          clearable
        >
          <el-option label="无分组" :value="null" />
          <el-option
            v-for="group in groups"
            :key="group.id"
            :label="group.group_name"
            :value="group.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitting" @click="handleSubmit">
        确认操作
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
  action: string
  selectedServices: any[]
  groups: any[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()

// 表单相关
const formRef = ref<FormInstance>()
const submitting = ref(false)

const formData = reactive({
  reason: '',
  daily_assign_limit: 50,
  is_assign_new_session: true,
  group_id: null
})

const formRules: FormRules = {
  reason: [
    { required: true, message: '请输入禁用原因', trigger: 'blur' }
  ],
  daily_assign_limit: [
    { required: true, message: '请输入每日分配上限', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '每日分配上限必须在 1 到 1000 之间', trigger: 'blur' }
  ],
  group_id: [
    { required: true, message: '请选择目标分组', trigger: 'change' }
  ]
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

function resetForm() {
  Object.assign(formData, {
    reason: '',
    daily_assign_limit: 50,
    is_assign_new_session: true,
    group_id: null
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

function getDialogTitle() {
  const actionMap = {
    'enable': '批量启用客服',
    'disable': '批量禁用客服',
    'set_daily_limit': '设置每日分配上限',
    'enable_assign': '开启分配新会话',
    'disable_assign': '关闭分配新会话',
    'move_to_group': '移动到分组'
  }
  return actionMap[props.action] || '批量操作'
}

function handleClose() {
  emit('update:visible', false)
}

async function handleSubmit() {
  if (!formRef.value) return
  
  // 根据操作类型验证表单
  const needsValidation = ['disable', 'set_daily_limit', 'move_to_group'].includes(props.action)
  
  if (needsValidation) {
    try {
      await formRef.value.validate()
    } catch (error) {
      return
    }
  }
  
  try {
    submitting.value = true
    
    const serviceIds = props.selectedServices.map(service => service.id)
    const requestData: any = {
      service_ids: serviceIds,
      action: getApiAction(),
      data: getActionData()
    }
    
    const response = await fetch('/api/admin/customer-services/batch', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('批量操作成功')
      emit('success')
    } else {
      ElMessage.error(result.message || '批量操作失败')
    }
  } catch (error) {
    ElMessage.error('批量操作失败')
  } finally {
    submitting.value = false
  }
}

function getApiAction() {
  const actionMap = {
    'enable': 'enable',
    'disable': 'disable',
    'set_daily_limit': 'set_daily_limit',
    'enable_assign': 'set_assign_session',
    'disable_assign': 'set_assign_session',
    'move_to_group': 'move_to_group'
  }
  return actionMap[props.action]
}

function getActionData() {
  switch (props.action) {
    case 'disable':
      return { reason: formData.reason }
    case 'set_daily_limit':
      return { daily_assign_limit: formData.daily_assign_limit }
    case 'enable_assign':
      return { is_assign_new_session: true }
    case 'disable_assign':
      return { is_assign_new_session: false }
    case 'move_to_group':
      return { group_id: formData.group_id }
    default:
      return {}
  }
}
</script>

<style scoped>
.batch-info {
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 20px;
}

.batch-info p {
  margin: 0;
  color: var(--el-color-primary);
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
