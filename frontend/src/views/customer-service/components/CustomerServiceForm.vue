<template>
  <el-dialog
    :model-value="visible"
    :title="mode === 'create' ? '添加客服' : '编辑客服'"
    width="600px"
    @close="handleClose"
    @update:model-value="(value) => emit('update:visible', value)"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="客服账号" prop="service_account">
        <el-input
          v-model="formData.service_account"
          placeholder="请输入客服账号"
          :disabled="mode === 'edit'"
        />
      </el-form-item>
      
      <el-form-item label="客服昵称" prop="service_nickname">
        <el-input
          v-model="formData.service_nickname"
          placeholder="请输入客服昵称"
        />
      </el-form-item>
      
      <el-form-item label="客服类型" prop="service_type">
        <el-select v-model="formData.service_type" placeholder="请选择客服类型">
          <el-option label="普通客服" value="regular" />
          <el-option label="高级客服" value="senior" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="所属分组" prop="group_id">
        <el-select v-model="formData.group_id" placeholder="请选择所属分组" clearable>
          <el-option
            v-for="group in groups"
            :key="group.id"
            :label="group.group_name"
            :value="group.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="active">启用</el-radio>
          <el-radio value="disabled">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="每日分配上限" prop="daily_assign_limit">
        <el-input-number
          v-model="formData.daily_assign_limit"
          :min="1"
          :max="1000"
          placeholder="每日分配上限"
        />
      </el-form-item>
      
      <el-form-item label="分配新会话">
        <el-switch
          v-model="formData.is_assign_new_session"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      
      <el-form-item label="隐藏客户WS号">
        <el-switch
          v-model="formData.hide_customer_ws_number"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
      
      <el-form-item label="客户群发">
        <el-switch
          v-model="formData.is_customer_group_send"
          active-text="允许"
          inactive-text="禁止"
        />
      </el-form-item>
      
      <el-form-item v-if="formData.status === 'disabled'" label="禁用原因" prop="disabled_reason">
        <el-input
          v-model="formData.disabled_reason"
          type="textarea"
          :rows="3"
          placeholder="请输入禁用原因"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitting" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
  mode: 'create' | 'edit'
  service?: any
  groups: any[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  service: null
})

const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()

// 表单相关
const formRef = ref<FormInstance>()
const submitting = ref(false)

const formData = reactive({
  service_account: '',
  service_nickname: '',
  service_type: 'regular',
  group_id: null,
  status: 'active',
  daily_assign_limit: 50,
  is_assign_new_session: true,
  hide_customer_ws_number: false,
  is_customer_group_send: false,
  disabled_reason: ''
})

const formRules: FormRules = {
  service_account: [
    { required: true, message: '请输入客服账号', trigger: 'blur' },
    { min: 3, max: 50, message: '客服账号长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  service_nickname: [
    { required: true, message: '请输入客服昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '客服昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  service_type: [
    { required: true, message: '请选择客服类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  daily_assign_limit: [
    { required: true, message: '请输入每日分配上限', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '每日分配上限必须在 1 到 1000 之间', trigger: 'blur' }
  ],
  disabled_reason: [
    { required: true, message: '请输入禁用原因', trigger: 'blur' }
  ]
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    if (props.mode === 'edit' && props.service) {
      nextTick(() => {
        Object.assign(formData, {
          service_account: props.service.service_account || '',
          service_nickname: props.service.service_nickname || '',
          service_type: props.service.service_type || 'regular',
          group_id: props.service.group_id || null,
          status: props.service.status || 'active',
          daily_assign_limit: props.service.daily_assign_limit || 50,
          is_assign_new_session: props.service.is_assign_new_session ?? true,
          hide_customer_ws_number: props.service.hide_customer_ws_number ?? false,
          is_customer_group_send: props.service.is_customer_group_send ?? false,
          disabled_reason: props.service.disabled_reason || ''
        })
      })
    }
  }
})

function resetForm() {
  Object.assign(formData, {
    service_account: '',
    service_nickname: '',
    service_type: 'regular',
    group_id: null,
    status: 'active',
    daily_assign_limit: 50,
    is_assign_new_session: true,
    hide_customer_ws_number: false,
    is_customer_group_send: false,
    disabled_reason: ''
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

function handleClose() {
  emit('update:visible', false)
}

async function handleSubmit() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const url = props.mode === 'create' 
      ? '/api/admin/customer-services'
      : `/api/admin/customer-services/${props.service.id}`
    
    const method = props.mode === 'create' ? 'POST' : 'PUT'
    
    const response = await fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
      emit('success')
    } else {
      ElMessage.error(result.message || (props.mode === 'create' ? '创建失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
