<template>
  <el-dialog
    :model-value="visible"
    :title="`分组客服列表 - ${group?.group_name || ''}`"
    width="800px"
    @close="handleClose"
    @update:model-value="(value) => emit('update:visible', value)"
  >
    <div v-if="group" class="group-info">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="分组名称">{{ group.group_name }}</el-descriptions-item>
        <el-descriptions-item label="客服数量">{{ group.service_count }}</el-descriptions-item>
        <el-descriptions-item label="每日分配上限">{{ group.daily_assign_limit }}</el-descriptions-item>
        <el-descriptions-item label="分配新会话">
          <el-tag :type="group.is_assign_new_session ? 'success' : 'info'" size="small">
            {{ group.is_assign_new_session ? '开启' : '关闭' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="分组描述" :span="2">
          {{ group.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="services-section">
      <div class="section-header">
        <h4>分组客服列表</h4>
        <el-button size="small" :icon="Refresh" @click="loadServices">
          刷新
        </el-button>
      </div>

      <el-table 
        :data="services" 
        :loading="loading"
        stripe
        style="width: 100%"
        empty-text="该分组暂无客服"
      >
        <el-table-column prop="service_account" label="客服账号" min-width="120" />
        <el-table-column prop="service_nickname" label="客服昵称" min-width="100" />
        <el-table-column prop="service_type" label="客服类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.service_type === 'senior' ? 'warning' : 'info'" size="small">
              {{ getServiceTypeText(row.service_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <StatusTag 
              :status="row.status"
              size="small"
              :show-icon="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="daily_assign_limit" label="每日分配上限" width="120" align="center" />
        <el-table-column prop="is_assign_new_session" label="分配新会话" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_assign_new_session ? 'success' : 'info'" size="small">
              {{ row.is_assign_new_session ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="加入时间" width="150" align="center">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import StatusTag from '@/components/StatusTag.vue'
import { useAuthStore } from '@/stores/auth'
import { formatTime } from '@/utils/date'

interface Props {
  visible: boolean
  group?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  group: null
})

const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const services = ref([])

// 监听 visible 和 group 变化
watch([() => props.visible, () => props.group], ([newVisible, newGroup]) => {
  if (newVisible && newGroup) {
    loadServices()
  }
})

async function loadServices() {
  if (!props.group) return
  
  try {
    loading.value = true
    const response = await fetch(`/api/admin/customer-service-groups/${props.group.id}/services`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      services.value = result.data.services || []
    } else {
      ElMessage.error(result.message || '加载客服列表失败')
    }
  } catch (error) {
    ElMessage.error('加载客服列表失败')
  } finally {
    loading.value = false
  }
}

function getServiceTypeText(type: string) {
  const typeMap = {
    'regular': '普通客服',
    'senior': '高级客服'
  }
  return typeMap[type] || type
}

function handleClose() {
  emit('update:visible', false)
}
</script>

<style scoped>
.group-info {
  margin-bottom: 20px;
}

.services-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}
</style>
