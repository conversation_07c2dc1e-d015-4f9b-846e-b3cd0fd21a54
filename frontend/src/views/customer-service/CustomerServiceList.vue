<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header fade-in">
      <div>
        <h2 class="page-title">我的客服</h2>
        <p class="page-subtitle">客服账号管理</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          添加客服
        </el-button>
        <el-dropdown @command="handleBatchAction" style="margin-left: 8px;">
          <el-button :icon="Operation">
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="enable">批量启用客服</el-dropdown-item>
              <el-dropdown-item command="disable">批量禁用客服</el-dropdown-item>
              <el-dropdown-item command="set_daily_limit">设置每日自动分配上限</el-dropdown-item>
              <el-dropdown-item command="enable_assign">开启分配新会话</el-dropdown-item>
              <el-dropdown-item command="disable_assign">关闭分配新会话</el-dropdown-item>
              <el-dropdown-item command="move_group">移动到分组</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button :icon="Download" @click="exportServices" style="margin-left: 8px;">
          导出
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section fade-in" style="animation-delay: 0.1s">
      <el-form :model="searchForm" inline>
        <el-form-item label="客服账号">
          <el-input 
            v-model="searchForm.serviceAccount" 
            placeholder="请输入客服账号"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="客服昵称">
          <el-input 
            v-model="searchForm.serviceNickname" 
            placeholder="请输入客服昵称"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="所属分组">
          <el-select 
            v-model="searchForm.groupId" 
            placeholder="请选择所属分组"
            clearable
            style="width: 200px;"
          >
            <el-option label="全部" value="" />
            <el-option 
              v-for="group in groups" 
              :key="group.id" 
              :label="group.group_name" 
              :value="group.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否分配会话">
          <el-select 
            v-model="searchForm.isAssignNewSession" 
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="隐藏客户WS号">
          <el-select 
            v-model="searchForm.hideCustomerWSNumber" 
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否客户群发">
          <el-select 
            v-model="searchForm.isCustomerGroupSend" 
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="客服类型">
          <el-select 
            v-model="searchForm.serviceType" 
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option label="普通客服" value="regular" />
            <el-option label="高级客服" value="senior" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="全部"
            clearable
            style="width: 150px;"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="停用时间">
          <el-date-picker
            v-model="searchForm.disabledDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            查询
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section fade-in" style="animation-delay: 0.2s">
      <el-table 
        :data="filteredServices" 
        :loading="loading"
        stripe
        style="width: 100%"
        empty-text="暂无客服数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="service_type" label="客服类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.service_type === 'senior' ? 'warning' : 'info'" size="small">
              {{ getServiceTypeText(row.service_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="service_nickname" label="客服昵称" min-width="120" />
        <el-table-column prop="service_account" label="客服账号" min-width="150" />
        <el-table-column prop="status" label="客服状态" width="100" align="center">
          <template #default="{ row }">
            <StatusTag 
              :status="row.status"
              size="small"
              :show-icon="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="disabled_at" label="停用时间" width="150" align="center">
          <template #default="{ row }">
            <div v-if="row.status === 'disabled' && row.disabled_at">
              {{ formatTime(row.disabled_at) }}
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="所属分组" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.group" size="small">{{ row.group.group_name }}</el-tag>
            <span v-else class="text-gray-400">未分组</span>
          </template>
        </el-table-column>
        <el-table-column prop="daily_assign_limit" label="每日自动分配上限" width="150" align="center">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              @click="editDailyLimit(row)"
            >
              {{ row.daily_assign_limit }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="success" @click="openServicePanel(row)">
              客服台
            </el-button>
            <el-dropdown @command="(command) => handleRowAction(command, row)">
              <el-button size="small" :icon="MoreFilled">
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`toggle_status_${row.id}`">
                    {{ row.status === 'active' ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="`edit_limit_${row.id}`">
                    设置分配上限
                  </el-dropdown-item>
                  <el-dropdown-item :command="`move_group_${row.id}`">
                    移动分组
                  </el-dropdown-item>
                  <el-dropdown-item :command="`delete_${row.id}`" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredServices.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 客服表单对话框 -->
    <CustomerServiceForm
      v-model:visible="formVisible"
      :mode="formMode"
      :service="currentService"
      :groups="groups"
      @success="handleFormSuccess"
    />

    <!-- 批量操作对话框 -->
    <BatchActionDialog
      v-model:visible="batchDialogVisible"
      :action="batchAction"
      :selected-services="selectedServices"
      :groups="groups"
      @success="handleBatchSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Download, 
  Operation, 
  ArrowDown,
  MoreFilled
} from '@element-plus/icons-vue'
import StatusTag from '@/components/StatusTag.vue'
import CustomerServiceForm from './components/CustomerServiceForm.vue'
import BatchActionDialog from './components/BatchActionDialog.vue'
import { useAuthStore } from '@/stores/auth'
import { formatTime } from '@/utils/date'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const services = ref([])
const groups = ref([])
const selectedServices = ref([])

// 搜索表单
const searchForm = reactive({
  serviceAccount: '',
  serviceNickname: '',
  groupId: '',
  isAssignNewSession: '',
  hideCustomerWSNumber: '',
  isCustomerGroupSend: '',
  serviceType: '',
  status: '',
  disabledDateRange: null
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 表单相关
const formVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentService = ref(null)

// 批量操作
const batchDialogVisible = ref(false)
const batchAction = ref('')

// 计算属性
const filteredServices = computed(() => {
  let filtered = services.value

  if (searchForm.serviceAccount) {
    filtered = filtered.filter(service => 
      service.service_account?.toLowerCase().includes(searchForm.serviceAccount.toLowerCase())
    )
  }

  if (searchForm.serviceNickname) {
    filtered = filtered.filter(service => 
      service.service_nickname?.toLowerCase().includes(searchForm.serviceNickname.toLowerCase())
    )
  }

  if (searchForm.groupId) {
    filtered = filtered.filter(service => 
      service.group_id === searchForm.groupId
    )
  }

  if (searchForm.serviceType) {
    filtered = filtered.filter(service => 
      service.service_type === searchForm.serviceType
    )
  }

  if (searchForm.status) {
    filtered = filtered.filter(service => 
      service.status === searchForm.status
    )
  }

  if (searchForm.isAssignNewSession !== '') {
    filtered = filtered.filter(service => 
      service.is_assign_new_session === searchForm.isAssignNewSession
    )
  }

  if (searchForm.hideCustomerWSNumber !== '') {
    filtered = filtered.filter(service => 
      service.hide_customer_ws_number === searchForm.hideCustomerWSNumber
    )
  }

  if (searchForm.isCustomerGroupSend !== '') {
    filtered = filtered.filter(service => 
      service.is_customer_group_send === searchForm.isCustomerGroupSend
    )
  }

  return filtered
})

// 生命周期
onMounted(() => {
  loadServices()
  loadGroups()
})

// 方法
async function loadServices() {
  try {
    loading.value = true
    const response = await fetch('/api/admin/customer-services', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      services.value = result.data.services || []
    } else {
      ElMessage.error(result.message || '加载客服列表失败')
    }
  } catch (error) {
    ElMessage.error('加载客服列表失败')
  } finally {
    loading.value = false
  }
}

async function loadGroups() {
  try {
    const response = await fetch('/api/admin/customer-service-groups', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      groups.value = result.data.groups || []
    }
  } catch (error) {
    console.error('加载分组列表失败:', error)
  }
}

function getServiceTypeText(type: string) {
  const typeMap = {
    'regular': '普通客服',
    'senior': '高级客服'
  }
  return typeMap[type] || type
}

function handleAdd() {
  formMode.value = 'create'
  currentService.value = null
  formVisible.value = true
}

function handleEdit(row: any) {
  formMode.value = 'edit'
  currentService.value = row
  formVisible.value = true
}

function handleSearch() {
  currentPage.value = 1
}

function handleReset() {
  Object.assign(searchForm, {
    serviceAccount: '',
    serviceNickname: '',
    groupId: '',
    isAssignNewSession: '',
    hideCustomerWSNumber: '',
    isCustomerGroupSend: '',
    serviceType: '',
    status: '',
    disabledDateRange: null
  })
  currentPage.value = 1
}

function handleSelectionChange(selection: any[]) {
  selectedServices.value = selection
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
}

function handleCurrentChange(page: number) {
  currentPage.value = page
}

function handleBatchAction(command: string) {
  if (selectedServices.value.length === 0) {
    ElMessage.warning('请先选择要操作的客服')
    return
  }
  
  batchAction.value = command
  batchDialogVisible.value = true
}

function handleRowAction(command: string, row: any) {
  const [action, id] = command.split('_')
  
  switch (action) {
    case 'toggle':
      toggleServiceStatus(row)
      break
    case 'edit':
      editDailyLimit(row)
      break
    case 'move':
      moveToGroup(row)
      break
    case 'delete':
      deleteService(row)
      break
  }
}

async function toggleServiceStatus(service: any) {
  const newStatus = service.status === 'active' ? 'disabled' : 'active'
  const action = newStatus === 'active' ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}客服 "${service.service_nickname || service.service_account}" 吗？`,
      `确认${action}`,
      { type: 'warning' }
    )
    
    const response = await fetch(`/api/admin/customer-services/${service.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...service,
        status: newStatus
      })
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success(`${action}成功`)
      loadServices()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

async function deleteService(service: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除客服 "${service.service_nickname || service.service_account}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const response = await fetch(`/api/admin/customer-services/${service.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('删除成功')
      loadServices()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

function editDailyLimit(service: any) {
  // TODO: 实现编辑每日分配上限
  ElMessage.info('功能开发中')
}

function moveToGroup(service: any) {
  // TODO: 实现移动到分组
  ElMessage.info('功能开发中')
}

function openServicePanel(service: any) {
  // TODO: 打开客服台
  ElMessage.info('客服台功能开发中')
}

function exportServices() {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

function handleFormSuccess() {
  formVisible.value = false
  loadServices()
}

function handleBatchSuccess() {
  batchDialogVisible.value = false
  selectedServices.value = []
  loadServices()
}
</script>

<style scoped>
.page-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.page-title {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.page-subtitle {
  margin: 4px 0 0 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.search-section {
  background: var(--el-bg-color);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-section {
  background: var(--el-bg-color);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
