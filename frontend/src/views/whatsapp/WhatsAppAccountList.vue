<template>
  <div class="whatsapp-account-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>WhatsApp账号管理</h2>
      <el-button type="primary" @click="openLoginDialog" :icon="Plus">
        添加账号
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="账号名称">
          <el-input v-model="searchForm.accountName" placeholder="请输入账号名称" clearable />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input v-model="searchForm.phoneNumber" placeholder="请输入手机号码" clearable />
        </el-form-item>
        <el-form-item label="账号状态">
          <el-select v-model="searchForm.accountStatus" placeholder="请选择状态" clearable>
            <el-option label="正常" value="normal" />
            <el-option label="封号" value="banned" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属分组">
          <el-select v-model="searchForm.groupId" placeholder="请选择分组" clearable>
            <el-option label="未分组" :value="0" />
            <el-option 
              v-for="group in groups" 
              :key="group.id" 
              :label="group.name" 
              :value="group.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 账号列表 -->
    <div class="table-section">
      <el-table 
        :data="filteredAccounts" 
        v-loading="loading"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        
        <el-table-column label="头像/昵称" width="200" align="center">
          <template #default="{ row }">
            <div class="account-info">
              <el-avatar 
                :src="row.profile_pic_url || defaultAvatar" 
                :size="40"
                @error="handleAvatarError"
              />
              <div class="account-details">
                <div class="nickname">{{ row.pushname || '未设置昵称' }}</div>
                <div class="phone">{{ row.phone_number }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.remark || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="所属组" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.group" :color="row.group.color">{{ row.group.name }}</el-tag>
            <span v-else class="no-group">未分组</span>
          </template>
        </el-table-column>
        
        <el-table-column label="账号状态" width="100" align="center">
          <template #default="{ row }">
            <StatusTag 
              :status="row.account_status || 'normal'"
              size="small"
              :show-icon="true"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="登录时间" width="150" align="center">
          <template #default="{ row }">
            <div>{{ formatTime(row.login_time) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="封号时间" width="150" align="center">
          <template #default="{ row }">
            <div v-if="row.account_status === 'banned'">
              <div class="ban-time">{{ formatTime(row.ban_time) }}</div>
              <div class="ban-reason" v-if="row.ban_reason">{{ row.ban_reason }}</div>
            </div>
            <div v-else class="no-ban">-</div>
          </template>
        </el-table-column>
        
        <el-table-column label="连接状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getConnectionStatusType(row.connection_status)"
              size="small"
            >
              {{ getConnectionStatusText(row.connection_status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div style="display: flex; gap: 8px; justify-content: center;">
              <!-- 连接/断开按钮 -->
              <el-button 
                v-if="row.connection_status === 'disconnected'"
                type="success" 
                size="small" 
                @click="handleConnect(row)"
                :loading="row.connecting"
                :icon="Connection"
              >
                连接
              </el-button>
              <el-button 
                v-else
                type="warning" 
                size="small" 
                @click="handleDisconnect(row)"
                :loading="row.disconnecting"
                :icon="SwitchButton"
              >
                断开
              </el-button>
              
              <!-- 更多操作下拉菜单 -->
              <el-dropdown @command="(command) => handleMoreAction(command, row)" trigger="click">
                <el-button type="info" size="small" :icon="More">
                  更多
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 发送消息 -->
                    <el-dropdown-item 
                      v-if="row.connection_status === 'connected'"
                      command="sendMessage"
                      :icon="ChatDotRound"
                    >
                      发送消息
                    </el-dropdown-item>
                    
                    <!-- 获取信息 -->
                    <el-dropdown-item 
                      command="getInfo"
                      :icon="InfoFilled"
                      :disabled="row.gettingInfo"
                    >
                      <span v-if="row.gettingInfo">获取信息中...</span>
                      <span v-else>获取信息</span>
                    </el-dropdown-item>
                    
                    <!-- 编辑账号 -->
                    <el-dropdown-item 
                      command="edit"
                      :icon="Edit"
                    >
                      编辑账号
                    </el-dropdown-item>
                    
                    <!-- 删除账号 -->
                    <el-dropdown-item 
                      command="delete"
                      :icon="Delete"
                      divided
                    >
                      删除账号
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 扫码登录弹窗 -->
    <el-dialog 
      v-model="showLoginDialog" 
      :title="isReconnectMode ? '重新扫码登录' : '扫码登录添加账号'"
      width="800px"
      :close-on-click-modal="false"
      @close="handleLoginDialogClose"
    >
      <div class="login-dialog-content">
        <div class="login-form" v-if="!isReconnectMode">
          <el-form :model="loginForm" :rules="loginFormRules" ref="loginFormRef" label-width="100px">
            <el-form-item label="账号分组" prop="groupId">
              <el-select v-model="loginForm.groupId" placeholder="请选择分组" clearable style="width: 100%">
                <el-option label="未分组" :value="0" />
                <el-option 
                  v-for="group in groups" 
                  :key="group.id" 
                  :label="group.name" 
                  :value="group.id" 
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="账号类型" prop="accountType">
              <el-radio-group v-model="loginForm.accountType">
                <el-radio label="personal">个人版</el-radio>
                <el-radio label="business">商业版</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="备注">
              <el-input 
                v-model="loginForm.remark" 
                placeholder="请输入备注信息"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="getQRCode" 
                :loading="qrLoading"
                :disabled="qrLoading"
              >
                {{ qrLoading ? '正在生成二维码...' : '获取二维码' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 重新扫码模式：显示简单提示 -->
        <div class="reconnect-info" v-if="isReconnectMode">
          <el-alert
            title="需要重新扫码登录"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>当前账号的Session已失效，请重新扫码登录</p>
            </template>
          </el-alert>
          
          <div class="reconnect-actions" style="margin-top: 20px;">
            <el-button 
              type="primary" 
              @click="getQRCode" 
              :loading="qrLoading"
              :disabled="qrLoading"
            >
              {{ qrLoading ? '正在生成二维码...' : '获取二维码' }}
            </el-button>
          </div>
        </div>
        
        <div class="qr-section" v-if="qrLoading || qrCodeData">
          <div class="qr-container">
            <h4>请使用WhatsApp扫描二维码登录</h4>
            <div class="qr-code-wrapper" v-if="qrCodeData">
              <img :src="qrCodeData" alt="QR Code" class="qr-code" />
            </div>
            <div class="qr-loading" v-else-if="qrLoading">
              <el-icon class="loading-icon" size="48"><Loading /></el-icon>
              <p>正在生成二维码，请稍候...</p>
            </div>
            <div class="qr-status" v-if="qrCodeData">
              <el-tag :type="loginStatus === 'connected' ? 'success' : 'warning'">
                {{ getStatusText(loginStatus) }}
              </el-tag>
            </div>
            <div class="qr-tip">
              <p>• 请确保手机已安装WhatsApp</p>
              <p>• 打开WhatsApp，点击设置 → 链接设备</p>
              <p>• 扫描上方二维码完成登录</p>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="handleCancelLogin">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleLoginSuccess" 
          :disabled="loginStatus !== 'connected'"
        >
          {{ isReconnectMode ? '确认重新登录' : '确认添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 封号原因弹窗 -->
    <el-dialog v-model="showBanDialog" title="设置封号原因" width="500px">
      <el-form :model="banForm" ref="banFormRef" label-width="100px">
        <el-form-item label="封号原因" prop="banReason">
          <el-input 
            v-model="banForm.banReason" 
            type="textarea" 
            :rows="4"
            placeholder="请输入封号原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBanDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmBan">确认封号</el-button>
      </template>
    </el-dialog>

    <!-- 编辑账号弹窗 -->
    <el-dialog v-model="showEditDialog" title="编辑账号" width="600px">
      <el-form :model="editForm" ref="editFormRef" label-width="100px">
        <el-form-item label="备注">
          <el-input 
            v-model="editForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        
        <el-form-item label="所属分组">
          <el-select v-model="editForm.groupId" placeholder="请选择分组" clearable style="width: 100%">
            <el-option label="未分组" :value="0" />
            <el-option 
              v-for="group in groups" 
              :key="group.id" 
              :label="group.name" 
              :value="group.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="账号状态">
          <el-radio-group v-model="editForm.accountStatus">
            <el-radio label="normal">正常</el-radio>
            <el-radio label="banned">封号</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit">确认修改</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  Search, 
  Delete,
  Warning,
  Check,
  Connection,
  SwitchButton,
  ChatDotRound,
  InfoFilled,
  Loading,
  More,
  Edit
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { getApiUrl } from '../../../env.config.js'
import StatusTag from '@/components/StatusTag.vue'

// 状态管理
const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)
const accounts = ref([])
const groups = ref([])
const showLoginDialog = ref(false)
const showBanDialog = ref(false)
const showEditDialog = ref(false)
const qrLoading = ref(false)
const qrCodeData = ref('')
const loginStatus = ref('disconnected')
const loginPolling = ref(null)
const sessionId = ref('')
const isReconnectMode = ref(false) // 新增：区分重新扫码模式
const currentReconnectAccount = ref(null) // 新增：当前重新扫码的账户

// 默认头像
const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2ZyB4PSIxMCIgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIiBmaWxsPSJub25lIj4KPHBhdGggZD0iTTEwIDUuNUMxMC4yNzYxIDUuNSAxMC41IDUuNzIzODYgMTAuNSA2VjE0QzEwLjUgMTQuMjc2MSAxMC4yNzYxIDE0LjUgMTAgMTQuNUM5LjcyMzg2IDE0LjUgOS41IDE0LjI3NjEgOS41IDE0VjZDOS41IDUuNzIzODYgOS43MjM4NiA1LjUgMTAgNS41WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTE1IDkuNUg1QzQuNzIzODYgOS41IDQuNSA5LjcyMzg2IDQuNSAxMEM0LjUgMTAuMjc2MSA0LjcyMzg2IDEwLjUgNSAxMC41SDE1QzE1LjI3NjEgMTAuNSAxNS41IDEwLjI3NjEgMTUuNSAxMEMxNS41IDkuNzIzODYgMTUuMjc2MSA5LjUgMTUgOS41WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo='

// 搜索表单
const searchForm = reactive({
  accountName: '',
  phoneNumber: '',
  accountStatus: '',
  groupId: 0
})

// 登录表单
const loginForm = reactive({
  groupId: 0,
  accountType: 'personal',
  remark: '',
  account_name: '新账号',
  phone_number: '',
  group_id: 0
})

// 封号表单
const banForm = reactive({
  accountId: null,
  banReason: ''
})

// 编辑表单
const editForm = reactive({
  id: null,
  remark: '',
  groupId: 0,
  accountStatus: 'normal'
})

// 表单验证规则
const loginFormRules = {
  accountType: [
    { required: true, message: '请选择账号类型', trigger: 'change' }
  ]
}

// 过滤后的账号列表
const filteredAccounts = computed(() => {
  let filtered = accounts.value

  if (searchForm.accountName) {
    filtered = filtered.filter(account => 
      account.account_name?.toLowerCase().includes(searchForm.accountName.toLowerCase()) ||
      account.nickname?.toLowerCase().includes(searchForm.accountName.toLowerCase())
    )
  }

  if (searchForm.phoneNumber) {
    filtered = filtered.filter(account => 
      account.phone_number?.includes(searchForm.phoneNumber)
    )
  }

  if (searchForm.accountStatus) {
    filtered = filtered.filter(account => 
      account.account_status === searchForm.accountStatus
    )
  }

  if (searchForm.groupId !== 0) {
    filtered = filtered.filter(account => 
      account.group_id === searchForm.groupId
    )
  }
  // 当groupId为0时，显示所有账号（不进行分组过滤）

  return filtered
})

// 获取账号列表
const fetchAccounts = async () => {
  loading.value = true
  try {
    const response = await fetch(getApiUrl('/api/whatsapp/accounts'), {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      accounts.value = result.data.accounts || []
      // total.value = result.data.total || 0 // total is not defined in this component
    } else {
      ElMessage.error(result.message || '获取账号列表失败')
    }
  } catch (error) {
    ElMessage.error('获取账号列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分组列表
const fetchGroups = async () => {
  try {
    const response = await fetch(getApiUrl('/api/whatsapp/groups'), {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      groups.value = result.data.groups || []
    }
  } catch (error) {
    console.error('获取分组列表失败:', error)
  }
}

// 获取Session列表
const fetchSessions = async () => {
  try {
    const response = await fetch(getApiUrl('/api/whatsapp/sessions'), {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      // sessions.value = result.data.sessions || [] // sessions is not defined in this component
    }
  } catch (error) {
    console.error('获取Session列表失败:', error)
  }
}

// 获取二维码
const getQRCode = async () => {
  qrLoading.value = true
  try {
    const response = await fetch(getApiUrl('/api/whatsapp/sessions'), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        account_name: loginForm.account_name,
        group_id: loginForm.groupId
      })
    })
    const result = await response.json()
    
    if (result.code === 200) {
      // 使用正确的数据结构
      console.log('API响应:', result)
      console.log('二维码数据:', result.data.session.qr_code)
      qrCodeData.value = result.data.session.qr_code
      sessionId.value = result.data.session.session_id
      loginStatus.value = 'waiting'
      startLoginPolling()
      ElMessage.success('二维码生成成功，请扫描登录')
    } else {
      console.error('API错误:', result)
      ElMessage.error(result.message || '生成二维码失败')
    }
  } catch (error) {
    ElMessage.error('生成二维码失败')
  } finally {
    qrLoading.value = false
  }
}

// 开始轮询登录状态
const startLoginPolling = () => {
  loginStatus.value = 'connecting'
  let pollCount = 0
  const maxPolls = 150 // 最多轮询5分钟 (150 * 2秒)
  
  loginPolling.value = setInterval(async () => {
    pollCount++
    
    // 超时检查
    if (pollCount > maxPolls) {
      loginStatus.value = 'error'
      clearInterval(loginPolling.value)
      ElMessage.error('登录超时，请重新获取二维码')
      return
    }
    
    try {
      const response = await fetch(getApiUrl(`/api/whatsapp/sessions/${sessionId.value}/status`), {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      })
      const result = await response.json()
      
      if (result.code === 200) {
        const status = result.data.session.status
        if (status === 'active' || status === 'connected') {
          loginStatus.value = 'connected'
          clearInterval(loginPolling.value)
          // 创建Account
          await createAccount(sessionId.value)
        } else if (status === 'expired') {
          loginStatus.value = 'error'
          clearInterval(loginPolling.value)
          ElMessage.error('二维码已过期，请重新获取')
        }
        // 如果状态是 initializing 或 disconnected，继续轮询
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
      // 如果连续失败次数过多，也停止轮询
      if (pollCount > 30) {
        loginStatus.value = 'error'
        clearInterval(loginPolling.value)
        ElMessage.error('连接失败，请检查网络后重试')
      }
    }
  }, 2000)
}

// 创建Account
const createAccount = async (sessionId: string) => {
  try {
    console.log('开始创建Account，sessionId:', sessionId)
    
    if (isReconnectMode.value && currentReconnectAccount.value) {
      // 重新扫码模式：更新现有账户
      console.log('重新扫码模式：更新现有账户')
      
      const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${currentReconnectAccount.value.id}/connect-session`), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      })
      
      console.log('更新账户响应状态:', response.status)
      const result = await response.json()
      console.log('更新账户响应:', result)
      
      if (result.code === 200) {
        ElMessage.success('重新登录成功')
        showLoginDialog.value = false
        resetLoginForm()
        await fetchAccounts()
      } else {
        ElMessage.error(result.message || '重新登录失败')
        console.error('更新账户失败:', result)
      }
    } else {
      // 添加账户模式：创建新账户
      console.log('添加账户模式：创建新账户')
      
      const requestBody = {
        session_id: sessionId,
        account_name: loginForm.account_name || '新账号',
        group_id: loginForm.groupId || null
      }
      console.log('创建Account请求体:', requestBody)
      
      const response = await fetch(getApiUrl('/api/whatsapp/accounts/from-session'), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })
      
      console.log('创建Account响应状态:', response.status)
      const result = await response.json()
      console.log('创建Account响应:', result)
      
      if (result.code === 200) {
        ElMessage.success('账号创建成功')
        showLoginDialog.value = false
        resetLoginForm()
        console.log('开始刷新账号列表')
        await fetchAccounts()
        console.log('账号列表刷新完成')
      } else {
        ElMessage.error(result.message || '账号创建失败')
        console.error('创建Account失败:', result)
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '连接错误'
  }
  return statusMap[status] || status
}

// 处理取消登录
const handleCancelLogin = async () => {
  try {
    // 清理当前session
    await cleanupCurrentSession()
    // 重置表单
    resetLoginForm()
    // 关闭对话框
    showLoginDialog.value = false
    ElMessage.info('已取消登录')
  } catch (error) {
    console.error('取消登录失败:', error)
    showLoginDialog.value = false
  }
}

// 处理对话框关闭
const handleLoginDialogClose = async () => {
  // 如果对话框关闭时还有轮询在进行，需要清理
  if (loginPolling.value || qrCodeData.value) {
    await cleanupCurrentSession()
    resetLoginForm()
  }
  // 重置重新扫码模式
  isReconnectMode.value = false
  currentReconnectAccount.value = null
}

// 处理登录成功
const handleLoginSuccess = async () => {
  try {
    if (isReconnectMode.value) {
      // 重新扫码模式
      ElMessage.success('重新登录成功')
    } else {
      // 添加账户模式
      ElMessage.success('账号添加成功')
    }
    showLoginDialog.value = false
    resetLoginForm()
    fetchAccounts()
    // 重置模式
    isReconnectMode.value = false
    currentReconnectAccount.value = null
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 清理当前session
const cleanupCurrentSession = async () => {
  if (sessionId.value) {
    try {
      // 清理未扫码的session
      const response = await fetch(getApiUrl(`/api/whatsapp/session/${sessionId.value}/cleanup-unscanned`), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        console.log('Session清理结果:', result.message);
      }
    } catch (error) {
      console.error('清理Session失败:', error);
    }
  }
  
  // 重置状态
  qrCodeData.value = ''
  sessionId.value = ''
  loginStatus.value = 'disconnected'
  if (loginPolling.value) {
    clearInterval(loginPolling.value)
    loginPolling.value = null
  }
}

// 重置登录表单
const resetLoginForm = () => {
  loginForm.groupId = 0
  loginForm.accountType = 'personal'
  loginForm.remark = ''
  loginForm.account_name = '新账号'
  loginForm.phone_number = ''
  loginForm.group_id = 0
}

// 重置登录状态
const resetLoginState = () => {
  qrCodeData.value = ''
  sessionId.value = ''
  loginStatus.value = 'disconnected'
  if (loginPolling.value) {
    clearInterval(loginPolling.value)
    loginPolling.value = null
  }
}

// 打开登录对话框
const openLoginDialog = () => {
  // 重置为添加账户模式
  isReconnectMode.value = false
  currentReconnectAccount.value = null
  resetLoginState()
  resetLoginForm()
  showLoginDialog.value = true
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已通过computed实现
}

// 处理重置
const handleReset = () => {
  searchForm.accountName = ''
  searchForm.phoneNumber = ''
  searchForm.accountStatus = ''
  searchForm.groupId = 0
}

// 处理删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除账号 "${row.account_name}" 吗？这将同时删除账号和清理相关Session文件。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 先清理Session
    if (row.session_id) {
      try {
        const cleanupResponse = await fetch(getApiUrl(`/api/whatsapp/accounts/${row.id}/cleanup`), {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authStore.token}`,
            'Content-Type': 'application/json'
          }
        })
        const cleanupResult = await cleanupResponse.json()
        if (cleanupResult.code !== 200) {
          console.warn('清理Session失败:', cleanupResult.message)
        }
      } catch (error) {
        console.warn('清理Session失败:', error)
      }
    }

    // 删除账号
    const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${row.id}`), {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('删除成功')
      fetchAccounts()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理状态切换
const handleToggleStatus = (row: any) => {
  if (row.account_status === 'normal') {
    // 设为封号
    banForm.accountId = row.id
    banForm.banReason = ''
    showBanDialog.value = true
  } else {
    // 设为正常
    updateAccountStatus(row.id, 'normal')
  }
}

// 确认封号
const confirmBan = async () => {
  if (!banForm.banReason.trim()) {
    ElMessage.warning('请输入封号原因')
    return
  }

  try {
    await updateAccountStatus(banForm.accountId, 'banned', banForm.banReason)
    showBanDialog.value = false
    banForm.accountId = null
    banForm.banReason = ''
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 获取连接状态类型
const getConnectionStatusType = (status: string) => {
  const statusMap = {
    'disconnected': 'info',
    'connecting': 'warning',
    'connected': 'success',
    'error': 'danger',
    'banned': 'danger',
    'offline': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取连接状态文本
const getConnectionStatusText = (status: string) => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '连接错误',
    'banned': '已封号',
    'offline': '离线'
  }
  return statusMap[status] || status
}

// 连接WhatsApp账号
const handleConnect = async (row: any) => {
  row.connecting = true
  try {
    const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${row.id}/connect-session`), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('连接成功')
      fetchAccounts() // 刷新列表
    } else if (response.status === 404 && result.message?.includes('需要重新扫码')) {
      // Session不存在，弹出扫码界面
      ElMessage.warning('Session不存在，需要重新扫码')
      // 设置重新扫码模式
      isReconnectMode.value = true
      currentReconnectAccount.value = row
      showLoginDialog.value = true
      resetLoginState()
      resetLoginForm()
    } else {
      ElMessage.error(result.message || '连接失败')
    }
  } catch (error) {
    ElMessage.error('连接失败')
  } finally {
    row.connecting = false
  }
}

// 断开WhatsApp账号
const handleDisconnect = async (row: any) => {
  row.disconnecting = true
  try {
    const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${row.id}/disconnect-session`), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        account_id: row.id
      })
    })
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('断开成功')
      fetchAccounts() // 刷新列表
    } else {
      ElMessage.error(result.message || '断开失败')
    }
  } catch (error) {
    ElMessage.error('断开失败')
  } finally {
    row.disconnecting = false
  }
}

// 发送消息
const handleSendMessage = (row: any) => {
  router.push(`/whatsapp/accounts/${row.id}/send-message`)
}

// 获取账号信息
const handleGetInfo = async (row: any) => {
  row.gettingInfo = true
  try {
    const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${row.id}/info`), {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('获取信息成功')
      fetchAccounts() // 刷新列表以显示最新信息
    } else {
      ElMessage.error(result.message || '获取信息失败')
    }
  } catch (error) {
    ElMessage.error('获取信息失败')
  } finally {
    row.gettingInfo = false
  }
}

// 打开编辑对话框
const handleEdit = (row: any) => {
  editForm.id = row.id
  editForm.remark = row.remark || ''
  editForm.groupId = row.group_id || 0
  editForm.accountStatus = row.account_status || 'normal'
  showEditDialog.value = true
}

// 提交编辑
const handleEditSubmit = async () => {
  try {
    const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${editForm.id}`), {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        remark: editForm.remark,
        group_id: editForm.groupId,
        account_status: editForm.accountStatus
      })
    })
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('编辑成功')
      showEditDialog.value = false
      fetchAccounts() // 刷新列表
    } else {
      ElMessage.error(result.message || '编辑失败')
    }
  } catch (error) {
    ElMessage.error('编辑失败')
  }
}

// 更新账号状态
const updateAccountStatus = async (accountId: number, status: string, reason?: string) => {
  try {
    const response = await fetch(getApiUrl(`/api/whatsapp/accounts/${accountId}/status`), {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        account_status: status,
        ban_reason: reason || ''
      })
    })
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success(status === 'banned' ? '已设为封号' : '已设为正常')
      fetchAccounts()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}



// 处理更多操作
const handleMoreAction = (command: string, row: any) => {
  switch (command) {
    case 'sendMessage':
      handleSendMessage(row)
      break
    case 'getInfo':
      handleGetInfo(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

// 处理头像加载错误
const handleAvatarError = () => {
  // 头像加载失败时使用默认头像
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

// 页面加载
onMounted(() => {
  fetchAccounts()
  fetchGroups()
})

// 页面卸载
onUnmounted(() => {
  if (loginPolling.value) {
    clearInterval(loginPolling.value)
  }
})
</script>

<style scoped>
.whatsapp-account-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: var(--text-primary);
}

.search-section {
  background: var(--bg-secondary);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.table-section {
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.account-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nickname {
  font-weight: 600;
  color: var(--text-primary);
}

.phone {
  font-size: 12px;
  color: var(--text-secondary);
}

.no-group {
  color: var(--text-tertiary);
  font-size: 12px;
}

.ban-time {
  color: var(--color-danger);
  font-size: 12px;
}

.ban-reason {
  color: var(--text-tertiary);
  font-size: 11px;
  margin-top: 2px;
}

.no-ban {
  color: var(--text-tertiary);
  font-size: 12px;
}

.login-dialog-content {
  display: flex;
  gap: 30px;
}

.login-form {
  flex: 1;
}

.reconnect-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.reconnect-info .el-alert {
  width: 100%;
  margin-bottom: 20px;
}

.reconnect-actions {
  display: flex;
  gap: 10px;
}

.qr-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.qr-container {
  text-align: center;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
}

.qr-container h4 {
  margin: 0 0 20px 0;
  color: var(--text-primary);
}

.qr-code-wrapper {
  margin-bottom: 20px;
}

.qr-code {
  max-width: 200px;
  border: 4px solid #25d366;
  border-radius: 8px;
  padding: 10px;
  background: var(--bg-primary);
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.loading-icon {
  color: #25d366; /* WhatsApp green */
  margin-bottom: 10px;
}

.qr-loading p {
  color: var(--text-secondary);
  font-size: 14px;
}

.qr-status {
  margin-bottom: 15px;
}

.qr-tip {
  text-align: left;
  font-size: 12px;
  color: var(--text-secondary);
}

.qr-tip p {
  margin: 5px 0;
}
</style> 