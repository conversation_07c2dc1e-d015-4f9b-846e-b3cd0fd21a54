<template>
  <div class="whatsapp-account-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>WhatsApp账号管理</h2>
      <el-button type="primary" @click="showLoginDialog = true" :icon="Plus">
        扫码登录添加账号
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="账号名称">
          <el-input v-model="searchForm.accountName" placeholder="请输入账号名称" clearable />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input v-model="searchForm.phoneNumber" placeholder="请输入手机号码" clearable />
        </el-form-item>
        <el-form-item label="账号状态">
          <el-select v-model="searchForm.accountStatus" placeholder="请选择状态" clearable>
            <el-option label="正常" value="normal" />
            <el-option label="封号" value="banned" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属分组">
          <el-select v-model="searchForm.groupId" placeholder="请选择分组" clearable>
            <el-option label="未分组" :value="0" />
            <el-option 
              v-for="group in groups" 
              :key="group.id" 
              :label="group.name" 
              :value="group.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 账号列表 -->
    <div class="table-section">
      <el-table 
        :data="filteredAccounts" 
        v-loading="loading"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        
        <el-table-column label="头像/昵称" width="200" align="center">
          <template #default="{ row }">
            <div class="account-info">
              <el-avatar 
                :src="row.avatar || defaultAvatar" 
                :size="40"
                @error="handleAvatarError"
              />
              <div class="account-details">
                <div class="nickname">{{ row.nickname || '未设置昵称' }}</div>
                <div class="phone">{{ row.phone_number }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.remark || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="所属组" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.group" :color="row.group.color">{{ row.group.name }}</el-tag>
            <span v-else class="no-group">未分组</span>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.account_status === 'normal' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.account_status === 'normal' ? '正常' : '封号' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="登录时间" width="150" align="center">
          <template #default="{ row }">
            <div>{{ formatTime(row.login_time) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="封号时间" width="150" align="center">
          <template #default="{ row }">
            <div v-if="row.account_status === 'banned'">
              <div class="ban-time">{{ formatTime(row.ban_time) }}</div>
              <div class="ban-reason" v-if="row.ban_reason">{{ row.ban_reason }}</div>
            </div>
            <div v-else class="no-ban">-</div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div style="display: flex; gap: 8px; justify-content: center;">
              <el-button 
                type="danger" 
                size="small" 
                @click="handleDelete(row)"
                :icon="Delete"
              >
                删除
              </el-button>
              <el-button 
                :type="row.account_status === 'normal' ? 'warning' : 'success'" 
                size="small" 
                @click="handleToggleStatus(row)"
                :icon="row.account_status === 'normal' ? 'Warning' : 'Check'"
              >
                {{ row.account_status === 'normal' ? '设为封号' : '设为正常' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 扫码登录弹窗 -->
    <el-dialog 
      v-model="showLoginDialog" 
      title="扫码登录添加账号"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="login-dialog-content">
        <div class="login-form">
          <el-form :model="loginForm" :rules="loginFormRules" ref="loginFormRef" label-width="100px">
            <el-form-item label="账号分组" prop="groupId">
              <el-select v-model="loginForm.groupId" placeholder="请选择分组" clearable style="width: 100%">
                <el-option label="未分组" :value="0" />
                <el-option 
                  v-for="group in groups" 
                  :key="group.id" 
                  :label="group.name" 
                  :value="group.id" 
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="账号类型" prop="accountType">
              <el-radio-group v-model="loginForm.accountType">
                <el-radio label="personal">个人版</el-radio>
                <el-radio label="business">商业版</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="备注">
              <el-input 
                v-model="loginForm.remark" 
                placeholder="请输入备注信息"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="getQRCode" 
                :loading="qrLoading"
                :disabled="!!qrCodeData"
              >
                获取二维码
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="qr-section" v-if="qrCodeData">
          <div class="qr-container">
            <h4>请使用WhatsApp扫描二维码登录</h4>
            <div class="qr-code-wrapper">
              <img :src="qrCodeData" alt="QR Code" class="qr-code" />
            </div>
            <div class="qr-status">
              <el-tag :type="loginStatus === 'connected' ? 'success' : 'warning'">
                {{ getStatusText(loginStatus) }}
              </el-tag>
            </div>
            <div class="qr-tip">
              <p>• 请确保手机已安装WhatsApp</p>
              <p>• 打开WhatsApp，点击设置 → 链接设备</p>
              <p>• 扫描上方二维码完成登录</p>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showLoginDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleLoginSuccess" 
          :disabled="loginStatus !== 'connected'"
        >
          确认添加
        </el-button>
      </template>
    </el-dialog>

    <!-- 封号原因弹窗 -->
    <el-dialog v-model="showBanDialog" title="设置封号原因" width="500px">
      <el-form :model="banForm" ref="banFormRef" label-width="100px">
        <el-form-item label="封号原因" prop="banReason">
          <el-input 
            v-model="banForm.banReason" 
            type="textarea" 
            :rows="4"
            placeholder="请输入封号原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBanDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmBan">确认封号</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  Search, 
  Delete,
  Warning,
  Check
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// 状态管理
const authStore = useAuthStore()
const loading = ref(false)
const accounts = ref([])
const groups = ref([])
const showLoginDialog = ref(false)
const showBanDialog = ref(false)
const qrLoading = ref(false)
const qrCodeData = ref('')
const loginStatus = ref('disconnected')
const loginPolling = ref(null)

// 默认头像
const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2ZyB4PSIxMCIgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIiBmaWxsPSJub25lIj4KPHBhdGggZD0iTTEwIDUuNUMxMC4yNzYxIDUuNSAxMC41IDUuNzIzODYgMTAuNSA2VjE0QzEwLjUgMTQuMjc2MSAxMC4yNzYxIDE0LjUgMTAgMTQuNUM5LjcyMzg2IDE0LjUgOS41IDE0LjI3NjEgOS41IDE0VjZDOS41IDUuNzIzODYgOS43MjM4NiA1LjUgMTAgNS41WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTE1IDkuNUg1QzQuNzIzODYgOS41IDQuNSA5LjcyMzg2IDQuNSAxMEM0LjUgMTAuMjc2MSA0LjcyMzg2IDEwLjUgNSAxMC41SDE1QzE1LjI3NjEgMTAuNSAxNS41IDEwLjI3NjEgMTUuNSAxMEMxNS41IDkuNzIzODYgMTUuMjc2MSA5LjUgMTUgOS41WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo='

// 搜索表单
const searchForm = reactive({
  accountName: '',
  phoneNumber: '',
  accountStatus: '',
  groupId: 0
})

// 登录表单
const loginForm = reactive({
  groupId: 0,
  accountType: 'personal',
  remark: ''
})

// 封号表单
const banForm = reactive({
  accountId: null,
  banReason: ''
})

// 表单验证规则
const loginFormRules = {
  accountType: [
    { required: true, message: '请选择账号类型', trigger: 'change' }
  ]
}

// 过滤后的账号列表
const filteredAccounts = computed(() => {
  let filtered = accounts.value

  if (searchForm.accountName) {
    filtered = filtered.filter(account => 
      account.account_name?.toLowerCase().includes(searchForm.accountName.toLowerCase()) ||
      account.nickname?.toLowerCase().includes(searchForm.accountName.toLowerCase())
    )
  }

  if (searchForm.phoneNumber) {
    filtered = filtered.filter(account => 
      account.phone_number?.includes(searchForm.phoneNumber)
    )
  }

  if (searchForm.accountStatus) {
    filtered = filtered.filter(account => 
      account.account_status === searchForm.accountStatus
    )
  }

  if (searchForm.groupId !== 0) {
    filtered = filtered.filter(account => 
      account.group_id === searchForm.groupId
    )
  } else {
    // 当选择"未分组"时，显示group_id为null或0的账号
    filtered = filtered.filter(account => 
      !account.group_id || account.group_id === 0
    )
  }

  return filtered
})

// 获取账号列表
const fetchAccounts = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/whatsapp/accounts', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    if (result.code === 200) {
      accounts.value = result.data.accounts || []
    } else {
      ElMessage.error(result.message || '获取账号列表失败')
    }
  } catch (error) {
    console.error('获取账号列表失败:', error)
    ElMessage.error('获取账号列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分组列表
const fetchGroups = async () => {
  try {
    const response = await fetch('/api/whatsapp/groups', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    if (result.code === 200) {
      groups.value = result.data.groups || []
    } else {
      console.error('获取分组列表失败:', result.message)
    }
  } catch (error) {
    console.error('获取分组列表失败:', error)
  }
}

// 获取二维码
const getQRCode = async () => {
  qrLoading.value = true
  try {
    const response = await fetch('/api/whatsapp/accounts/qr-code', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    if (result.success) {
      qrCodeData.value = result.data.qr_code
      startLoginPolling(result.data.session_id)
    } else {
      ElMessage.error(result.message || '获取二维码失败')
    }
  } catch (error) {
    ElMessage.error('获取二维码失败')
  } finally {
    qrLoading.value = false
  }
}

// 开始轮询登录状态
const startLoginPolling = (sessionId: string) => {
  loginStatus.value = 'connecting'
  
  loginPolling.value = setInterval(async () => {
    try {
      // 这里应该调用检查登录状态的API
      // 暂时模拟登录成功
      setTimeout(() => {
        loginStatus.value = 'connected'
        clearInterval(loginPolling.value)
      }, 3000)
    } catch (error) {
      console.error('检查登录状态失败:', error)
    }
  }, 2000)
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '连接错误'
  }
  return statusMap[status] || status
}

// 处理登录成功
const handleLoginSuccess = async () => {
  try {
    // 这里应该调用创建账号的API
    ElMessage.success('账号添加成功')
    showLoginDialog.value = false
    resetLoginForm()
    fetchAccounts()
  } catch (error) {
    ElMessage.error('账号添加失败')
  }
}

// 重置登录表单
const resetLoginForm = () => {
  loginForm.groupId = 0
  loginForm.accountType = 'personal'
  loginForm.remark = ''
  qrCodeData.value = ''
  loginStatus.value = 'disconnected'
  if (loginPolling.value) {
    clearInterval(loginPolling.value)
    loginPolling.value = null
  }
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已通过computed实现
}

// 处理重置
const handleReset = () => {
  searchForm.accountName = ''
  searchForm.phoneNumber = ''
  searchForm.accountStatus = ''
  searchForm.groupId = 0
}

// 处理删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除账号 "${row.account_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/whatsapp/accounts/${row.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('删除成功')
      fetchAccounts()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理状态切换
const handleToggleStatus = (row: any) => {
  if (row.account_status === 'normal') {
    // 设为封号
    banForm.accountId = row.id
    banForm.banReason = ''
    showBanDialog.value = true
  } else {
    // 设为正常
    updateAccountStatus(row.id, 'normal')
  }
}

// 确认封号
const confirmBan = async () => {
  if (!banForm.banReason.trim()) {
    ElMessage.warning('请输入封号原因')
    return
  }

  try {
    await updateAccountStatus(banForm.accountId, 'banned', banForm.banReason)
    showBanDialog.value = false
    banForm.accountId = null
    banForm.banReason = ''
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 更新账号状态
const updateAccountStatus = async (accountId: number, status: string, reason?: string) => {
  try {
    const response = await fetch(`/api/whatsapp/accounts/${accountId}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        account_status: status,
        ban_reason: reason || ''
      })
    })
    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(status === 'banned' ? '已设为封号' : '已设为正常')
      fetchAccounts()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 处理头像加载错误
const handleAvatarError = () => {
  // 头像加载失败时使用默认头像
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

// 页面加载
onMounted(() => {
  fetchAccounts()
  fetchGroups()
})

// 页面卸载
onUnmounted(() => {
  if (loginPolling.value) {
    clearInterval(loginPolling.value)
  }
})
</script>

<style scoped>
.whatsapp-account-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: var(--text-primary);
}

.search-section {
  background: var(--bg-secondary);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.table-section {
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.account-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nickname {
  font-weight: 600;
  color: var(--text-primary);
}

.phone {
  font-size: 12px;
  color: var(--text-secondary);
}

.no-group {
  color: var(--text-tertiary);
  font-size: 12px;
}

.ban-time {
  color: var(--color-danger);
  font-size: 12px;
}

.ban-reason {
  color: var(--text-tertiary);
  font-size: 11px;
  margin-top: 2px;
}

.no-ban {
  color: var(--text-tertiary);
  font-size: 12px;
}

.login-dialog-content {
  display: flex;
  gap: 30px;
}

.login-form {
  flex: 1;
}

.qr-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.qr-container {
  text-align: center;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
}

.qr-container h4 {
  margin: 0 0 20px 0;
  color: var(--text-primary);
}

.qr-code-wrapper {
  margin-bottom: 20px;
}

.qr-code {
  max-width: 200px;
  border: 4px solid #25d366;
  border-radius: 8px;
  padding: 10px;
  background: var(--bg-primary);
}

.qr-status {
  margin-bottom: 15px;
}

.qr-tip {
  text-align: left;
  font-size: 12px;
  color: var(--text-secondary);
}

.qr-tip p {
  margin: 5px 0;
}
</style> 