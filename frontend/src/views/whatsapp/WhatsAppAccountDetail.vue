<template>
  <div class="whatsapp-account-detail">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><User /></el-icon>
            账号详情 - {{ accountInfo.account_name }}
          </span>
          <el-tag :type="getStatusType(accountInfo.connection_status)" size="small">
            {{ getStatusText(accountInfo.connection_status) }}
          </el-tag>
        </div>
      </template>

      <!-- 账号基本信息 -->
      <el-descriptions :column="2" border title="基本信息">
        <el-descriptions-item label="账号名称">
          {{ accountInfo.account_name }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          {{ accountInfo.phone_number || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="连接状态">
          <el-tag :type="getStatusType(accountInfo.connection_status)">
            {{ getStatusText(accountInfo.connection_status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="账号状态">
          <el-tag :type="getAccountStatusType(accountInfo.account_status)">
            {{ getAccountStatusText(accountInfo.account_status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="账号类型">
          {{ getAccountTypeText(accountInfo.account_type) }}
        </el-descriptions-item>
        <el-descriptions-item label="所属分组">
          <el-tag v-if="accountInfo.group" :color="accountInfo.group.color">
            {{ accountInfo.group.name }}
          </el-tag>
          <span v-else class="no-group">未分组</span>
        </el-descriptions-item>
        <el-descriptions-item label="昵称">
          {{ accountInfo.nickname || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ accountInfo.remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- WhatsApp客户端信息 -->
      <el-descriptions :column="2" border title="WhatsApp客户端信息" style="margin-top: 20px;">
        <el-descriptions-item label="平台">
          {{ accountInfo.platform || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="显示名称">
          {{ accountInfo.pushname || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="商业名称">
          {{ accountInfo.business_name || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ accountInfo.email || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="网站">
          {{ accountInfo.website || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="设备数量">
          {{ accountInfo.device_count || 1 }}
        </el-descriptions-item>
        <el-descriptions-item label="是否商业账号">
          <el-tag :type="accountInfo.is_business ? 'success' : 'info'" size="small">
            {{ accountInfo.is_business ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否已验证">
          <el-tag :type="accountInfo.is_verified ? 'success' : 'warning'" size="small">
            {{ accountInfo.is_verified ? '已验证' : '未验证' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 时间信息 -->
      <el-descriptions :column="2" border title="时间信息" style="margin-top: 20px;">
        <el-descriptions-item label="创建时间">
          {{ formatTime(accountInfo.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatTime(accountInfo.updated_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="登录时间">
          {{ formatTime(accountInfo.login_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后活动">
          {{ formatTime(accountInfo.last_activity) }}
        </el-descriptions-item>
        <el-descriptions-item label="封号时间" v-if="accountInfo.ban_time">
          {{ formatTime(accountInfo.ban_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="封号原因" v-if="accountInfo.ban_reason">
          {{ accountInfo.ban_reason }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- Session信息 -->
      <el-descriptions :column="2" border title="Session信息" style="margin-top: 20px;">
        <el-descriptions-item label="Session ID">
          {{ accountInfo.session_id || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="Session状态">
          <el-tag :type="accountInfo.is_session_valid ? 'success' : 'danger'" size="small">
            {{ accountInfo.is_session_valid ? '有效' : '无效' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="Session创建时间">
          {{ formatTime(accountInfo.session_created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="Session最后使用">
          {{ formatTime(accountInfo.session_last_used) }}
        </el-descriptions-item>
        <el-descriptions-item label="Session文件大小">
          {{ formatFileSize(accountInfo.session_file_size) }}
        </el-descriptions-item>
        <el-descriptions-item label="Session文件数量">
          {{ accountInfo.session_file_count || 0 }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 操作按钮 -->
      <div class="action-buttons" style="margin-top: 20px;">
        <el-button 
          type="primary" 
          @click="goToSendMessage"
          :disabled="accountInfo.connection_status !== 'connected'"
        >
          <el-icon><Message /></el-icon>
          发送消息
        </el-button>
        <el-button 
          type="success" 
          @click="handleConnect"
          v-if="accountInfo.connection_status === 'disconnected'"
        >
          <el-icon><Connection /></el-icon>
          连接账号
        </el-button>
        <el-button 
          type="warning" 
          @click="handleDisconnect"
          v-if="accountInfo.connection_status === 'connected'"
        >
          <el-icon><SwitchButton /></el-icon>
          断开连接
        </el-button>
        <el-button 
          type="info" 
          @click="handleReconnect"
          v-if="accountInfo.connection_status === 'error'"
        >
          <el-icon><Refresh /></el-icon>
          重新连接
        </el-button>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回列表
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Message, Connection, SwitchButton, Refresh, Back } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 账号信息
const accountInfo = ref({})
const accountId = route.params.id

// 获取账号信息
const getAccountInfo = async () => {
  try {
    const response = await fetch(`/api/whatsapp/accounts/${accountId}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      accountInfo.value = data.data
    } else {
      ElMessage.error('获取账号信息失败')
    }
  } catch (error) {
    console.error('获取账号信息失败:', error)
    ElMessage.error('获取账号信息失败')
  }
}

// 连接账号
const handleConnect = async () => {
  try {
    const response = await fetch(`/api/whatsapp/accounts/${accountId}/connect`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('连接请求已发送')
      await getAccountInfo()
    } else {
      ElMessage.error('连接失败')
    }
  } catch (error) {
    console.error('连接失败:', error)
    ElMessage.error('连接失败')
  }
}

// 断开连接
const handleDisconnect = async () => {
  try {
    const response = await fetch(`/api/whatsapp/accounts/${accountId}/disconnect`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('断开连接成功')
      await getAccountInfo()
    } else {
      ElMessage.error('断开连接失败')
    }
  } catch (error) {
    console.error('断开连接失败:', error)
    ElMessage.error('断开连接失败')
  }
}

// 重新连接
const handleReconnect = async () => {
  try {
    const response = await fetch(`/api/whatsapp/accounts/${accountId}/reconnect`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('重新连接请求已发送')
      await getAccountInfo()
    } else {
      ElMessage.error('重新连接失败')
    }
  } catch (error) {
    console.error('重新连接失败:', error)
    ElMessage.error('重新连接失败')
  }
}

// 跳转到发送消息页面
const goToSendMessage = () => {
  router.push(`/whatsapp/accounts/${accountId}/send-message`)
}

// 返回列表
const goBack = () => {
  router.push('/whatsapp/accounts')
}

// 工具函数
const getStatusType = (status) => {
  const statusMap = {
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'info',
    'error': 'danger',
    'banned': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'connected': '已连接',
    'connecting': '连接中',
    'disconnected': '未连接',
    'error': '连接错误',
    'banned': '已封禁'
  }
  return statusMap[status] || '未知'
}

const getAccountStatusType = (status) => {
  const statusMap = {
    'normal': 'success',
    'banned': 'danger',
    'suspended': 'warning'
  }
  return statusMap[status] || 'info'
}

const getAccountStatusText = (status) => {
  const statusMap = {
    'normal': '正常',
    'banned': '已封禁',
    'suspended': '已暂停'
  }
  return statusMap[status] || '未知'
}

const getAccountTypeText = (type) => {
  const typeMap = {
    'personal': '个人版',
    'business': '商业版'
  }
  return typeMap[type] || '未知'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  getAccountInfo()
})
</script>

<style scoped>
.whatsapp-account-detail {
  padding: 20px;
}

.detail-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.no-group {
  color: #909399;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

:deep(.el-descriptions__title) {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}
</style> 