<template>
  <div class="whatsapp-send-message">
    <el-card class="send-message-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><Message /></el-icon>
            发送消息测试 - {{ accountInfo.account_name }}
          </span>
          <el-tag :type="getStatusType(accountInfo.connection_status)" size="small">
            {{ getStatusText(accountInfo.connection_status) }}
          </el-tag>
        </div>
      </template>

      <!-- 账号信息 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="账号名称">
          {{ accountInfo.account_name }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          {{ accountInfo.phone_number || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="连接状态">
          <el-tag :type="getStatusType(accountInfo.connection_status)">
            {{ getStatusText(accountInfo.connection_status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="账号状态">
          <el-tag :type="getAccountStatusType(accountInfo.account_status)">
            {{ getAccountStatusText(accountInfo.account_status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 发送消息表单 -->
      <el-form
        ref="messageFormRef"
        :model="messageForm"
        :rules="messageRules"
        label-width="120px"
        class="message-form"
      >
        <el-form-item label="目标手机号" prop="phoneNumber">
          <el-input
            v-model="messageForm.phoneNumber"
            placeholder="请输入手机号 (例如: *************)"
            clearable
          >
            <template #prepend>+</template>
          </el-input>
          <div class="form-tip">
            请输入完整的手机号，包含国家代码 (例如: *************)
          </div>
        </el-form-item>

        <el-form-item label="消息类型" prop="messageType">
          <el-radio-group v-model="messageForm.messageType">
            <el-radio value="text">文本消息</el-radio>
            <el-radio value="image">图片消息</el-radio>
            <el-radio value="file">文件消息</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="消息内容" prop="messageContent" v-if="messageForm.messageType === 'text'">
          <el-input
            v-model="messageForm.messageContent"
            type="textarea"
            :rows="4"
            placeholder="请输入消息内容"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="图片文件" prop="imageFile" v-if="messageForm.messageType === 'image'">
          <el-upload
            ref="imageUploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept="image/*"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            :before-upload="beforeImageUpload"
          >
            <el-button type="primary" :loading="uploadingImage">
              <el-icon><Upload /></el-icon>
              {{ uploadingImage ? '上传中...' : '选择图片' }}
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 JPG、PNG、GIF、WEBP 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>

          <!-- 图片预览 -->
          <div v-if="messageForm.imageFile && imagePreviewUrl" class="image-preview">
            <img :src="imagePreviewUrl" alt="预览图片" class="preview-image" />
            <div class="image-info">
              <p><strong>文件名:</strong> {{ messageForm.imageFile.name }}</p>
              <p><strong>大小:</strong> {{ formatFileSize(messageForm.imageFile.size) }}</p>
              <p><strong>状态:</strong>
                <el-tag v-if="imageUploadStatus === 'success'" type="success" size="small">上传成功</el-tag>
                <el-tag v-else-if="imageUploadStatus === 'uploading'" type="warning" size="small">上传中</el-tag>
                <el-tag v-else type="info" size="small">待上传</el-tag>
              </p>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="图片描述" v-if="messageForm.messageType === 'image' && messageForm.imageFile">
          <el-input
            v-model="messageForm.imageCaption"
            placeholder="可选：为图片添加描述文字"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="文件" prop="file" v-if="messageForm.messageType === 'file'">
          <el-upload
            ref="fileUploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持各种文件格式，文件大小不超过 50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="sendMessage"
            :disabled="!canSendMessage"
          >
            <el-icon><ChatDotRound /></el-icon>
            发送消息
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 发送历史 -->
      <el-divider content-position="left">
        <span class="divider-title">
          <el-icon><Clock /></el-icon>
          发送历史
        </span>
      </el-divider>

      <el-table :data="sendHistory" style="width: 100%" v-loading="loadingHistory">
        <el-table-column prop="timestamp" label="发送时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="phone_number" label="目标手机号" width="150" />
        <el-table-column prop="message_type" label="消息类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getMessageTypeTag(row.message_type)" size="small">
              {{ getMessageTypeText(row.message_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="消息内容" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSendStatusType(row.status)" size="small">
              {{ getSendStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="error" label="错误信息" width="200" />
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Message, ChatDotRound, Clock, Upload } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useTenantStore } from '@/stores/tenant'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const tenantStore = useTenantStore()

// 账号信息
const accountInfo = ref({})
const accountId = route.params.id

// 表单相关
const messageFormRef = ref()
const messageForm = reactive({
  phoneNumber: '',
  messageType: 'text',
  messageContent: '',
  imageFile: null,
  imageCaption: '',
  imageFilePath: '', // 存储上传后的文件路径
  file: null
})

// 发送状态
const sending = ref(false)
const uploadingImage = ref(false)
const imageUploadStatus = ref('') // 'uploading', 'success', 'failed'
const imagePreviewUrl = ref('')

// 历史记录
const sendHistory = ref([])
const loadingHistory = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单验证规则
const messageRules = {
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^\d{10,15}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  messageContent: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ]
}

// 计算属性
const canSendMessage = computed(() => {
  if (!accountInfo.value.connection_status || accountInfo.value.connection_status !== 'connected') {
    return false
  }
  
  if (messageForm.messageType === 'text') {
    return messageForm.phoneNumber && messageForm.messageContent
  } else if (messageForm.messageType === 'image') {
    return messageForm.phoneNumber && messageForm.imageFile && messageForm.imageFilePath && imageUploadStatus.value === 'success'
  } else if (messageForm.messageType === 'file') {
    return messageForm.phoneNumber && messageForm.file
  }
  
  return false
})

// 获取账号信息
const getAccountInfo = async () => {
  try {
    const response = await fetch(`/api/whatsapp/accounts/${accountId}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      accountInfo.value = data.data
    } else {
      ElMessage.error('获取账号信息失败')
    }
  } catch (error) {
    console.error('获取账号信息失败:', error)
    ElMessage.error('获取账号信息失败')
  }
}

// 发送消息
const sendMessage = async () => {
  try {
    await messageFormRef.value.validate()
    
    sending.value = true
    
    const formData = new FormData()
    formData.append('phone_number', messageForm.phoneNumber)
    formData.append('message_type', messageForm.messageType)
    
    if (messageForm.messageType === 'text') {
      formData.append('message', messageForm.messageContent)
    } else if (messageForm.messageType === 'image') {
      formData.append('file_path', messageForm.imageFilePath)
      if (messageForm.imageCaption) {
        formData.append('message', messageForm.imageCaption)
      }
    } else if (messageForm.messageType === 'file') {
      formData.append('file', messageForm.file.raw)
    }
    
    const response = await fetch(`/api/whatsapp/accounts/${accountId}/send-message`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      },
      body: formData
    })
    
    if (response.ok) {
      const data = await response.json()
      ElMessage.success('消息发送成功')
      
      // 添加到历史记录
      let content = ''
      if (messageForm.messageType === 'text') {
        content = messageForm.messageContent
      } else if (messageForm.messageType === 'image') {
        content = messageForm.imageCaption || `图片: ${messageForm.imageFile.name}`
      } else {
        content = '文件消息'
      }

      sendHistory.value.unshift({
        timestamp: new Date().toISOString(),
        phone_number: messageForm.phoneNumber,
        message_type: messageForm.messageType,
        content: content,
        status: 'success',
        error: null
      })
      
      // 重置表单
      resetForm()
      
      // 刷新历史记录
      loadSendHistory()
    } else {
      const errorData = await response.json()
      ElMessage.error(errorData.message || '发送失败')
      
      // 添加到历史记录
      let content = ''
      if (messageForm.messageType === 'text') {
        content = messageForm.messageContent
      } else if (messageForm.messageType === 'image') {
        content = messageForm.imageCaption || `图片: ${messageForm.imageFile.name}`
      } else {
        content = '文件消息'
      }

      sendHistory.value.unshift({
        timestamp: new Date().toISOString(),
        phone_number: messageForm.phoneNumber,
        message_type: messageForm.messageType,
        content: content,
        status: 'failed',
        error: errorData.message || '发送失败'
      })
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  messageForm.phoneNumber = ''
  messageForm.messageType = 'text'
  messageForm.messageContent = ''
  messageForm.imageFile = null
  messageForm.imageCaption = ''
  messageForm.imageFilePath = ''
  messageForm.file = null
  imageUploadStatus.value = ''
  imagePreviewUrl.value = ''
  messageFormRef.value?.resetFields()
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 JPG、PNG、GIF、WEBP 格式的图片!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

// 文件上传处理
const handleImageChange = async (file) => {
  if (!beforeImageUpload(file.raw)) {
    return
  }

  messageForm.imageFile = file

  // 创建预览URL
  imagePreviewUrl.value = URL.createObjectURL(file.raw)

  // 立即上传图片到服务器
  await uploadImageToServer(file.raw)
}

const handleImageRemove = () => {
  messageForm.imageFile = null
  messageForm.imageFilePath = ''
  imageUploadStatus.value = ''
  if (imagePreviewUrl.value) {
    URL.revokeObjectURL(imagePreviewUrl.value)
    imagePreviewUrl.value = ''
  }
}

// 上传图片到服务器
const uploadImageToServer = async (file) => {
  try {
    uploadingImage.value = true
    imageUploadStatus.value = 'uploading'

    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', 'image')

    const response = await fetch('/api/files/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      },
      body: formData
    })

    const result = await response.json()

    if (result.code === 200) {
      messageForm.imageFilePath = result.data.file.file_path
      imageUploadStatus.value = 'success'
      ElMessage.success('图片上传成功')
      console.log('图片上传成功:', result.data.file.file_path)
    } else {
      imageUploadStatus.value = 'failed'
      ElMessage.error(result.message || '图片上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    imageUploadStatus.value = 'failed'
    ElMessage.error('图片上传失败')
  } finally {
    uploadingImage.value = false
  }
}

const handleFileChange = (file) => {
  messageForm.file = file
}

const handleFileRemove = () => {
  messageForm.file = null
}

// 加载发送历史
const loadSendHistory = async () => {
  try {
    loadingHistory.value = true
    
    const response = await fetch(`/api/whatsapp/accounts/${accountId}/send-history?page=${currentPage.value}&size=${pageSize.value}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      sendHistory.value = data.data.history
      total.value = data.data.total
    }
  } catch (error) {
    console.error('加载发送历史失败:', error)
  } finally {
    loadingHistory.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadSendHistory()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadSendHistory()
}

// 工具函数
const getStatusType = (status) => {
  if (!status) return 'info'
  const statusMap = {
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'info',
    'error': 'danger',
    'banned': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  if (!status) return '未知'
  const statusMap = {
    'connected': '已连接',
    'connecting': '连接中',
    'disconnected': '未连接',
    'error': '连接错误',
    'banned': '已封禁'
  }
  return statusMap[status] || '未知'
}

const getAccountStatusType = (status) => {
  if (!status) return 'info'
  const statusMap = {
    'normal': 'success',
    'banned': 'danger',
    'suspended': 'warning'
  }
  return statusMap[status] || 'info'
}

const getAccountStatusText = (status) => {
  if (!status) return '未知'
  const statusMap = {
    'normal': '正常',
    'banned': '已封禁',
    'suspended': '已暂停'
  }
  return statusMap[status] || '未知'
}

const getMessageTypeTag = (type) => {
  const typeMap = {
    'text': 'info',
    'image': 'success',
    'video': 'primary',
    'file': 'warning',
    'document': 'warning'
  }
  return typeMap[type] || 'info'
}

const getMessageTypeText = (type) => {
  const typeMap = {
    'text': '文本',
    'image': '图片',
    'video': '视频',
    'file': '文件',
    'document': '文档'
  }
  return typeMap[type] || '未知'
}

const getSendStatusType = (status) => {
  const statusMap = {
    'success': 'success',
    'failed': 'danger',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

const getSendStatusText = (status) => {
  const statusMap = {
    'success': '成功',
    'failed': '失败',
    'pending': '发送中'
  }
  return statusMap[status] || '未知'
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  getAccountInfo()
  loadSendHistory()
})
</script>

<style scoped>
.whatsapp-send-message {
  padding: 20px;
}

.send-message-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.message-form {
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.divider-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.image-preview {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 12px;
}

.image-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.image-info strong {
  color: #303133;
}
</style> 