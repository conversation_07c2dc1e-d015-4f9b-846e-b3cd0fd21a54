<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header fade-in">
      <div>
        <h2 class="page-title">WhatsApp分组管理</h2>
        <p class="page-subtitle">管理WhatsApp账号分组和排序</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新增分组
        </el-button>
        <el-button :icon="Refresh" @click="loadGroups" style="margin-left: 8px;">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section fade-in" style="animation-delay: 0.1s">
      <el-form :inline="true" class="search-form">
        <el-form-item label="分组名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="请输入分组名称" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择状态" 
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 分组列表 -->
    <div class="groups-section fade-in" style="animation-delay: 0.2s">
      <el-row :gutter="20">
        <el-col 
          v-for="group in filteredGroups" 
          :key="group.id" 
          :xs="24" 
          :sm="12" 
          :md="8" 
          :lg="6"
        >
          <div class="group-card" :style="{ borderLeftColor: group.color }">
            <div class="group-header">
              <div class="group-info">
                <h3 class="group-name">{{ group.name }}</h3>
                <p class="group-description">{{ group.description || '暂无描述' }}</p>
              </div>
              <div class="group-actions">
                <el-dropdown @command="handleGroupAction">
                  <el-button link :icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ action: 'edit', group }">
                        <el-icon><Edit /></el-icon>
                        编辑分组
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'accounts', group }">
                        <el-icon><List /></el-icon>
                        查看账号
                      </el-dropdown-item>
                      <el-dropdown-item 
                        :command="{ action: 'delete', group }"
                        :disabled="group.name === '默认分组'"
                        divided
                      >
                        <el-icon><Delete /></el-icon>
                        删除分组
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <div class="group-stats">
              <div class="stat-item">
                <span class="stat-label">账号数量</span>
                <span class="stat-value">{{ group.accounts?.length || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">排序</span>
                <span class="stat-value">{{ group.sort_order }}</span>
              </div>
            </div>

            <div class="group-footer">
              <el-tag 
                :type="group.status ? 'success' : 'info'" 
                size="small"
              >
                {{ group.status ? '启用' : '禁用' }}
              </el-tag>
              <span class="group-time">{{ formatDateTime(group.updated_at) }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogType === 'add' ? '新增分组' : '编辑分组'"
      width="500px"
    >
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="formRules" 
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入分组名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="分组描述" prop="description">
          <el-input 
            v-model="form.description" 
            placeholder="请输入分组描述"
            type="textarea"
            :rows="3"
            clearable
          />
        </el-form-item>
        <el-form-item label="分组颜色" prop="color">
          <el-color-picker 
            v-model="form.color" 
            show-alpha
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number 
            v-model="form.sortOrder" 
            :min="0" 
            :max="999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="form.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 分组账号对话框 -->
    <el-dialog 
      v-model="accountsDialogVisible" 
      :title="`${selectedGroup?.name} - 账号列表`"
      width="800px"
    >
      <div v-if="selectedGroup">
        <div class="group-summary">
          <p>分组描述：{{ selectedGroup.description || '暂无描述' }}</p>
          <p>账号数量：{{ groupAccounts.length }}</p>
        </div>
        
        <el-table 
          :data="groupAccounts" 
          stripe
          style="width: 100%"
          empty-text="该分组暂无账号"
        >
          <el-table-column prop="account_name" label="账号名称" min-width="150">
            <template #default="{ row }">
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-icon><ChatDotRound /></el-icon>
                <span>{{ row.account_name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="phone_number" label="手机号码" min-width="150" />
          <el-table-column prop="connection_status" label="连接状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.connection_status)" 
                size="small"
              >
                {{ getStatusDisplayName(row.connection_status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="last_activity" label="最后活动" width="180" align="center">
            <template #default="{ row }">
              <span v-if="row.last_activity">
                {{ formatDateTime(row.last_activity) }}
              </span>
              <span v-else style="color: var(--text-secondary);">--</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  Search, 
  Edit, 
  Delete, 
  MoreFilled,
  List,
  ChatDotRound
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// 状态管理
const authStore = useAuthStore()
const loading = ref(false)
const groups = ref([])
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const accountsDialogVisible = ref(false)
const selectedGroup = ref(null)
const groupAccounts = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  status: null
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  description: '',
  color: '#409EFF',
  sortOrder: 0,
  status: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择分组颜色', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序值在 0 到 999 之间', trigger: 'blur' }
  ]
}

// 计算属性
const filteredGroups = computed(() => {
  return groups.value.filter(group => {
    const nameMatch = !searchForm.name || 
      group.name.toLowerCase().includes(searchForm.name.toLowerCase())
    const statusMatch = searchForm.status === null || 
      group.status === searchForm.status

    return nameMatch && statusMatch
  }).sort((a, b) => a.sort_order - b.sort_order)
})

// 方法
const loadGroups = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/whatsapp/groups', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      groups.value = data.data.groups || []
    } else {
      ElMessage.error('加载分组失败')
    }
  } catch (error) {
    console.error('加载分组失败:', error)
    ElMessage.error('加载分组失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleGroupAction = async (command: any) => {
  const { action, group } = command
  
  switch (action) {
    case 'edit':
      handleEdit(group)
      break
    case 'accounts':
      await handleViewAccounts(group)
      break
    case 'delete':
      await handleDelete(group)
      break
  }
}

const handleEdit = (group: any) => {
  dialogType.value = 'edit'
  Object.assign(form, {
    id: group.id,
    name: group.name,
    description: group.description,
    color: group.color,
    sortOrder: group.sort_order,
    status: group.status
  })
  dialogVisible.value = true
}

const handleViewAccounts = async (group: any) => {
  selectedGroup.value = group
  accountsDialogVisible.value = true
  
  try {
    const response = await fetch(`/api/whatsapp/groups/${group.id}/accounts`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      groupAccounts.value = data.data.accounts || []
    } else {
      ElMessage.error('加载分组账号失败')
    }
  } catch (error) {
    console.error('加载分组账号失败:', error)
    ElMessage.error('加载分组账号失败')
  }
}

const handleDelete = async (group: any) => {
  if (group.name === '默认分组') {
    ElMessage.warning('不能删除默认分组')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除分组 "${group.name}" 吗？删除后该分组下的账号将变为未分组状态。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/whatsapp/groups/${group.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      ElMessage.success('删除成功')
      await loadGroups()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  submitLoading.value = true
  try {
    const url = dialogType.value === 'add' ? '/api/whatsapp/groups' : `/api/whatsapp/groups/${form.id}`
    const method = dialogType.value === 'add' ? 'POST' : 'PUT'
    
    const response = await fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: form.name,
        description: form.description,
        color: form.color,
        sort_order: form.sortOrder,
        status: form.status
      })
    })

    if (response.ok) {
      ElMessage.success(dialogType.value === 'add' ? '创建成功' : '更新成功')
      dialogVisible.value = false
      await loadGroups()
    } else {
      ElMessage.error(dialogType.value === 'add' ? '创建失败' : '更新失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: null
  })
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    description: '',
    color: '#409EFF',
    sortOrder: 0,
    status: true
  })
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    connected: 'success',
    connecting: 'warning',
    disconnected: 'info',
    error: 'danger',
    offline: 'info',
    banned: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusDisplayName = (status: string) => {
  const statusMap: Record<string, string> = {
    connected: '已连接',
    connecting: '连接中',
    disconnected: '已断开',
    error: '错误',
    offline: '离线',
    banned: '被封禁'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '--'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await loadGroups()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.page-subtitle {
  margin: 8px 0 0 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.search-section {
  background: var(--bg-color);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.groups-section {
  margin-bottom: 20px;
}

.group-card {
  background: var(--bg-color);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.group-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.group-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.group-description {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.group-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.group-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.group-summary {
  background: var(--bg-color);
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.group-summary p {
  margin: 5px 0;
  color: var(--text-secondary);
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 