#!/bin/bash

# 全面测试脚本 - 测试所有类型用户的所有接口行为
# 测试目标：验证多租户权限控制和数据隔离

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local expected_status="$2"
    local expected_pattern="$3"
    local curl_cmd="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}测试: ${test_name}${NC}"
    echo "命令: $curl_cmd"
    
    # 执行API调用
    local response
    local status_code
    response=$(eval "$curl_cmd" 2>/dev/null)
    status_code=$?
    
    # 检查HTTP状态码
    if [[ $status_code -eq 0 ]]; then
        # 提取HTTP状态码
        local http_status=$(echo "$response" | grep -o '"code":[0-9]*' | grep -o '[0-9]*' | head -1 || echo "000")
        
        # 检查响应内容
        if [[ "$response" =~ $expected_pattern ]]; then
            if [[ "$http_status" == "$expected_status" ]]; then
                echo -e "${GREEN}✓ 通过${NC}"
                echo "状态码: $http_status"
                echo "响应: $(echo "$response" | head -c 100)..."
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "${RED}✗ 失败 - 状态码不匹配${NC}"
                echo "期望: $expected_status, 实际: $http_status"
                echo "响应: $(echo "$response" | head -c 100)..."
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            echo -e "${RED}✗ 失败 - 响应内容不匹配${NC}"
            echo "期望模式: $expected_pattern"
            echo "实际响应: $(echo "$response" | head -c 100)..."
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${RED}✗ 失败 - 请求执行失败${NC}"
        echo "错误: $response"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo "----------------------------------------"
}

# 获取JWT token
get_token() {
    local username="$1"
    local password="$2"
    
    local response
    response=$(curl -s -X POST http://localhost:8081/api/auth/login \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"password\":\"$password\"}")
    
    echo "$response" | jq -r '.data.token' 2>/dev/null || echo ""
}

# 打印测试结果统计
print_summary() {
    echo -e "\n${YELLOW}=== 测试结果统计 ===${NC}"
    echo -e "总测试数: ${TOTAL_TESTS}"
    echo -e "${GREEN}通过: ${PASSED_TESTS}${NC}"
    echo -e "${RED}失败: ${FAILED_TESTS}${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 所有测试通过！系统功能正常。${NC}"
    else
        echo -e "\n${RED}❌ 有 ${FAILED_TESTS} 个测试失败，请检查系统。${NC}"
    fi
}

# 主测试函数
main() {
    echo -e "${YELLOW}=== 开始全面测试 ===${NC}"
    echo "测试目标：验证多租户权限控制和数据隔离"
    echo "========================================"
    
    # 1. 系统超级管理员测试
    echo -e "\n${YELLOW}=== 1. 系统超级管理员测试 ===${NC}"
    
    # 登录系统超级管理员
    echo "登录系统超级管理员..."
    SYSTEM_ADMIN_TOKEN=$(get_token "admin" "admin123")
    if [[ -z "$SYSTEM_ADMIN_TOKEN" ]]; then
        echo -e "${RED}系统超级管理员登录失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}系统超级管理员登录成功${NC}"
    
    # 测试系统超级管理员查看所有用户
    test_api "系统超级管理员查看所有用户" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $SYSTEM_ADMIN_TOKEN\" http://localhost:8081/api/users"
    
    # 测试系统超级管理员查看所有角色
    test_api "系统超级管理员查看所有角色" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $SYSTEM_ADMIN_TOKEN\" http://localhost:8081/api/roles"
    
    # 测试系统超级管理员查看所有租户
    test_api "系统超级管理员查看所有租户" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $SYSTEM_ADMIN_TOKEN\" http://localhost:8081/api/tenant/accessible"
    
    # 测试系统超级管理员切换到租户1
    test_api "系统超级管理员切换到租户1" "200" '"code":200' \
        "curl -s -X POST -H \"Authorization: Bearer $SYSTEM_ADMIN_TOKEN\" -H \"Content-Type: application/json\" -d '{\"tenant_id\": 1}' http://localhost:8081/api/tenant/switch"
    
    # 获取切换后的token
    SWITCH_RESPONSE=$(curl -s -X POST -H "Authorization: Bearer $SYSTEM_ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"tenant_id": 1}' http://localhost:8081/api/tenant/switch)
    SWITCHED_TOKEN=$(echo "$SWITCH_RESPONSE" | jq -r '.data.new_token' 2>/dev/null || echo "")
    
    if [[ -n "$SWITCHED_TOKEN" ]]; then
        # 测试切换到租户后查看用户（应该看到所有用户）
        test_api "系统超级管理员切换到租户1后查看用户" "200" '"code":200' \
            "curl -s -H \"Authorization: Bearer $SWITCHED_TOKEN\" http://localhost:8081/api/users"
        
        # 测试切换到租户后查看角色（应该看到所有角色）
        test_api "系统超级管理员切换到租户1后查看角色" "200" '"code":200' \
            "curl -s -H \"Authorization: Bearer $SWITCHED_TOKEN\" http://localhost:8081/api/roles"
    fi
    
    # 测试系统超级管理员回到系统管理
    test_api "系统超级管理员回到系统管理" "200" '"code":200' \
        "curl -s -X POST -H \"Authorization: Bearer $SYSTEM_ADMIN_TOKEN\" -H \"Content-Type: application/json\" -d '{\"tenant_id\": 0}' http://localhost:8081/api/tenant/switch"
    
    # 2. 租户级管理员测试
    echo -e "\n${YELLOW}=== 2. 租户级管理员测试 ===${NC}"
    
    # 登录租户1管理员
    echo "登录租户1管理员..."
    TENANT1_ADMIN_TOKEN=$(get_token "admin_Hive SaaS" "admin123")
    if [[ -z "$TENANT1_ADMIN_TOKEN" ]]; then
        echo -e "${RED}租户1管理员登录失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}租户1管理员登录成功${NC}"
    
    # 测试租户1管理员查看用户（应该只能看到租户1的用户）
    test_api "租户1管理员查看用户" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT1_ADMIN_TOKEN\" http://localhost:8081/api/users"
    
    # 测试租户1管理员查看角色（应该只能看到租户1的角色）
    test_api "租户1管理员查看角色" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT1_ADMIN_TOKEN\" http://localhost:8081/api/roles"
    
    # 测试租户1管理员尝试切换到其他租户（应该失败）
    test_api "租户1管理员尝试切换到租户2" "403" '"code":403' \
        "curl -s -X POST -H \"Authorization: Bearer $TENANT1_ADMIN_TOKEN\" -H \"Content-Type: application/json\" -d '{\"tenant_id\": 2}' http://localhost:8081/api/tenant/switch"
    
    # 测试租户1管理员尝试回到系统管理（应该失败）
    test_api "租户1管理员尝试回到系统管理" "403" '"code":403' \
        "curl -s -X POST -H \"Authorization: Bearer $TENANT1_ADMIN_TOKEN\" -H \"Content-Type: application/json\" -d '{\"tenant_id\": 0}' http://localhost:8081/api/tenant/switch"
    
    # 3. 租户级客服测试
    echo -e "\n${YELLOW}=== 3. 租户级客服测试 ===${NC}"
    
    # 登录租户1客服
    echo "登录租户1客服..."
    TENANT1_CS_TOKEN=$(get_token "customer_service_Hive SaaS" "admin123")
    if [[ -z "$TENANT1_CS_TOKEN" ]]; then
        echo -e "${RED}租户1客服登录失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}租户1客服登录成功${NC}"
    
    # 测试租户1客服查看用户（应该只能看到租户1的用户）
    test_api "租户1客服查看用户" "403" '"code":403' \
        "curl -s -H \"Authorization: Bearer $TENANT1_CS_TOKEN\" http://localhost:8081/api/users"
    
    # 测试租户1客服查看角色（应该只能看到租户1的角色）
    test_api "租户1客服查看角色" "403" '"code":403' \
        "curl -s -H \"Authorization: Bearer $TENANT1_CS_TOKEN\" http://localhost:8081/api/roles"
    
    # 测试租户1客服访问租户信息（应该有权限）
    test_api "租户1客服查看当前租户" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT1_CS_TOKEN\" http://localhost:8081/api/tenant/current"
    
    # 4. 其他租户管理员测试
    echo -e "\n${YELLOW}=== 4. 其他租户管理员测试 ===${NC}"
    
    # 登录租户2管理员
    echo "登录租户2管理员..."
    TENANT2_ADMIN_TOKEN=$(get_token "admin_测试公司A" "admin123")
    if [[ -z "$TENANT2_ADMIN_TOKEN" ]]; then
        echo -e "${RED}租户2管理员登录失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}租户2管理员登录成功${NC}"
    
    # 测试租户2管理员查看用户（应该只能看到租户2的用户）
    test_api "租户2管理员查看用户" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT2_ADMIN_TOKEN\" http://localhost:8081/api/users"
    
    # 测试租户2管理员查看角色（应该只能看到租户2的角色）
    test_api "租户2管理员查看角色" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT2_ADMIN_TOKEN\" http://localhost:8081/api/roles"
    
    # 5. 权限隔离验证
    echo -e "\n${YELLOW}=== 5. 权限隔离验证 ===${NC}"
    
    # 验证租户1管理员无法看到租户2的用户
    test_api "租户1管理员无法看到租户2用户" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT1_ADMIN_TOKEN\" http://localhost:8081/api/users"
    
    # 验证租户1管理员无法看到租户2的角色
    test_api "租户1管理员无法看到租户2角色" "200" '"code":200' \
        "curl -s -H \"Authorization: Bearer $TENANT1_ADMIN_TOKEN\" http://localhost:8081/api/roles"
    
    # 6. 错误情况测试
    echo -e "\n${YELLOW}=== 6. 错误情况测试 ===${NC}"
    
    # 测试无效token
    test_api "无效token访问API" "401" '"code":401' \
        "curl -s -H \"Authorization: Bearer invalid_token\" http://localhost:8081/api/users"
    
    # 测试无权限访问
    test_api "无权限用户访问管理员接口" "403" '"code":403' \
        "curl -s -H \"Authorization: Bearer $TENANT1_CS_TOKEN\" http://localhost:8081/api/tenant/accessible"
    
    # 7. 数据验证
    echo -e "\n${YELLOW}=== 7. 数据验证 ===${NC}"
    
    # 验证系统超级管理员看到所有用户
    SYSTEM_USERS=$(curl -s -H "Authorization: Bearer $SYSTEM_ADMIN_TOKEN" http://localhost:8081/api/users | jq -r '.data.users | length' 2>/dev/null || echo "0")
    if [[ "$SYSTEM_USERS" -ge 10 ]]; then
        echo -e "${GREEN}✓ 系统超级管理员可以看到所有用户 (${SYSTEM_USERS}个)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 系统超级管理员用户数量异常 (${SYSTEM_USERS}个)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 验证租户1管理员只看到租户1用户
    TENANT1_USERS=$(curl -s -H "Authorization: Bearer $TENANT1_ADMIN_TOKEN" http://localhost:8081/api/users | jq -r '.data.users | length' 2>/dev/null || echo "0")
    if [[ "$TENANT1_USERS" -eq 2 ]]; then
        echo -e "${GREEN}✓ 租户1管理员只能看到租户1用户 (${TENANT1_USERS}个)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 租户1管理员用户数量异常 (${TENANT1_USERS}个)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 验证系统超级管理员看到所有角色
    SYSTEM_ROLES=$(curl -s -H "Authorization: Bearer $SYSTEM_ADMIN_TOKEN" http://localhost:8081/api/roles | jq -r '.data.roles | length' 2>/dev/null || echo "0")
    if [[ "$SYSTEM_ROLES" -ge 10 ]]; then
        echo -e "${GREEN}✓ 系统超级管理员可以看到所有角色 (${SYSTEM_ROLES}个)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 系统超级管理员角色数量异常 (${SYSTEM_ROLES}个)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 验证租户1管理员只看到租户1角色
    TENANT1_ROLES=$(curl -s -H "Authorization: Bearer $TENANT1_ADMIN_TOKEN" http://localhost:8081/api/roles | jq -r '.data.roles | length' 2>/dev/null || echo "0")
    if [[ "$TENANT1_ROLES" -eq 2 ]]; then
        echo -e "${GREEN}✓ 租户1管理员只能看到租户1角色 (${TENANT1_ROLES}个)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 租户1管理员角色数量异常 (${TENANT1_ROLES}个)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 打印测试结果统计
    print_summary
}

# 检查依赖
check_dependencies() {
    echo "检查依赖..."
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}错误: curl 未安装${NC}"
        exit 1
    fi
    
    # 检查jq
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}错误: jq 未安装${NC}"
        exit 1
    fi
    
    # 检查后端服务
    if ! curl -s http://localhost:8081/api/health &> /dev/null; then
        echo -e "${RED}错误: 后端服务未运行，请先启动服务${NC}"
        echo "启动命令: cd backend && go run ."
        exit 1
    fi
    
    echo -e "${GREEN}依赖检查通过${NC}"
}

# 主程序
echo -e "${YELLOW}=== Hive SaaS 全面测试脚本 ===${NC}"
echo "测试时间: $(date)"
echo "========================================"

# 检查依赖
check_dependencies

# 运行测试
main

echo -e "\n${YELLOW}测试完成！${NC}" 