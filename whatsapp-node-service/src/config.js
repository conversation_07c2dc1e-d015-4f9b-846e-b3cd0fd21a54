// 服务配置
export const config = {
  // 服务端口
  PORT: process.env.PORT || 3000,
  
  // 后端API配置
  BACKEND_API_URL: process.env.BACKEND_API_URL || 'http://localhost:8081',
  
  // Session配置
  SESSION_DATA_DIR: process.env.SESSION_DATA_DIR || './sessions',
  
  // 并发限制
  MAX_CONCURRENT_SESSIONS: parseInt(process.env.MAX_CONCURRENT_SESSIONS) || 10,
  
  // 清理配置
  CLEANUP_INTERVAL: parseInt(process.env.CLEANUP_INTERVAL) || 5 * 60 * 1000, // 5分钟（降低频率）
  UNSCANNED_SESSION_MAX_AGE: parseInt(process.env.UNSCANNED_SESSION_MAX_AGE) || 10 * 60 * 1000, // 10分钟（未扫码session）
  
  // Chrome配置
  CHROME_EXECUTABLE_PATH: process.env.CHROME_EXECUTABLE_PATH || '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
  
  // 开发环境配置
  NODE_ENV: process.env.NODE_ENV || 'development',
}

// 获取后端API URL的辅助函数
export const getBackendApiUrl = (path) => {
  const baseUrl = config.BACKEND_API_URL
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

// 获取Chrome路径的辅助函数
export const getChromePath = () => {
  return config.CHROME_EXECUTABLE_PATH
} 