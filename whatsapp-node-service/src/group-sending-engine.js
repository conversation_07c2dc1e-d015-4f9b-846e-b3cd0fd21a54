import pkg from 'whatsapp-web.js';
const { Client, LocalAuth, MessageMedia } = pkg;
import qrcode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class GroupSendingEngine {
    constructor() {
        this.clients = new Map(); // 存储WhatsApp客户端
        this.tasks = new Map(); // 存储运行中的任务
        this.safeTestMessages = [
            "您好，这是一条测试消息，请忽略。",
            "测试消息：系统正在验证功能。",
            "这是一条自动发送的测试消息。",
            "功能测试中，请勿回复。",
            "系统消息：功能验证完成。"
        ];
    }

    // 初始化WhatsApp客户端
    async initializeClient(accountId, sessionId) {
        try {
            const client = new Client({
                authStrategy: new LocalAuth({
                    clientId: `account-${accountId}`,
                    dataPath: path.join(__dirname, '../data/sessions')
                }),
                puppeteer: {
                    headless: true,
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu'
                    ]
                }
            });

            client.on('qr', (qr) => {
                console.log(`[Account ${accountId}] QR Code generated`);
                // 这里可以保存QR码供前端显示
            });

            client.on('ready', () => {
                console.log(`[Account ${accountId}] Client is ready!`);
            });

            client.on('authenticated', () => {
                console.log(`[Account ${accountId}] Client is authenticated!`);
            });

            client.on('auth_failure', (msg) => {
                console.error(`[Account ${accountId}] Auth failure:`, msg);
            });

            client.on('disconnected', (reason) => {
                console.log(`[Account ${accountId}] Client was disconnected:`, reason);
            });

            await client.initialize();
            this.clients.set(accountId, client);
            return client;
        } catch (error) {
            console.error(`[Account ${accountId}] Failed to initialize client:`, error);
            throw error;
        }
    }

    // 获取安全的测试消息
    getSafeTestMessage(index = 0) {
        return this.safeTestMessages[index % this.safeTestMessages.length];
    }

    // 验证手机号格式
    validatePhoneNumber(phoneNumber) {
        // 移除所有非数字字符
        const cleanNumber = phoneNumber.replace(/\D/g, '');
        
        // 检查是否为有效的手机号格式
        if (cleanNumber.length < 10 || cleanNumber.length > 15) {
            return false;
        }
        
        return cleanNumber;
    }

    // 发送单条消息
    async sendMessage(accountId, phoneNumber, statement, taskId) {
        try {
            const client = this.clients.get(accountId);
            if (!client) {
                throw new Error('WhatsApp client not initialized');
            }

            // 验证手机号
            const validatedNumber = this.validatePhoneNumber(phoneNumber);
            if (!validatedNumber) {
                throw new Error('Invalid phone number format');
            }

            // 格式化手机号（添加国家代码）
            const formattedNumber = validatedNumber.includes('@c.us')
                ? validatedNumber
                : `${validatedNumber}@c.us`;

            // 检查联系人是否存在
            const contact = await client.getContactById(formattedNumber);
            if (!contact) {
                throw new Error('Contact not found');
            }

            let result;
            let messageContent = '';

            // 根据语句类型发送不同类型的消息
            switch (statement.type) {
                case 'text':
                    messageContent = statement.content;
                    result = await client.sendMessage(formattedNumber, statement.content);
                    break;

                case 'image':
                    if (!statement.file_url) {
                        throw new Error('Image file path not provided');
                    }

                    // 处理文件路径
                    let imageFilePath = statement.file_url;
                    if (!statement.file_url.startsWith('/') && !statement.file_url.startsWith('C:') && !statement.file_url.startsWith('../')) {
                        imageFilePath = `../${statement.file_url}`;
                    }

                    if (!fs.existsSync(imageFilePath)) {
                        throw new Error(`Image file not found: ${imageFilePath}`);
                    }

                    messageContent = `图片: ${statement.material_name || statement.content}`;
                    const imageMedia = MessageMedia.fromFilePath(imageFilePath);
                    if (statement.content && statement.content !== statement.material_name) {
                        imageMedia.caption = statement.content;
                    }
                    result = await client.sendMessage(formattedNumber, imageMedia);
                    break;

                case 'video':
                    if (!statement.file_url) {
                        throw new Error('Video file path not provided');
                    }

                    // 处理文件路径
                    let videoFilePath = statement.file_url;
                    if (!statement.file_url.startsWith('/') && !statement.file_url.startsWith('C:') && !statement.file_url.startsWith('../')) {
                        videoFilePath = `../${statement.file_url}`;
                    }

                    if (!fs.existsSync(videoFilePath)) {
                        throw new Error(`Video file not found: ${videoFilePath}`);
                    }

                    messageContent = `视频: ${statement.material_name || statement.content}`;
                    const videoMedia = MessageMedia.fromFilePath(videoFilePath);
                    if (statement.content && statement.content !== statement.material_name) {
                        videoMedia.caption = statement.content;
                    }
                    result = await client.sendMessage(formattedNumber, videoMedia);
                    break;

                case 'call':
                    messageContent = '📞 拨打电话动作';
                    result = await client.sendMessage(formattedNumber, '📞 拨打电话动作');
                    break;

                default:
                    messageContent = this.getSafeTestMessage();
                    result = await client.sendMessage(formattedNumber, messageContent);
            }

            // 记录发送日志
            await this.logMessage(taskId, accountId, phoneNumber, messageContent, 'success', null);

            console.log(`[Task ${taskId}] Message sent to ${phoneNumber}: ${messageContent.substring(0, 50)}...`);

            return {
                success: true,
                messageId: result.id._serialized,
                timestamp: new Date()
            };
        } catch (error) {
            console.error(`[Task ${taskId}] Failed to send message to ${phoneNumber}:`, error.message);

            // 记录错误日志
            await this.logMessage(taskId, accountId, phoneNumber, statement.content || 'Unknown message', 'failed', error.message);

            return {
                success: false,
                error: error.message,
                timestamp: new Date()
            };
        }
    }

    // 执行群发任务
    async executeTask(taskData) {
        const taskId = taskData.id;
        const accountId = taskData.selected_account_id || taskData.account_id;
        
        try {
            console.log(`[Task ${taskId}] Starting group sending task`);
            
            // 初始化客户端
            const client = await this.initializeClient(accountId, `task-${taskId}`);
            
            // 解析客户文件
            const phoneNumbers = await this.parseCustomerFile(taskData.customer_file);
            
            // 解析群发语句
            const statements = await this.parseStatements(taskData.statements);
            
            // 设置任务状态为运行中
            await this.updateTaskStatus(taskId, 'running');
            
            let sentCount = 0;
            let failedCount = 0;
            
            // 逐个发送消息
            for (let i = 0; i < phoneNumbers.length; i++) {
                const phoneNumber = phoneNumbers[i];
                
                // 检查任务是否被终止
                if (this.tasks.get(taskId)?.status === 'terminated') {
                    console.log(`[Task ${taskId}] Task terminated by user`);
                    break;
                }
                
                // 检查任务是否被暂停
                if (this.tasks.get(taskId)?.status === 'paused') {
                    console.log(`[Task ${taskId}] Task paused by user`);
                    await this.waitForResume(taskId);
                }
                
                // 发送所有语句
                for (let j = 0; j < statements.length; j++) {
                    const statement = statements[j];
                    const result = await this.sendMessage(accountId, phoneNumber, statement, taskId);

                    if (!result.success) {
                        failedCount++;
                        break; // 如果某条语句发送失败，跳过该客户的后续语句
                    }

                    // 语句间隔
                    if (j < statements.length - 1) {
                        await this.sleep(taskData.sentence_interval * 1000);
                    }
                }

                sentCount++;

                
                // 更新进度
                await this.updateTaskProgress(taskId, sentCount, failedCount, phoneNumbers.length);
                
                // 客户间隔
                if (i < phoneNumbers.length - 1) {
                    await this.sleep(taskData.customer_interval * 1000);
                }
            }
            
            // 任务完成
            await this.updateTaskStatus(taskId, 'completed');
            console.log(`[Task ${taskId}] Task completed. Sent: ${sentCount}, Failed: ${failedCount}`);
            
        } catch (error) {
            console.error(`[Task ${taskId}] Task execution failed:`, error);
            await this.updateTaskStatus(taskId, 'failed');
        }
    }

    // 解析客户文件
    async parseCustomerFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n').filter(line => line.trim());
            
            // 验证手机号格式
            const validNumbers = lines.map(line => {
                const cleanNumber = line.trim().replace(/\D/g, '');
                return cleanNumber.length >= 10 ? cleanNumber : null;
            }).filter(number => number !== null);
            
            console.log(`Parsed ${validNumbers.length} valid phone numbers from file`);
            return validNumbers;
        } catch (error) {
            console.error('Failed to parse customer file:', error);
            throw error;
        }
    }

    // 解析群发语句
    async parseStatements(statementsJson) {
        try {
            const statements = JSON.parse(statementsJson);

            // 直接返回语句对象数组，保留完整信息
            if (statements && statements.length > 0) {
                return statements;
            } else {
                // 如果没有语句，返回默认的安全测试消息
                return [{
                    type: 'text',
                    content: this.getSafeTestMessage(),
                    material_name: '默认测试消息',
                    order: 1
                }];
            }
        } catch (error) {
            console.error('Failed to parse statements:', error);
            return [{
                type: 'text',
                content: this.getSafeTestMessage(),
                material_name: '默认测试消息',
                order: 1
            }];
        }
    }

    // 记录发送日志
    async logMessage(taskId, accountId, phoneNumber, message, status, errorMsg) {
        try {
            const logData = {
                task_id: taskId,
                account_id: accountId,
                phone_number: phoneNumber,
                message: message.substring(0, 500), // 限制长度
                status: status,
                error_msg: errorMsg,
                sent_at: new Date()
            };
            
            // 这里应该调用后端API保存日志
            console.log(`[Log] ${status}: ${phoneNumber} - ${message.substring(0, 50)}...`);
            
        } catch (error) {
            console.error('Failed to log message:', error);
        }
    }

    // 更新任务状态
    async updateTaskStatus(taskId, status) {
        try {
            // 这里应该调用后端API更新任务状态
            console.log(`[Task ${taskId}] Status updated to: ${status}`);
            
            // 更新本地任务状态
            const task = this.tasks.get(taskId);
            if (task) {
                task.status = status;
            }
        } catch (error) {
            console.error('Failed to update task status:', error);
        }
    }

    // 更新任务进度
    async updateTaskProgress(taskId, sentCount, failedCount, totalCount) {
        try {
            const progress = Math.round((sentCount + failedCount) / totalCount * 100);
            
            // 这里应该调用后端API更新任务进度
            console.log(`[Task ${taskId}] Progress: ${progress}% (${sentCount}/${totalCount})`);
        } catch (error) {
            console.error('Failed to update task progress:', error);
        }
    }

    // 等待任务恢复
    async waitForResume(taskId) {
        return new Promise((resolve) => {
            const checkResume = () => {
                const task = this.tasks.get(taskId);
                if (task && task.status === 'running') {
                    resolve();
                } else {
                    setTimeout(checkResume, 1000);
                }
            };
            checkResume();
        });
    }

    // 工具函数：延时
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 启动任务
    startTask(taskData) {
        const taskId = taskData.id;
        this.tasks.set(taskId, { ...taskData, status: 'pending' });
        
        // 异步执行任务
        this.executeTask(taskData).catch(error => {
            console.error(`[Task ${taskId}] Task execution failed:`, error);
        });
        
        return { success: true, message: 'Task started successfully' };
    }

    // 暂停任务
    pauseTask(taskId) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.status = 'paused';
            console.log(`[Task ${taskId}] Task paused`);
            return { success: true, message: 'Task paused successfully' };
        }
        return { success: false, message: 'Task not found' };
    }

    // 恢复任务
    resumeTask(taskId) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.status = 'running';
            console.log(`[Task ${taskId}] Task resumed`);
            return { success: true, message: 'Task resumed successfully' };
        }
        return { success: false, message: 'Task not found' };
    }

    // 终止任务
    terminateTask(taskId) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.status = 'terminated';
            console.log(`[Task ${taskId}] Task terminated`);
            return { success: true, message: 'Task terminated successfully' };
        }
        return { success: false, message: 'Task not found' };
    }

    // 获取任务状态
    getTaskStatus(taskId) {
        const task = this.tasks.get(taskId);
        return task ? task.status : 'not_found';
    }

    // 清理资源
    async cleanup() {
        for (const [accountId, client] of this.clients) {
            try {
                await client.destroy();
                console.log(`[Account ${accountId}] Client destroyed`);
            } catch (error) {
                console.error(`[Account ${accountId}] Failed to destroy client:`, error);
            }
        }
        this.clients.clear();
        this.tasks.clear();
    }
}

export default GroupSendingEngine; 