import { parentPort, workerData } from 'worker_threads';
import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import path from 'path';
import fs from 'fs-extra';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WhatsAppWorker {
  constructor(sessionId, phoneNumber, tenantId, sessionDataDir) {
    this.sessionId = sessionId;
    this.phoneNumber = phoneNumber;
    this.tenantId = tenantId;
    this.sessionDataDir = sessionDataDir;
    this.client = null;
    this.isReady = false;
  }

  // 初始化客户端
  async initialize() {
    try {
      console.log(`Worker ${this.sessionId}: 初始化客户端`);
      
      // 确保会话目录存在
      await fs.ensureDir(this.sessionDataDir);
      
      // 创建客户端
      this.client = new Client({
        authStrategy: new LocalAuth({
          clientId: this.sessionId,
          dataPath: this.sessionDataDir
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
          ]
        }
      });

      // 设置事件监听器
      this.setupEventListeners();
      
      // 初始化客户端
      await this.client.initialize();
      
      console.log(`Worker ${this.sessionId}: 客户端初始化完成`);
      
      return {
        success: true,
        message: '客户端初始化成功'
      };

    } catch (error) {
      console.error(`Worker ${this.sessionId}: 初始化失败`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    this.client.on('ready', () => {
      console.log(`Worker ${this.sessionId}: 客户端已就绪`);
      this.isReady = true;
      this.sendToParent({
        type: 'status',
        data: { status: 'ready', sessionId: this.sessionId }
      });
    });

    this.client.on('authenticated', () => {
      console.log(`Worker ${this.sessionId}: 客户端已认证`);
      this.sendToParent({
        type: 'status',
        data: { status: 'authenticated', sessionId: this.sessionId }
      });
    });

    this.client.on('auth_failure', (msg) => {
      console.log(`Worker ${this.sessionId}: 认证失败`, msg);
      this.sendToParent({
        type: 'error',
        data: { error: 'auth_failure', message: msg, sessionId: this.sessionId }
      });
    });

    this.client.on('disconnected', (reason) => {
      console.log(`Worker ${this.sessionId}: 客户端断开连接`, reason);
      this.isReady = false;
      this.sendToParent({
        type: 'status',
        data: { status: 'disconnected', reason, sessionId: this.sessionId }
      });
    });

    this.client.on('message', (msg) => {
      console.log(`Worker ${this.sessionId}: 收到消息`, msg.body);
      this.sendToParent({
        type: 'message',
        data: { message: msg, sessionId: this.sessionId }
      });
    });
  }

  // 连接到会话
  async connect() {
    try {
      if (!this.client) {
        await this.initialize();
      }

      // 等待客户端就绪
      if (!this.isReady) {
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('客户端初始化超时'));
          }, 30000);

          this.client.once('ready', () => {
            clearTimeout(timeout);
            resolve();
          });
        });
      }

      // 获取客户端信息
      const clientInfo = await this.getClientInfo();

      return {
        success: true,
        message: '连接成功',
        data: {
          session_id: this.sessionId,
          status: 'connected',
          client_info: clientInfo
        }
      };

    } catch (error) {
      console.error(`Worker ${this.sessionId}: 连接失败`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 断开连接
  async disconnect() {
    try {
      if (this.client) {
        await this.client.destroy();
        this.client = null;
        this.isReady = false;
      }

      return {
        success: true,
        message: '断开成功'
      };

    } catch (error) {
      console.error(`Worker ${this.sessionId}: 断开失败`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取客户端信息
  async getClientInfo() {
    if (!this.client || !this.client.info) {
      return null;
    }

    return {
      wid: this.client.info.wid._serialized,
      pushname: this.client.info.pushname,
      platform: this.client.info.platform,
      business_name: this.client.info.businessName,
      description: this.client.info.description,
      email: this.client.info.email,
      website: this.client.info.website,
      address: this.client.info.address,
      category: this.client.info.category,
      subcategory: this.client.info.subcategory,
      is_business: this.client.info.isBusiness,
      is_enterprise: this.client.info.isEnterprise,
      is_verified: this.client.info.isVerified,
      device_count: this.client.info.deviceCount,
      profile_pic_url: this.client.info.profilePicUrl
    };
  }

  // 发送消息
  async sendMessage(to, message) {
    try {
      if (!this.client || !this.isReady) {
        throw new Error('客户端未就绪');
      }

      const result = await this.client.sendMessage(to, message);
      
      return {
        success: true,
        message: '消息发送成功',
        data: {
          message_id: result.id._serialized
        }
      };

    } catch (error) {
      console.error(`Worker ${this.sessionId}: 发送消息失败`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 发送消息到父进程
  sendToParent(message) {
    if (parentPort) {
      parentPort.postMessage(message);
    }
  }

  // 关闭Worker
  async shutdown() {
    try {
      await this.disconnect();
      console.log(`Worker ${this.sessionId}: 已关闭`);
      process.exit(0);
    } catch (error) {
      console.error(`Worker ${this.sessionId}: 关闭失败`, error);
      process.exit(1);
    }
  }
}

// 创建Worker实例
const worker = new WhatsAppWorker(
  workerData.sessionId,
  workerData.phoneNumber,
  workerData.tenantId,
  workerData.sessionDataDir
);

// 处理来自父进程的消息
parentPort.on('message', async (message) => {
  try {
    console.log(`Worker ${workerData.sessionId}: 收到命令`, message.type);
    
    let result;
    
    switch (message.type) {
      case 'connect':
        result = await worker.connect();
        break;
      case 'disconnect':
        result = await worker.disconnect();
        break;
      case 'send_message':
        result = await worker.sendMessage(message.data.to, message.data.message);
        break;
      case 'get_info':
        result = {
          success: true,
          data: await worker.getClientInfo()
        };
        break;
      case 'shutdown':
        await worker.shutdown();
        return;
      default:
        result = {
          success: false,
          error: '未知命令'
        };
    }
    
    // 发送响应
    parentPort.postMessage({
      id: message.timestamp,
      ...result
    });
    
  } catch (error) {
    console.error(`Worker ${workerData.sessionId}: 处理命令失败`, error);
    parentPort.postMessage({
      id: message.timestamp,
      success: false,
      error: error.message
    });
  }
});

// 初始化Worker
worker.initialize().then(() => {
  console.log(`Worker ${workerData.sessionId}: 初始化完成`);
}).catch((error) => {
  console.error(`Worker ${this.sessionId}: 初始化失败`, error);
}); 