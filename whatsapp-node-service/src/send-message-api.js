import pkg from 'whatsapp-web.js';
const { Client, LocalAuth, MessageMedia } = pkg;
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 全局WhatsApp服务实例，将在index.js中设置
let whatsappService = null;

// 设置WhatsApp服务实例的函数
export function setWhatsAppService(service) {
    whatsappService = service;
}

// 发送消息API
export async function sendMessage(req, res) {
    try {
        const { account_id, phone_number, message_type, message, file_path } = req.body;

        console.log('📤 发送消息请求:', {
            account_id,
            phone_number,
            message_type,
            message,
            file_path
        });

        // 验证参数
        if (!account_id || !phone_number || !message_type) {
            return res.status(400).json({
                code: 400,
                message: '缺少必要参数'
            });
        }

        // 检查WhatsApp服务是否已初始化
        if (!whatsappService) {
            return res.status(500).json({
                code: 500,
                message: 'WhatsApp服务未初始化'
            });
        }

        // 根据account_id获取session_id
        // 从WhatsAppService中获取活跃的Session
        const activeSessions = Array.from(whatsappService.sessions.keys());
        let sessionId = null;
        
        // 优先选择已连接的Session
        for (const sessionKey of activeSessions) {
            const session = whatsappService.sessions.get(sessionKey);
            if (session && session.status === 'active' && session.client && session.client.info) {
                sessionId = sessionKey;
                console.log(`✅ 找到已连接的Session: ${sessionId}`);
                break;
            }
        }
        
        // 如果没有找到已连接的Session，使用第一个可用的Session
        if (!sessionId && activeSessions.length > 0) {
            sessionId = activeSessions[0];
            console.log(`⚠️ 使用第一个可用Session: ${sessionId}`);
        }
        
        console.log(`🔍 可用的Session:`, activeSessions);
        console.log(`🔍 选择的Session ID:`, sessionId);
        
        // 获取Session状态
        let session = whatsappService.sessions.get(sessionId);
        console.log(`🔍 查找Session: ${sessionId}`, {
            sessionExists: !!session,
            sessionStatus: session?.status,
            clientInfo: !!session?.client?.info,
            clientExists: !!session?.client
        });
        
        // 如果没有找到合适的Session，返回错误
        if (!session) {
            return res.status(400).json({
                code: 400,
                message: '没有可用的WhatsApp Session，请先连接WhatsApp账号'
            });
        }

        // 检查Session状态
        console.log(`🔍 Session详细状态:`, {
            sessionId: sessionId,
            status: session.status,
            hasClient: !!session.client,
            hasClientInfo: !!session.client?.info,
            clientInfo: session.client?.info,
            isRegistered: session.client?.isRegistered,
            ready: session.client?.ready
        });
        
        if (session.status !== 'active' || !session.client.info) {
            console.log(`❌ Session状态检查失败:`, {
                status: session.status,
                hasClientInfo: !!session.client?.info,
                clientReady: !!session.client
            });
            return res.status(400).json({
                code: 400,
                message: 'WhatsApp客户端未连接'
            });
        }

        // 格式化手机号
        const formattedPhone = formatPhoneNumber(phone_number);
        console.log(`📱 目标手机号: ${formattedPhone}`);

        // 根据消息类型发送不同类型的消息
        let result;
        try {
            switch (message_type) {
                case 'text':
                    result = await sendTextMessage(session.client, formattedPhone, message);
                    break;
                case 'image':
                    if (!file_path) {
                        return res.status(400).json({
                            code: 400,
                            message: '图片文件路径不能为空'
                        });
                    }
                    result = await sendImageMessage(session.client, formattedPhone, file_path, message);
                    break;
                case 'video':
                    if (!file_path) {
                        return res.status(400).json({
                            code: 400,
                            message: '视频文件路径不能为空'
                        });
                    }
                    result = await sendVideoMessage(session.client, formattedPhone, file_path, message);
                    break;
                case 'file':
                    if (!file_path) {
                        return res.status(400).json({
                            code: 400,
                            message: '文件路径不能为空'
                        });
                    }
                    result = await sendFileMessage(session.client, formattedPhone, file_path);
                    break;
                default:
                    return res.status(400).json({
                        code: 400,
                        message: '不支持的消息类型'
                    });
            }

            if (result.success) {
                console.log('✅ 消息发送成功:', result.messageId);
                return res.json({
                    code: 200,
                    message: '消息发送成功',
                    data: {
                        message_id: result.messageId || 'unknown',
                        status: 'success',
                        timestamp: new Date().toISOString()
                    }
                });
            } else {
                console.log('❌ 消息发送失败:', result.error);
                return res.status(500).json({
                    code: 500,
                    message: result.error
                });
            }
        } catch (error) {
            console.error('❌ 发送消息异常:', error);
            return res.status(500).json({
                code: 500,
                message: '发送消息失败: ' + error.message
            });
        }

    } catch (error) {
        console.error('❌ 发送消息异常:', error);
        return res.status(500).json({
            code: 500,
            message: '发送消息失败: ' + error.message
        });
    }
}



// 格式化手机号
function formatPhoneNumber(phoneNumber) {
    // 移除所有非数字字符
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // 保持完整的手机号，不移除86前缀
    // 添加@c.us后缀
    return cleaned + '@c.us';
}

// 发送文本消息
async function sendTextMessage(client, phoneNumber, message) {
    try {
        console.log(`📤 发送文本消息到 ${phoneNumber}:`, message);
        
        const result = await client.sendMessage(phoneNumber, message);
        
        return {
            success: true,
            messageId: result.id._serialized
        };
    } catch (error) {
        console.error('❌ 发送文本消息失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 发送图片消息
async function sendImageMessage(client, phoneNumber, filePath, caption = '') {
    try {
        console.log(`📤 发送图片消息到 ${phoneNumber}:`, filePath);

        // 处理文件路径 - 如果是相对路径，需要指向项目根目录
        let actualFilePath = filePath;
        if (!filePath.startsWith('/') && !filePath.startsWith('C:') && !filePath.startsWith('../')) {
            // 相对路径，指向项目根目录
            actualFilePath = `../${filePath}`;
        }

        console.log(`🔍 实际文件路径: ${actualFilePath}`);

        if (!fs.existsSync(actualFilePath)) {
            console.log(`❌ 文件不存在: ${actualFilePath}`);
            return {
                success: false,
                error: '图片文件不存在'
            };
        }

        const media = MessageMedia.fromFilePath(actualFilePath);
        if (caption) {
            media.caption = caption;
        }
        const result = await client.sendMessage(phoneNumber, media);

        return {
            success: true,
            messageId: result.id._serialized
        };
    } catch (error) {
        console.error('❌ 发送图片消息失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 发送视频消息
async function sendVideoMessage(client, phoneNumber, filePath, caption = '') {
    try {
        console.log(`📤 发送视频消息到 ${phoneNumber}:`, filePath);

        // 处理文件路径 - 如果是相对路径，需要指向项目根目录
        let actualFilePath = filePath;
        if (!filePath.startsWith('/') && !filePath.startsWith('C:') && !filePath.startsWith('../')) {
            // 相对路径，指向项目根目录
            actualFilePath = `../${filePath}`;
        }

        console.log(`🔍 实际文件路径: ${actualFilePath}`);

        if (!fs.existsSync(actualFilePath)) {
            console.log(`❌ 文件不存在: ${actualFilePath}`);
            return {
                success: false,
                error: '视频文件不存在'
            };
        }

        const media = MessageMedia.fromFilePath(actualFilePath);
        if (caption) {
            media.caption = caption;
        }
        const result = await client.sendMessage(phoneNumber, media);

        return {
            success: true,
            messageId: result.id._serialized
        };
    } catch (error) {
        console.error('❌ 发送视频消息失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 发送文件消息
async function sendFileMessage(client, phoneNumber, filePath) {
    try {
        console.log(`📤 发送文件消息到 ${phoneNumber}:`, filePath);
        
        if (!fs.existsSync(filePath)) {
            return {
                success: false,
                error: '文件不存在'
            };
        }

        const media = MessageMedia.fromFilePath(filePath);
        const result = await client.sendMessage(phoneNumber, media);
        
        return {
            success: true,
            messageId: result.id._serialized
        };
    } catch (error) {
        console.error('❌ 发送文件消息失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 清理客户端
export function cleanupClient(accountId) {
    const client = clients.get(accountId);
    if (client) {
        client.destroy();
        clients.delete(accountId);
        console.log(`🧹 已清理账号 ${accountId} 的客户端`);
    }
}

// 获取客户端状态
export function getClientStatus(accountId) {
    const client = clients.get(accountId);
    if (!client) {
        return 'disconnected';
    }
    
    if (client.info) {
        return 'connected';
    }
    
    return 'connecting';
} 