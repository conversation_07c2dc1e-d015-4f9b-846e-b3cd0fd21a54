import { Worker } from 'worker_threads';
import path from 'path';
import fs from 'fs-extra';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class WorkerManager {
  constructor() {
    this.workers = new Map(); // sessionId -> worker
    this.workerScript = path.join(__dirname, 'whatsapp-worker.js');
  }

  // 为会话创建独立的Worker进程
  async createWorker(sessionId, phoneNumber, tenantId) {
    try {
      console.log(`创建Worker进程: ${sessionId}`);
      
      // 创建Worker
      const worker = new Worker(this.workerScript, {
        workerData: {
          sessionId,
          phoneNumber,
          tenantId,
          sessionDataDir: process.env.SESSION_DATA_DIR || './sessions'
        }
      });

      // 设置消息处理器
      worker.on('message', (message) => {
        console.log(`Worker ${sessionId} 消息:`, message);
        this.handleWorkerMessage(sessionId, message);
      });

      worker.on('error', (error) => {
        console.error(`Worker ${sessionId} 错误:`, error);
        this.cleanupWorker(sessionId);
      });

      worker.on('exit', (code) => {
        console.log(`Worker ${sessionId} 退出，代码: ${code}`);
        this.cleanupWorker(sessionId);
      });

      // 存储Worker
      this.workers.set(sessionId, worker);

      return {
        success: true,
        message: 'Worker创建成功'
      };

    } catch (error) {
      console.error(`创建Worker失败: ${sessionId}`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 处理Worker消息
  handleWorkerMessage(sessionId, message) {
    // 这里可以处理来自Worker的消息
    // 比如状态更新、错误通知等
    console.log(`处理Worker消息: ${sessionId}`, message);
  }

  // 向Worker发送命令
  async sendCommand(sessionId, command, data = {}) {
    const worker = this.workers.get(sessionId);
    if (!worker) {
      return {
        success: false,
        error: 'Worker不存在'
      };
    }

    try {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Worker响应超时'));
        }, 30000);

        worker.once('message', (response) => {
          clearTimeout(timeout);
          resolve(response);
        });

        worker.postMessage({
          type: command,
          data,
          timestamp: Date.now()
        });
      });
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 清理Worker
  cleanupWorker(sessionId) {
    const worker = this.workers.get(sessionId);
    if (worker) {
      worker.terminate();
      this.workers.delete(sessionId);
      console.log(`Worker已清理: ${sessionId}`);
    }
  }

  // 获取所有活跃的Worker
  getActiveWorkers() {
    return Array.from(this.workers.keys());
  }

  // 关闭所有Worker
  async shutdown() {
    console.log('关闭所有Worker...');
    const promises = Array.from(this.workers.keys()).map(sessionId => {
      return this.sendCommand(sessionId, 'shutdown');
    });
    
    await Promise.allSettled(promises);
    this.workers.clear();
    console.log('所有Worker已关闭');
  }
} 