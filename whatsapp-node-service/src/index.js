import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs-extra';
import { WhatsAppService } from './services/WhatsAppService.js';
import GroupSendingEngine from './group-sending-engine.js';
import { sendMessage, setWhatsAppService } from './send-message-api.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 创建WhatsApp服务实例
const whatsappService = new WhatsAppService();

// 设置全局WhatsApp服务实例
setWhatsAppService(whatsappService);

// 创建群发引擎实例
const groupSendingEngine = new GroupSendingEngine();

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'whatsapp-node-service'
  });
});

// WhatsApp API路由
app.post('/api/whatsapp/action', async (req, res) => {
  try {
    const { session_id, phone_number, tenant_id, action, data } = req.body;
    
    console.log(`收到WhatsApp请求: ${action}`, { session_id, phone_number, tenant_id });
    
    let result;
    
    switch (action) {
      case 'create':
        result = await whatsappService.createSession(session_id, phone_number, tenant_id, data);
        break;
      case 'connect':
        result = await whatsappService.connectSession(session_id, phone_number, tenant_id);
        break;
      case 'reconnect':
        result = await whatsappService.reconnectSession(session_id, phone_number, tenant_id);
        break;
      case 'disconnect':
        result = await whatsappService.disconnectSession(session_id, phone_number, tenant_id);
        break;
      case 'send_message':
        result = await whatsappService.sendMessage(session_id, phone_number, tenant_id, data);
        break;
      case 'get_info':
        result = await whatsappService.getClientInfo(session_id, phone_number, tenant_id);
        break;
      case 'cleanup':
        result = await whatsappService.cleanupSession(session_id, phone_number, tenant_id);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: `不支持的操作: ${action}`
        });
    }
    
    res.json(result);
  } catch (error) {
    console.error('WhatsApp API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取Session状态
app.get('/api/whatsapp/session/:session_id/status', async (req, res) => {
  try {
    const { session_id } = req.params;
    const status = await whatsappService.getSessionStatus(session_id);
    res.json({ success: true, data: status });
  } catch (error) {
    console.error('获取Session状态错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取所有活跃Session
app.get('/api/whatsapp/sessions', async (req, res) => {
  try {
    const sessions = await whatsappService.getAllSessions();
    res.json({ success: true, data: sessions });
  } catch (error) {
    console.error('获取Session列表错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 群发任务API
app.post('/api/group-sending/execute', async (req, res) => {
  try {
    const { task_id, task_data } = req.body;
    
    console.log(`收到群发任务请求: ${task_id}`, task_data);
    
    const result = groupSendingEngine.startTask(task_data);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('群发任务执行错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 发送消息API
app.post('/api/whatsapp/send-message', sendMessage);

// 群发任务控制API
app.post('/api/group-sending/control', async (req, res) => {
  try {
    const { task_id, action } = req.body;
    
    console.log(`收到群发任务控制请求: ${action}`, { task_id });
    
    let result;
    
    switch (action) {
      case 'pause':
        result = groupSendingEngine.pauseTask(task_id);
        break;
      case 'resume':
        result = groupSendingEngine.resumeTask(task_id);
        break;
      case 'terminate':
        result = groupSendingEngine.terminateTask(task_id);
        break;
      case 'status':
        result = { status: groupSendingEngine.getTaskStatus(task_id) };
        break;
      default:
        return res.status(400).json({
          success: false,
          error: `不支持的操作: ${action}`
        });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('群发任务控制错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 清理所有无效Session
app.post('/api/whatsapp/cleanup-all', async (req, res) => {
  try {
    const result = await whatsappService.cleanupAllInvalidSessions();
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('清理所有Session错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 清理未扫码的Session
app.post('/api/whatsapp/session/:session_id/cleanup-unscanned', async (req, res) => {
  try {
    const { session_id } = req.params;
    const result = await whatsappService.cleanupUnscannedSession(session_id);
    res.json({ 
      success: true, 
      message: result ? 'Session已清理' : 'Session受保护，未清理',
      cleaned: result
    });
  } catch (error) {
    console.error('清理未扫码Session错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取Node服务状态
app.get('/api/whatsapp/service/status', (req, res) => {
  try {
    const status = whatsappService.getServiceStatus();
    res.json({ 
      success: true, 
      data: status
    });
  } catch (error) {
    console.error('获取服务状态错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 按需恢复Session
app.post('/api/whatsapp/session/:session_id/restore', async (req, res) => {
  try {
    const { session_id } = req.params;
    console.log(`收到按需恢复请求: ${session_id}`);
    
    const result = await whatsappService.restoreSessionOnDemand(session_id);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('按需恢复Session错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    error: '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`WhatsApp Node服务启动在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`API端点: http://localhost:${PORT}/api/whatsapp/action`);
});

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到SIGTERM信号，正在关闭服务...');
  await whatsappService.cleanupAllSessions();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('收到SIGINT信号，正在关闭服务...');
  await whatsappService.cleanupAllSessions();
  process.exit(0);
}); 