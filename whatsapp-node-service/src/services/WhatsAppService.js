import pkg from 'whatsapp-web.js'
const { Client, LocalAuth } = pkg
import QRCode from 'qrcode'
import fs from 'fs-extra'
import path from 'path'
import { fileURLToPath } from 'url'
import { config, getBackendApiUrl, getChromePath } from '../config.js'

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class WhatsAppService {
  constructor() {
    this.sessions = new Map()
    this.sessionLocks = new Map()
    this.sessionDataDir = config.SESSION_DATA_DIR
    this.maxConcurrentSessions = config.MAX_CONCURRENT_SESSIONS
    this.serviceId = `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    this.heartbeatInterval = null
    
    // 清理任务相关
    this.lastCleanupTime = Date.now()
    this.lastSessionCount = 0
    this.cleanupStats = { total: 0, cleaned: 0 }
    
    // 确保session目录存在
    this.ensureSessionDirectory()
    
    // 通知Go后端服务启动
    this.notifyServiceStart()
    
    // 启动时加载已存在的session
    this.loadExistingSessions()
    
    // 启动清理任务
    this.startCleanupTask()
    
    // 启动心跳机制
    this.startHeartbeat()
    
    // 启动健康检查
    this.startHealthCheck()
    
    // 监听进程退出事件
    this.setupGracefulShutdown()
  }

  // 获取会话锁
  async acquireSessionLock(sessionId, timeout = 10000) {
    const startTime = Date.now();
    
    while (this.sessionLocks.has(sessionId)) {
      if (Date.now() - startTime > timeout) {
        throw new Error(`获取会话锁超时: ${sessionId}`);
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.sessionLocks.set(sessionId, true);
    return () => {
      this.sessionLocks.delete(sessionId);
    };
  }

  // 检查并发限制
  checkConcurrencyLimit() {
    if (this.sessions.size >= this.maxConcurrentSessions) {
      throw new Error(`已达到最大会话数限制: ${this.maxConcurrentSessions}`);
    }
  }

  // 获取活跃会话数
  getActiveSessionCount() {
    return this.sessions.size;
  }

  // 获取会话统计信息
  getSessionStats() {
    return {
      activeSessions: this.sessions.size,
      maxSessions: this.maxConcurrentSessions,
      lockedSessions: this.sessionLocks.size,
      availableSlots: this.maxConcurrentSessions - this.sessions.size
    };
  }

  // 确保Session目录存在
  ensureSessionDirectory() {
    if (!fs.existsSync(this.sessionDataDir)) {
      fs.mkdirpSync(this.sessionDataDir);
    }
  }

  // 启动定时清理任务
  startCleanupTask() {
    // 记录上次清理时间
    this.lastCleanupTime = Date.now();
    this.lastCleanupCount = 0;
    
    setInterval(() => {
      // 智能清理策略：如果没有需要清理的Session，延长检查间隔
      const timeSinceLastCleanup = Date.now() - this.lastCleanupTime;
      const shouldRunCleanup = (
        // 强制清理：超过15分钟必须检查一次
        timeSinceLastCleanup > 15 * 60 * 1000 ||
        // 有新Session创建时检查
        this.sessions.size > this.lastCleanupCount ||
        // 有Session数量变化时检查
        this.sessions.size !== this.lastSessionCount
      );
      
      if (shouldRunCleanup) {
        this.cleanupExpiredSessions();
        this.lastCleanupTime = Date.now();
        this.lastSessionCount = this.sessions.size;
      }
    }, config.CLEANUP_INTERVAL);
  }

  // 清理过期Session
  async cleanupExpiredSessions() {
    const now = new Date();
    const unscannedMaxAge = config.UNSCANNED_SESSION_MAX_AGE;
    
    // 统计信息
    let totalSessions = 0;
    let authenticatedSessions = 0;
    let cleanupCandidates = 0;
    let cleanedSessions = 0;
    let orphanedFiles = 0;
    
    console.log(`🧹 开始执行Session清理任务...`);
    
    // 第一步：清理内存中的过期Session
    for (const [sessionId, session] of this.sessions) {
      totalSessions++;
      const sessionAge = now - session.createdAt;
      
      // 检查session状态
      const isRegistered = session.client.isRegistered === true;
      const hasInfo = session.client.info && session.client.info.wid;
      const isAuthenticated = isRegistered || hasInfo;
      const hasPhoneNumber = session.phoneNumber && session.phoneNumber.trim() !== '';
      
      // 已认证的session不清理
      if (isAuthenticated || hasPhoneNumber) {
        authenticatedSessions++;
        continue;
      }
      
      // 检查是否需要清理
      const shouldCleanup = (
        // 未扫码session超时
        (sessionAge > unscannedMaxAge && (session.status === 'initializing' || session.status === 'waiting_for_qr')) ||
        // 初始化失败
        session.status === 'failed' ||
        // 断开连接超过一定时间
        (session.status === 'disconnected' && sessionAge > unscannedMaxAge * 2)
      );
      
      if (shouldCleanup) {
        cleanupCandidates++;
        console.log(`🗑️ 清理过期Session: ${sessionId} (状态: ${session.status}, 年龄: ${Math.floor(sessionAge/1000)}秒)`);
        try {
          await this.cleanupSession(sessionId, session.phoneNumber, session.tenantId);
          cleanedSessions++;
        } catch (error) {
          console.error(`❌ 清理Session失败: ${sessionId}`, error);
        }
      }
    }
    
    // 第二步：清理孤立的文件夹（数据库中不存在但文件系统中存在）
    try {
      const orphanedCount = await this.cleanupOrphanedSessionFiles();
      orphanedFiles = orphanedCount;
    } catch (error) {
      console.error(`❌ 清理孤立文件失败:`, error);
    }
    
    // 只在有实际操作时输出统计信息
    if (cleanupCandidates > 0 || orphanedFiles > 0 || totalSessions > authenticatedSessions) {
      console.log(`📊 清理任务完成: 内存${totalSessions}个Session(已认证${authenticatedSessions}个,清理${cleanedSessions}个), 孤立文件${orphanedFiles}个`);
    }
  }

  // 创建新的WhatsApp Session
  async createSession(sessionId, phoneNumber, tenantId, data = {}) {
    try {
      console.log(`创建Session: ${sessionId} for ${phoneNumber}`);
      
      // 检查并发限制
      this.checkConcurrencyLimit();
      
      // 检查Session是否已存在
      if (this.sessions.has(sessionId)) {
        return {
          success: false,
          error: 'Session已存在'
        };
      }

      // 强制创建新的Session，不检查现有文件
      console.log(`强制创建新的Session: ${sessionId}`);

      // 清理可能的Chrome进程冲突
      const sessionPath = path.join(this.sessionDataDir, `session-${sessionId}`);
      try {
        const lockFile = path.join(sessionPath, 'SingletonLock');
        if (fs.existsSync(lockFile)) {
          fs.unlinkSync(lockFile);
          console.log(`已清理锁文件: ${lockFile}`);
        }
      } catch (cleanupError) {
        console.log(`清理锁文件失败:`, cleanupError.message);
      }

      // 创建新的客户端
      const client = new Client({
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: this.sessionDataDir
        }),
        puppeteer: {
          headless: true,
          executablePath: getChromePath(),
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-ipc-flooding-protection',
            '--window-size=1920,1080',
            '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          ]
        }
      });

      // 存储Session信息
      this.sessions.set(sessionId, {
        client,
        phoneNumber,
        tenantId,
        createdAt: new Date(),
        lastUsed: new Date(),
        status: 'initializing'
      });

      // 设置事件监听器 - 在initialize之前设置
      let qrCodeDataUrl = null;
      let qrCodeGenerated = false;
      
      client.on('qr', async (qr) => {
        console.log(`Session ${sessionId} 生成二维码`);
        try {
          qrCodeDataUrl = await QRCode.toDataURL(qr);
          qrCodeGenerated = true;
          console.log(`Session ${sessionId} 二维码生成成功`);
        } catch (error) {
          console.error('生成二维码失败:', error);
          qrCodeGenerated = true; // 即使失败也标记为已处理
        }
      });

      client.on('ready', async () => {
        console.log(`Session ${sessionId} 客户端已准备就绪`);
        
        // 等待更长时间确保isRegistered属性更新
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 更新Session状态
        const sessionInfo = this.sessions.get(sessionId);
        if (sessionInfo) {
          sessionInfo.status = 'active';
          sessionInfo.lastUsed = new Date();
        }
        
        // 获取客户端信息
        const info = client.info;
        console.log(`Session ${sessionId} 客户端信息:`, info);
        
        // 更新Session中的手机号
        if (info && info.wid) {
          const phoneNumber = info.wid.user;
          console.log(`Session ${sessionId} 手机号: ${phoneNumber}`);
          
          // 获取更多WhatsApp客户端信息
          try {
            const clientInfo = await this.getClientInfoInternal(client);
            console.log(`Session ${sessionId} 详细客户端信息:`, clientInfo);
            
            // 通知Go后端更新Session状态，包含详细信息
            const response = await fetch(getBackendApiUrl(`/api/whatsapp/sessions/${sessionId}/status`), {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                status: 'active',
                phone_number: phoneNumber,
                client_info: clientInfo // 包含头像、昵称等信息
              })
            });
            
            if (response.ok) {
              console.log(`Session ${sessionId} 状态已更新到Go后端`);
            } else {
              console.error(`Session ${sessionId} 状态更新失败:`, response.status);
            }
          } catch (error) {
            console.error(`Session ${sessionId} 获取客户端信息失败:`, error);
          }
        }
      });

      client.on('authenticated', () => {
        console.log(`Session ${sessionId} 认证成功`);
      });

      client.on('auth_failure', (msg) => {
        console.error(`Session ${sessionId} 认证失败:`, msg);
        
        // 更新Session状态为failed
        const sessionInfo = this.sessions.get(sessionId);
        if (sessionInfo) {
          sessionInfo.status = 'failed';
          sessionInfo.error = msg;
        }
      });

      client.on('disconnected', (reason) => {
        console.log(`Session ${sessionId} 连接断开:`, reason);
        
        try {
          // 更新Session状态
          const sessionInfo = this.sessions.get(sessionId);
          if (sessionInfo) {
            sessionInfo.status = 'disconnected';
            sessionInfo.error = reason;
          }
          
          // 清理client引用，但不删除session文件
          if (sessionInfo && sessionInfo.client) {
            try {
              sessionInfo.client.destroy();
            } catch (destroyError) {
              console.error(`销毁client失败: ${sessionId}`, destroyError);
            }
            // 从内存中移除，但保留session文件
            this.sessions.delete(sessionId);
          }
        } catch (error) {
          console.error(`处理断开连接事件失败: ${sessionId}`, error);
        }
      });

      // 初始化客户端
      await client.initialize();

      // 等待二维码生成或客户端注册
      if (!client.isRegistered) {
        console.log(`Session ${sessionId} 等待二维码生成...`);
        
        // 等待二维码生成，最多等待30秒
        let waitTime = 0;
        const maxWaitTime = 30000; // 30秒
        const checkInterval = 1000; // 每1秒检查一次
        
        while (!qrCodeGenerated && waitTime < maxWaitTime) {
          await new Promise(resolve => setTimeout(resolve, checkInterval));
          waitTime += checkInterval;
          
          // 每5秒输出一次等待状态
          if (waitTime % 5000 === 0) {
            console.log(`Session ${sessionId} 等待二维码生成中... (${waitTime/1000}s)`);
          }
        }
        
        if (!qrCodeGenerated) {
          console.log(`Session ${sessionId} 二维码生成超时，但继续尝试...`);
          // 即使超时也继续，因为可能二维码已经生成但没有触发事件
        } else {
          console.log(`Session ${sessionId} 二维码生成完成，用时: ${waitTime}ms`);
        }
      } else {
        console.log(`Session ${sessionId} 客户端已注册，无需二维码`);
      }

      return {
        success: true,
        message: 'Session创建成功',
        data: {
          session_id: sessionId,
          session_path: path.join(this.sessionDataDir, sessionId), // 实际路径
          qr_code: qrCodeDataUrl,
          status: 'waiting_for_qr'
        }
      };

    } catch (error) {
      console.error(`创建Session失败: ${sessionId}`, error);
      
      // 清理已创建的client
      const sessionInfo = this.sessions.get(sessionId);
      if (sessionInfo && sessionInfo.client) {
        try {
          await sessionInfo.client.destroy();
        } catch (destroyError) {
          console.error(`销毁client失败: ${sessionId}`, destroyError);
        }
        this.sessions.delete(sessionId);
      }
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 连接现有Session
  async connectSession(sessionId, phoneNumber, tenantId) {
    // 获取会话锁
    const releaseLock = await this.acquireSessionLock(sessionId);
    
    try {
      console.log(`连接Session: ${sessionId}`);
      
      // 优先使用按需恢复
      const restoreResult = await this.restoreSessionOnDemand(sessionId);
      if (restoreResult.success) {
        return restoreResult;
      }
      
      console.log(`按需恢复失败，尝试传统连接方式: ${sessionId}`);
      
      // 检查Session是否已连接
      if (this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId);
        const isRegistered = session.client.isRegistered === true;
        const hasInfo = session.client.info && session.client.info.wid;
        const isAuthenticated = Boolean(isRegistered || hasInfo);
        
        if (isAuthenticated) {
          return {
            success: true,
            message: 'Session已连接',
            data: {
              session_id: sessionId,
              status: 'connected',
              client_info: await this.getClientInfoInternal(session.client)
            }
          };
        }
      }

      // 检查Session文件是否存在 - LocalAuth会自动添加session-前缀
      const actualSessionId = `session-${sessionId}`;
      const sessionPath = path.join(this.sessionDataDir, actualSessionId);
      const hasExistingSession = await fs.pathExists(sessionPath);
      
      if (!hasExistingSession) {
        return {
          success: false,
          error: 'Session文件不存在，请先创建Session'
        };
      }

      // 清理可能的Chrome进程冲突
      console.log(`清理可能的Chrome进程冲突: ${sessionId}`);
      try {
        const lockFile = path.join(sessionPath, 'SingletonLock');
        if (fs.existsSync(lockFile)) {
          fs.unlinkSync(lockFile);
          console.log(`已清理锁文件: ${lockFile}`);
        }
      } catch (cleanupError) {
        console.log(`清理锁文件失败:`, cleanupError.message);
      }

      // 创建客户端
      const client = new Client({
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: this.sessionDataDir
        }),
        puppeteer: {
          headless: true,
          executablePath: getChromePath(),
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-ipc-flooding-protection',
            '--disable-field-trial-config',
            '--disable-extensions',
            '--disable-plugins',
            '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          ]
        }
      });

      // 设置事件监听器
      client.on('ready', () => {
        console.log(`Session ${sessionId} 已准备就绪`);
      });

      client.on('disconnected', (reason) => {
        console.log(`Session ${sessionId} 已断开:`, reason);
        this.sessions.delete(sessionId);
      });

      client.on('auth_failure', (message) => {
        console.log(`Session ${sessionId} 认证失败:`, message);
      });

      // 初始化客户端
      await client.initialize();

      console.log(`Session ${sessionId} 初始化完成，isRegistered: ${client.isRegistered}`);

      // 等待客户端准备就绪
      await new Promise((resolve) => {
        if (client.isRegistered === true) {
          console.log(`Session ${sessionId} 已注册，直接解析`);
          resolve();
        } else if (client.info && client.info.wid) {
          console.log(`Session ${sessionId} 有客户端信息，直接解析`);
          resolve();
        } else {
          console.log(`Session ${sessionId} 等待ready事件`);
          client.once('ready', () => {
            console.log(`Session ${sessionId} 收到ready事件`);
            resolve();
          });
          // 设置超时
          setTimeout(() => {
            console.log(`Session ${sessionId} ready事件超时，但继续处理`);
            resolve();
          }, 10000); // 减少到10秒超时
        }
      });

      console.log(`Session ${sessionId} 等待完成，isRegistered: ${client.isRegistered}`);

      // 改进的认证状态检查
      const isRegistered = client.isRegistered === true;
      const hasInfo = client.info && client.info.wid;
      const isAuthenticated = isRegistered || hasInfo;
      console.log(`Session ${sessionId} 认证检查: isRegistered=${isRegistered}, hasInfo=${hasInfo}, isAuthenticated=${isAuthenticated}`);

      // 如果客户端有信息但 isRegistered 为 undefined，等待更长时间
      if (!isRegistered && hasInfo) {
        console.log(`Session ${sessionId} 有客户端信息但未注册，等待认证完成...`);
        await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
        
        // 重新检查认证状态
        const finalIsRegistered = client.isRegistered === true;
        const finalHasInfo = client.info && client.info.wid;
        const finalIsAuthenticated = finalIsRegistered || finalHasInfo;
        
        console.log(`Session ${sessionId} 最终认证检查: isRegistered=${finalIsRegistered}, hasInfo=${finalHasInfo}, isAuthenticated=${finalIsAuthenticated}`);
        
        if (!finalIsAuthenticated) {
          console.log(`Session ${sessionId} 未认证，返回错误`);
          return {
            success: false,
            error: 'Session未认证，请扫描二维码'
          };
        }
      } else if (!isAuthenticated) {
        console.log(`Session ${sessionId} 未认证，返回错误`);
        return {
          success: false,
          error: 'Session未认证，请扫描二维码'
        };
      }

      console.log(`Session ${sessionId} 认证成功，立即返回`);

      // 存储Session信息
      this.sessions.set(sessionId, {
        client,
        phoneNumber,
        tenantId,
        status: 'connected',
        createdAt: new Date(),
        lastUsed: new Date()
      });

      // 获取客户端信息
      const clientInfo = await this.getClientInfoInternal(client);

      return {
        success: true,
        message: 'Session连接成功',
        data: {
          session_id: sessionId,
          status: 'connected',
          client_info: clientInfo
        }
      };

    } catch (error) {
      console.error(`连接Session失败: ${sessionId}`, error);
      
      // 如果是Chrome进程冲突，尝试清理并重试
      if (error.message.includes('SingletonLock') || error.message.includes('Failed to launch')) {
        console.log(`检测到Chrome进程冲突，尝试清理...`);
        try {
          const sessionPath = path.join(this.sessionDataDir, `session-${sessionId}`);
          const lockFile = path.join(sessionPath, 'SingletonLock');
          if (fs.existsSync(lockFile)) {
            fs.unlinkSync(lockFile);
            console.log(`已清理锁文件: ${lockFile}`);
          }
        } catch (cleanupError) {
          console.log(`清理锁文件失败:`, cleanupError.message);
        }
      }
      
      return {
        success: false,
        error: error.message
      };
    } finally {
      // 释放会话锁
      releaseLock();
    }
  }

  // 断开Session（只关闭Chrome，保留session文件）
  async disconnectSession(sessionId, phoneNumber, tenantId) {
    // 获取会话锁
    const releaseLock = await this.acquireSessionLock(sessionId);
    
    try {
      console.log(`断开Session: ${sessionId}`);
      
      const session = this.sessions.get(sessionId);
      if (!session) {
        return {
          success: false,
          error: 'Session不存在'
        };
      }

      // 检查session是否已认证
      const isRegistered = session.client.isRegistered === true;
      const hasInfo = session.client.info && session.client.info.wid;
      const isAuthenticated = isRegistered || hasInfo;
      const hasPhoneNumber = session.phoneNumber && session.phoneNumber.trim() !== '';

      // 如果session已认证或有手机号，拒绝断开连接
      if (isAuthenticated || hasPhoneNumber) {
        console.log(`拒绝断开已认证session: ${sessionId} (认证: ${isAuthenticated}, 手机号: ${hasPhoneNumber})`);
        return {
          success: false,
          error: '已认证的session不允许断开连接'
        };
      }

      // 只关闭Chrome进程，不删除session文件
      await session.client.destroy();
      this.sessions.delete(sessionId);

      return {
        success: true,
        message: 'Session已断开，Chrome进程已关闭，session文件已保留'
      };

    } catch (error) {
      console.error(`断开Session失败: ${sessionId}`, error);
      return {
        success: false,
        error: error.message
      };
    } finally {
      // 释放会话锁
      releaseLock();
    }
  }

  // 重连Session（使用现有session文件）
  async reconnectSession(sessionId, phoneNumber, tenantId) {
    // 获取会话锁
    const releaseLock = await this.acquireSessionLock(sessionId);
    
    try {
      console.log(`重连Session: ${sessionId}`);
      
      // 检查Session是否已连接
      if (this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId);
        if (session.client.isRegistered) {
          return {
            success: true,
            message: 'Session已连接',
            data: {
              session_id: sessionId,
              status: 'connected',
              client_info: await this.getClientInfoInternal(session.client)
            }
          };
        }
      }

      // 检查Session文件是否存在
      const actualSessionId = `session-${sessionId}`;
      const sessionPath = path.join(this.sessionDataDir, actualSessionId);
      const hasExistingSession = await fs.pathExists(sessionPath);
      
      if (!hasExistingSession) {
        return {
          success: false,
          error: 'Session文件不存在，无法重连'
        };
      }

      // 创建新的Chrome进程，使用现有session文件
      const client = new Client({
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: this.sessionDataDir
        }),
        puppeteer: {
          headless: true,
          executablePath: getChromePath(),
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',
            '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          ]
        }
      });

      // 设置事件监听器
      client.on('ready', () => {
        console.log(`Session ${sessionId} 重连成功，已准备就绪`);
      });

      client.on('disconnected', (reason) => {
        console.log(`Session ${sessionId} 重连后断开:`, reason);
        this.sessions.delete(sessionId);
      });

      // 初始化客户端
      await client.initialize();

      console.log(`Session ${sessionId} 重连初始化完成，isRegistered: ${client.isRegistered}`);

      // 等待客户端准备就绪
      await new Promise((resolve) => {
        if (client.isRegistered) {
          console.log(`Session ${sessionId} 已注册，直接解析`);
          resolve();
        } else {
          console.log(`Session ${sessionId} 等待ready事件`);
          client.once('ready', () => {
            console.log(`Session ${sessionId} 收到ready事件`);
            resolve();
          });
          // 设置超时
          setTimeout(() => {
            console.log(`Session ${sessionId} ready事件超时，但继续处理`);
            resolve();
          }, 30000); // 30秒超时
        }
      });

      // 检查认证状态
      const isRegistered = client.isRegistered === true;
      const hasInfo = client.info && client.info.wid;
      const isAuthenticated = isRegistered || hasInfo;
      
      console.log(`Session ${sessionId} 重连认证检查: isRegistered=${isRegistered}, hasInfo=${hasInfo}, isAuthenticated=${isAuthenticated}`);

      if (!isAuthenticated) {
        await client.destroy();
        return {
          success: false,
          error: '重连失败：session文件已失效，需要重新扫码'
        };
      }

      // 保存session信息
      this.sessions.set(sessionId, {
        client,
        phoneNumber,
        tenantId,
        status: 'connected',
        createdAt: new Date(),
        lastUsed: new Date()
      });

      return {
        success: true,
        message: 'Session重连成功',
        data: {
          session_id: sessionId,
          status: 'connected',
          client_info: await this.getClientInfoInternal(client)
        }
      };

    } catch (error) {
      console.error(`重连Session失败: ${sessionId}`, error);
      return {
        success: false,
        error: error.message
      };
    } finally {
      // 释放会话锁
      releaseLock();
    }
  }

  // 发送消息
  async sendMessage(sessionId, phoneNumber, tenantId, data) {
    try {
      const { to, message } = data;
      
      // 验证必要参数
      if (!to || !message) {
        return {
          success: false,
          error: '缺少必要参数: to 或 message'
        };
      }
      
      console.log(`发送消息: ${sessionId} -> ${to}: ${message}`);
      
      const session = this.sessions.get(sessionId);
      if (!session) {
        return {
          success: false,
          error: 'Session不存在'
        };
      }

      // 检查客户端连接状态
      if (!session.client || !session.client.pupPage) {
        console.log(`Session ${sessionId} 客户端未连接，尝试重连...`);
        const reconnectResult = await this.reconnectSessionInternal(sessionId, session);
        if (!reconnectResult.success) {
          return {
            success: false,
            error: 'Session连接失败，无法发送消息'
          };
        }
      }

      // 检查页面是否仍然连接
      try {
        await session.client.pupPage.evaluate(() => true);
      } catch (pageError) {
        console.log(`Session ${sessionId} 页面连接已断开，尝试重连...`);
        const reconnectResult = await this.reconnectSessionInternal(sessionId, session);
        if (!reconnectResult.success) {
          return {
            success: false,
            error: 'Session页面连接失败，无法发送消息'
          };
        }
      }

      // 更可靠的认证检查
      const isRegistered = session.client.isRegistered === true;
      const hasInfo = session.client.info && session.client.info.wid;
      const isAuthenticated = isRegistered || hasInfo;
      
      if (!isAuthenticated) {
        return {
          success: false,
          error: 'Session未认证'
        };
      }

      // 格式化电话号码
      const formattedNumber = to.includes('@c.us') ? to : `${to}@c.us`;
      
      // 发送消息
      const sentMessage = await session.client.sendMessage(formattedNumber, message);
      
      // 更新最后使用时间
      session.lastUsed = new Date();

      return {
        success: true,
        message: '消息发送成功',
        data: {
          message_id: sentMessage.id._serialized,
          timestamp: sentMessage.timestamp
        }
      };

    } catch (error) {
      console.error(`发送消息失败: ${sessionId}`, error);
      
      // 如果是连接错误，尝试重连
      if (error.message.includes('Session closed') || error.message.includes('Protocol error')) {
        console.log(`检测到连接错误，尝试重连Session: ${sessionId}`);
        const session = this.sessions.get(sessionId);
        if (session) {
          try {
            await this.reconnectSessionInternal(sessionId, session);
            // 重连成功后，可以在这里重试发送消息
            console.log(`Session ${sessionId} 重连成功，可以重试发送消息`);
          } catch (reconnectError) {
            console.error(`Session ${sessionId} 重连失败:`, reconnectError);
          }
        }
      }
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取客户端信息
  async getClientInfo(sessionId, phoneNumber, tenantId) {
    try {
      console.log(`获取客户端信息: ${sessionId}`);
      
      const session = this.sessions.get(sessionId);
      if (!session) {
        return {
          success: false,
          error: 'Session不存在'
        };
      }

      // 改进的认证状态检查
      const isRegistered = session.client.isRegistered === true;
      const hasInfo = session.client.info && session.client.info.wid;
      const isAuthenticated = Boolean(isRegistered || hasInfo);
      
      console.log(`Session ${sessionId} 认证检查: isRegistered=${isRegistered}, hasInfo=${!!hasInfo}, isAuthenticated=${isAuthenticated}`);
      
      if (!isAuthenticated) {
        // 如果客户端有信息但isRegistered为undefined，等待一段时间再检查
        if (hasInfo && !isRegistered) {
          console.log(`Session ${sessionId} 有客户端信息但未注册，等待认证完成...`);
          await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
          
          // 重新检查认证状态
          const finalIsRegistered = session.client.isRegistered === true;
          const finalHasInfo = session.client.info && session.client.info.wid;
          const finalIsAuthenticated = finalIsRegistered || finalHasInfo;
          
          console.log(`Session ${sessionId} 最终认证检查: isRegistered=${finalIsRegistered}, hasInfo=${finalHasInfo}, isAuthenticated=${finalIsAuthenticated}`);
          
          if (!finalIsAuthenticated) {
            return {
              success: false,
              error: 'Session未认证'
            };
          }
        } else {
          return {
            success: false,
            error: 'Session未认证'
          };
        }
      }

      const clientInfo = await this.getClientInfoInternal(session.client);
      
      // 更新最后使用时间
      session.lastUsed = new Date();

      return {
        success: true,
        data: {
          client_info: clientInfo
        }
      };

    } catch (error) {
      console.error(`获取客户端信息失败: ${sessionId}`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 内部方法：获取客户端信息
  async getClientInfoInternal(client) {
    try {
      const info = client.info;
      if (!info || !info.wid) {
        console.log('客户端信息不完整，跳过详细信息获取');
        return {};
      }
      
      const wid = info.wid._serialized;
      
      // 从WID中提取手机号
      const phoneNumber = wid.split('@')[0];
      
      console.log(`获取到客户端信息: WID=${wid}, 手机号=${phoneNumber}, 昵称=${info.pushname}`);
      
      // 获取头像URL
      let profilePicUrl = null;
      try {
        profilePicUrl = await client.getProfilePicUrl(wid);
      } catch (error) {
        console.log('获取头像失败:', error.message);
        // 头像获取失败不影响其他信息
      }

      return {
        wid: wid,
        phone_number: phoneNumber, // 添加手机号
        platform: info.platform,
        pushname: info.pushname,
        business_name: info.businessName || '',
        description: info.description || '',
        email: info.email || '',
        website: info.website || '',
        address: info.address || '',
        category: info.category || '',
        subcategory: info.subcategory || '',
        is_business: info.isBusiness || false,
        is_enterprise: info.isEnterprise || false,
        is_verified: info.isVerified || false,
        device_count: info.deviceCount || 1,
        profile_pic_url: profilePicUrl
      };
    } catch (error) {
      console.error('获取客户端信息失败:', error);
      // 返回基本信息而不是空对象
      return {
        wid: client.info?.wid?._serialized || '',
        phone_number: client.info?.wid?.user || '',
        platform: client.info?.platform || '',
        pushname: client.info?.pushname || '',
        profile_pic_url: null
      };
    }
  }

  // 清理Session
  async cleanupSession(sessionId, phoneNumber, tenantId) {
    try {
      console.log(`清理Session: ${sessionId}`);
      
      // 检查session是否已认证
      const session = this.sessions.get(sessionId);
      if (session) {
        const isRegistered = session.client.isRegistered === true;
        const hasInfo = session.client.info && session.client.info.wid;
        const isAuthenticated = isRegistered || hasInfo;
        const hasPhoneNumber = session.phoneNumber && session.phoneNumber.trim() !== '';

        // 如果session已认证或有手机号，拒绝清理
        if (isAuthenticated || hasPhoneNumber) {
          console.log(`拒绝清理已认证session: ${sessionId} (认证: ${isAuthenticated}, 手机号: ${hasPhoneNumber})`);
          return {
            success: false,
            error: '已认证的session不允许清理'
          };
        }

        await session.client.destroy();
        this.sessions.delete(sessionId);
      }

      // 删除Session文件 - LocalAuth会自动添加session-前缀
      const actualSessionId = `session-${sessionId}`;
      const sessionPath = path.join(this.sessionDataDir, actualSessionId);
      let fileSizeFreed = 0;
      let fileCountFreed = 0;

      if (await fs.pathExists(sessionPath)) {
        const stats = await fs.stat(sessionPath);
        fileSizeFreed = stats.size;
        
        // 计算文件数量
        const files = await fs.readdir(sessionPath);
        fileCountFreed = files.length;
        
        // 删除目录
        await fs.remove(sessionPath);
      }

      // 通知Go后端删除数据库记录
      try {
        const response = await fetch(getBackendApiUrl(`/api/whatsapp/sessions/${sessionId}/cleanup`), {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          console.log(`已通知Go后端删除Session记录: ${sessionId}`);
        } else {
          console.error(`通知Go后端删除Session记录失败: ${sessionId}, 状态: ${response.status}`);
        }
      } catch (error) {
        console.error(`通知Go后端删除Session记录出错: ${sessionId}`, error);
      }

      return {
        success: true,
        message: 'Session清理成功',
        data: {
          file_size_freed: fileSizeFreed,
          file_count_freed: fileCountFreed
        }
      };

    } catch (error) {
      console.error(`清理Session失败: ${sessionId}`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取Session状态
  async getSessionStatus(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return { status: 'not_found' };
    }

    return {
      status: session.status,
      phone_number: session.phoneNumber,
      tenant_id: session.tenantId,
      created_at: session.createdAt,
      last_used: session.lastUsed,
      is_registered: session.client.isRegistered
    };
  }

  // 获取所有Session
  async getAllSessions() {
    const sessions = [];
    for (const [sessionId, session] of this.sessions) {
      sessions.push({
        session_id: sessionId,
        phone_number: session.phoneNumber,
        tenant_id: session.tenantId,
        status: session.status,
        created_at: session.createdAt,
        last_used: session.lastUsed,
        is_registered: session.client.isRegistered
      });
    }
    return sessions;
  }

  // 清理孤立的Session文件（数据库中不存在但文件系统中存在）
  async cleanupOrphanedSessionFiles() {
    try {
      // 获取Go后端数据库中的所有Session ID
      const response = await fetch(getBackendApiUrl('/api/whatsapp/sessions/internal'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      let validSessionIds = [];
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          validSessionIds = result.data.map(session => session.session_id);
        }
      } else {
        console.log(`⚠️ 无法获取数据库Session列表，跳过孤立文件清理`);
        return 0;
      }
      
      // 获取文件系统中的所有Session文件夹
      const sessionDirs = [];
      if (await fs.pathExists(this.sessionDataDir)) {
        const files = await fs.readdir(this.sessionDataDir);
        for (const file of files) {
          if (file.startsWith('session-') && await fs.stat(path.join(this.sessionDataDir, file)).then(s => s.isDirectory())) {
            // 提取session ID (去掉session-前缀)
            const sessionId = file.replace('session-', '');
            sessionDirs.push({ sessionId, dirName: file });
          }
        }
      }
      
      // 找出孤立的文件夹（文件系统中存在但数据库中不存在）
      let cleanedCount = 0;
      for (const { sessionId, dirName } of sessionDirs) {
        // 检查是否在数据库中存在
        const existsInDb = validSessionIds.includes(sessionId);
        // 检查是否在内存中存在
        const existsInMemory = this.sessions.has(sessionId);
        
        if (!existsInDb && !existsInMemory) {
          // 孤立文件，需要清理
          const sessionPath = path.join(this.sessionDataDir, dirName);
          try {
            // 计算文件夹大小
            const stats = await fs.stat(sessionPath);
            const files = await fs.readdir(sessionPath);
            
            console.log(`🗑️ 清理孤立Session文件: ${sessionId} (${files.length}个文件)`);
            
            // 删除整个文件夹
            await fs.remove(sessionPath);
            cleanedCount++;
            
          } catch (error) {
            console.error(`❌ 清理孤立文件失败: ${sessionId}`, error);
          }
        }
      }
      
      return cleanedCount;
      
    } catch (error) {
      console.error(`❌ 清理孤立文件过程出错:`, error);
      return 0;
    }
  }

  // 清理所有无效Session
  async cleanupAllInvalidSessions() {
    try {
      console.log('清理所有无效Session');
      
      const sessionDirs = await fs.readdir(this.sessionDataDir);
      let totalFreed = 0;
      let totalFiles = 0;
      let cleanedSessions = 0;
      let protectedSessions = 0;

      for (const sessionDir of sessionDirs) {
        const sessionPath = path.join(this.sessionDataDir, sessionDir);
        const stats = await fs.stat(sessionPath);
        
        // 检查Session是否在内存中
        if (!this.sessions.has(sessionDir)) {
          // 检查session文件是否包含认证信息
          try {
            const sessionFiles = await fs.readdir(sessionPath);
            const hasAuthFiles = sessionFiles.some(file => 
              file.includes('session') || file.includes('auth') || file.includes('tokens')
            );
            
            // 如果有认证文件，保护这个session
            if (hasAuthFiles) {
              console.log(`保护已认证session文件: ${sessionDir}`);
              protectedSessions += 1;
              continue;
            }
            
            // 删除无效Session
            await fs.remove(sessionPath);
            totalFreed += stats.size;
            totalFiles += 1;
            cleanedSessions += 1;
            console.log(`清理无效Session: ${sessionDir}`);
          } catch (error) {
            console.log(`检查session文件失败: ${sessionDir}`, error.message);
            // 如果检查失败，保守起见不删除
            protectedSessions += 1;
          }
        } else {
          // 检查内存中的session是否已认证
          const session = this.sessions.get(sessionDir);
          const isRegistered = session.client.isRegistered === true;
          const hasInfo = session.client.info && session.client.info.wid;
          const isAuthenticated = isRegistered || hasInfo;
          const hasPhoneNumber = session.phoneNumber && session.phoneNumber.trim() !== '';
          
          if (isAuthenticated || hasPhoneNumber) {
            console.log(`保护已认证session: ${sessionDir} (认证: ${isAuthenticated}, 手机号: ${hasPhoneNumber})`);
            protectedSessions += 1;
          }
        }
      }

      return {
        success: true,
        data: {
          cleaned_sessions: cleanedSessions,
          protected_sessions: protectedSessions,
          total_size_freed: totalFreed,
          total_files_freed: totalFiles
        }
      };

    } catch (error) {
      console.error('清理所有Session失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 清理所有Session（关闭服务时调用）
  async cleanupAllSessions() {
    console.log('关闭所有Session的Chrome进程');
    
    for (const [sessionId, session] of this.sessions) {
      try {
        // 只关闭Chrome进程，不删除session文件
        await session.client.destroy();
        console.log(`已关闭Session的Chrome进程: ${sessionId}`);
      } catch (error) {
        console.error(`关闭Session Chrome进程失败: ${sessionId}`, error);
      }
    }
    
    // 清空内存中的session记录
    this.sessions.clear();
    console.log('所有Session已离线，session文件已保留');
  }

  // 立即清理未扫码的session
  async cleanupUnscannedSession(sessionId) {
    try {
      const session = this.sessions.get(sessionId);
      if (session) {
        // 更可靠的认证状态检查
        const isRegistered = session.client.isRegistered === true;
        const hasInfo = session.client.info && session.client.info.wid;
        const isAuthenticated = isRegistered || hasInfo;
        const hasPhoneNumber = session.phoneNumber && session.phoneNumber.trim() !== '';
        const isActive = session.status === 'active';
        
        // 只清理未认证、没有手机号且不是active状态的session
        if (!isAuthenticated && !hasPhoneNumber && !isActive) {
          console.log(`立即清理未扫码session: ${sessionId} (认证: ${isAuthenticated}, 手机号: ${hasPhoneNumber}, 状态: ${session.status})`);
          await this.cleanupSession(sessionId, session.phoneNumber, session.tenantId);
          return true;
        } else {
          console.log(`保护session: ${sessionId} (认证: ${isAuthenticated}, 手机号: ${hasPhoneNumber}, 状态: ${session.status})`);
          return false;
        }
      }
      return false;
    } catch (error) {
      console.error(`立即清理session失败: ${sessionId}`, error);
      return false;
    }
  }

  // 启动时加载已存在的session
  async loadExistingSessions() {
    try {
      console.log('🔄 开始从Go后端获取Session状态信息...');
      
      // 从Go后端获取所有session状态（使用内部接口）
      const response = await fetch(getBackendApiUrl('/api/whatsapp/sessions/internal'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10秒超时
      });
      
      if (!response.ok) {
        console.error(`❌ 获取Session状态失败: HTTP ${response.status}`);
        return;
      }
      
      const result = await response.json();
      if (!result.data || !result.data.success) {
        console.error('❌ 获取Session状态失败:', result.message || '响应格式错误');
        return;
      }
      
      const sessions = result.data.data || [];
      console.log(`✅ 从Go后端获取到 ${sessions.length} 个Session状态`);
      
      let restoredCount = 0;
      let skippedCount = 0;
      let failedCount = 0;
      
      // 遍历所有session状态
      for (const sessionInfo of sessions) {
        const sessionId = sessionInfo.session_id;
        const status = sessionInfo.status;
        const phoneNumber = sessionInfo.phone_number;
        const tenantId = sessionInfo.tenant_id;
        const isSessionValid = sessionInfo.is_session_valid;
        
        console.log(`🔍 检查Session: ${sessionId} (状态: ${status}, 手机号: ${phoneNumber}, 有效: ${isSessionValid})`);
        
        // 恢复活跃且有效的session，包括disconnected状态的session
        if ((status === 'active' || status === 'connected' || status === 'disconnected') && isSessionValid && phoneNumber) {
          console.log(`🔄 尝试恢复Session: ${sessionId}`);
          
          try {
            // 使用重试机制恢复session
            const result = await this.restoreSessionWithRetry(sessionInfo, 3);
            if (result.success) {
              restoredCount++;
            } else {
              failedCount++;
            }
          } catch (error) {
            console.error(`❌ 恢复Session失败: ${sessionId}`, error.message);
            
            // 通知Go后端清理失败的Session
            await this.notifyGoCleanupSession(sessionId, phoneNumber, tenantId);
            failedCount++;
          }
        } else {
          console.log(`⏭️ 跳过Session: ${sessionId} (状态: ${status}, 有效: ${isSessionValid})`);
          skippedCount++;
        }
      }
      
      console.log(`🎉 Session恢复完成。统计: 恢复 ${restoredCount} 个, 跳过 ${skippedCount} 个, 失败 ${failedCount} 个`);
    } catch (error) {
      console.error('❌ Session恢复过程中发生错误:', error);
    }
  }

  // 带重试机制的session恢复
  async restoreSessionWithRetry(sessionInfo, maxRetries = 3) {
    const sessionId = sessionInfo.session_id;
    const phoneNumber = sessionInfo.phone_number;
    const tenantId = sessionInfo.tenant_id;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 尝试恢复Session: ${sessionId} (尝试 ${attempt}/${maxRetries})`);
        
        // 在重试前清理可能的Chrome进程冲突
        if (attempt > 1) {
          await this.cleanupChromeProcess(sessionId);
        }
        
        // 创建客户端
        const client = new Client({
          authStrategy: new LocalAuth({
            clientId: sessionId,
            dataPath: this.sessionDataDir
          }),
          puppeteer: {
            headless: true,
            executablePath: getChromePath(),
            args: [
              '--no-sandbox',
              '--disable-setuid-sandbox',
              '--disable-dev-shm-usage',
              '--disable-accelerated-2d-canvas',
              '--no-first-run',
              '--no-zygote',
              '--disable-gpu',
              '--disable-web-security',
              '--disable-features=VizDisplayCompositor',
              '--disable-blink-features=AutomationControlled',
              '--disable-background-timer-throttling',
              '--disable-backgrounding-occluded-windows',
              '--disable-renderer-backgrounding',
              '--disable-ipc-flooding-protection',
              '--disable-field-trial-config',
              '--disable-extensions',
              '--disable-plugins',
              '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
          }
        });

        // 设置事件监听器
        client.on('ready', () => {
          console.log(`Session ${sessionId} 恢复成功，已准备就绪`);
        });

        client.on('disconnected', (reason) => {
          console.log(`Session ${sessionId} 恢复后断开:`, reason);
          this.sessions.delete(sessionId);
        });

        client.on('auth_failure', (message) => {
          console.log(`Session ${sessionId} 认证失败:`, message);
        });

        // 初始化客户端
        console.log(`🔄 开始初始化客户端: ${sessionId} (尝试 ${attempt})`);
        await client.initialize();
        
        // 等待客户端初始化完成
        console.log(`⏳ 等待客户端初始化完成: ${sessionId}`);
        await new Promise(resolve => setTimeout(resolve, 8000 + attempt * 2000)); // 递增等待时间
        
        // 等待客户端准备就绪
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error(`客户端初始化超时 (尝试 ${attempt})`));
          }, 30000 + attempt * 10000); // 递增超时时间
          
          if (client.isRegistered === true) {
            clearTimeout(timeout);
            resolve();
          } else if (client.info && client.info.wid) {
            clearTimeout(timeout);
            resolve();
          } else {
            client.once('ready', () => {
              clearTimeout(timeout);
              resolve();
            });
          }
        });

        // 验证认证状态
        const isRegistered = client.isRegistered === true;
        const hasInfo = client.info && client.info.wid;
        const isAuthenticated = Boolean(isRegistered || hasInfo);
        
        console.log(`Session ${sessionId} 认证检查 (尝试 ${attempt}): isRegistered=${isRegistered}, hasInfo=${!!hasInfo}, isAuthenticated=${isAuthenticated}`);
        
        if (isAuthenticated) {
          console.log(`✅ Session ${sessionId} 恢复成功 (尝试 ${attempt})`);
          
          // 存储到内存
          this.sessions.set(sessionId, {
            client,
            phoneNumber: phoneNumber,
            tenantId: tenantId,
            createdAt: new Date(sessionInfo.created_at),
            lastUsed: new Date(sessionInfo.last_used),
            status: 'active'
          });
          
          // 通知Go后端更新状态
          await this.updateSessionStatus(sessionId, 'active', phoneNumber, tenantId);
          
          return { success: true, attempt };
        } else {
          // 认证失败，清理客户端
          await client.destroy();
          throw new Error(`Session未认证 (尝试 ${attempt})`);
        }
        
      } catch (error) {
        console.error(`❌ Session恢复失败 (尝试 ${attempt}/${maxRetries}): ${sessionId}`, error.message);
        
        // 如果不是最后一次尝试，进行错误处理和等待
        if (attempt < maxRetries) {
          // 处理特定错误类型
          if (error.message.includes('SingletonLock') || error.message.includes('Failed to launch')) {
            console.log(`🔧 检测到Chrome进程冲突，清理后重试...`);
            await this.cleanupChromeProcess(sessionId);
          }
          
          // 等待后重试，递增等待时间
          const waitTime = 3000 * attempt;
          console.log(`⏳ 等待 ${waitTime}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        } else {
          // 最后一次尝试失败，抛出错误
          throw error;
        }
      }
    }
    
    // 所有尝试都失败
    return { success: false, error: `所有 ${maxRetries} 次尝试都失败` };
  }

  // 清理Chrome进程冲突
  async cleanupChromeProcess(sessionId) {
    try {
      console.log(`🔧 清理Chrome进程冲突: ${sessionId}`);
      
      // 清理可能的锁文件
      const sessionPath = path.join(this.sessionDataDir, `session-${sessionId}`);
      const lockFile = path.join(sessionPath, 'SingletonLock');
      
      if (fs.existsSync(lockFile)) {
        fs.unlinkSync(lockFile);
        console.log(`✅ 已清理锁文件: ${lockFile}`);
      }
      
      // 清理其他可能的Chrome临时文件
      const tempFiles = ['Singleton*', 'chrome_*', '.org.chromium.*'];
      for (const pattern of tempFiles) {
        try {
          const files = fs.readdirSync(sessionPath).filter(file => 
            file.includes(pattern.replace('*', ''))
          );
          for (const file of files) {
            const filePath = path.join(sessionPath, file);
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
              console.log(`✅ 已清理临时文件: ${file}`);
            }
          }
        } catch (cleanupError) {
          // 忽略清理错误
        }
      }
      
      // 等待一段时间确保进程完全清理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.log(`⚠️ 清理Chrome进程失败: ${sessionId}`, error.message);
    }
  }
  
  // 通知Go后端清理Session
  async notifyGoCleanupSession(sessionId, phoneNumber, tenantId) {
    try {
      console.log(`🔄 通知Go后端清理未注册的Session: ${sessionId}`);
      const response = await fetch(getBackendApiUrl(`/api/whatsapp/sessions/${sessionId}/cleanup`), {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        console.log(`✅ Session ${sessionId} 已通知Go后端清理`);
      } else {
        console.error(`❌ 通知Go后端清理Session ${sessionId} 失败:`, response.status);
      }
    } catch (error) {
      console.error(`❌ 通知Go后端清理Session ${sessionId} 失败:`, error);
    }
  }
  
  // 更新Session状态到Go后端
  async updateSessionStatus(sessionId, status, phoneNumber, tenantId) {
    try {
      const response = await fetch(getBackendApiUrl(`/api/whatsapp/sessions/${sessionId}/status`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: status,
          phone_number: phoneNumber,
          tenant_id: tenantId
        })
      });
      
      if (response.ok) {
        console.log(`Session ${sessionId} 状态已更新为: ${status}`);
      } else {
        console.error(`更新Session ${sessionId} 状态失败:`, response.status);
      }
    } catch (error) {
      console.error(`更新Session ${sessionId} 状态失败:`, error);
    }
  }

  // 通知Go后端服务启动
  async notifyServiceStart() {
    try {
      console.log(`🚀 通知Go后端Node服务启动: ${this.serviceId}`);
      
      const response = await fetch(getBackendApiUrl('/api/whatsapp/node-service/restart'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: this.serviceId,
          timestamp: new Date().toISOString(),
          active_sessions: Array.from(this.sessions.keys()),
          process_pid: process.pid
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Node服务启动通知成功: ${this.serviceId} (重启次数: ${result.restart_count})`);
      } else {
        console.error(`❌ Node服务启动通知失败: HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Node服务启动通知失败:', error);
    }
  }

  // 启动心跳机制
  startHeartbeat() {
    // 每30秒发送一次心跳
    this.heartbeatInterval = setInterval(async () => {
      try {
        const response = await fetch(getBackendApiUrl('/api/whatsapp/node-service/heartbeat'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            service_id: this.serviceId,
            active_sessions: Array.from(this.sessions.keys()),
            process_pid: process.pid
          })
        });
        
        if (!response.ok) {
          console.error(`❌ 心跳发送失败: HTTP ${response.status}`);
        }
      } catch (error) {
        console.error('❌ 心跳发送失败:', error);
      }
    }, 30000); // 30秒间隔
    
    console.log(`💓 心跳机制已启动: ${this.serviceId}`);
  }

  // 停止心跳机制
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      console.log(`💔 心跳机制已停止: ${this.serviceId}`);
    }
  }

  // 设置优雅关闭
  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      console.log(`📡 收到${signal}信号，开始优雅关闭...`);
      
      // 停止心跳
      this.stopHeartbeat();
      
      // 停止健康检查
      this.stopHealthCheck();
      
      // 通知Go后端服务停止
      try {
        await fetch(getBackendApiUrl('/api/whatsapp/node-service/heartbeat'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            service_id: this.serviceId,
            active_sessions: [],
            process_pid: 0,
            status: 'stopping'
          })
        });
        console.log(`✅ 已通知Go后端服务停止: ${this.serviceId}`);
      } catch (error) {
        console.error('❌ 通知Go后端服务停止失败:', error);
      }
      
      // 清理所有session
      await this.cleanupAllSessions();
      
      console.log(`👋 Node服务优雅关闭完成: ${this.serviceId}`);
      process.exit(0);
    };
    
    // 监听各种退出信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon重启信号
    
    // 监听未捕获的异常
    process.on('uncaughtException', async (error) => {
      console.error('❌ 未捕获的异常:', error);
      await gracefulShutdown('uncaughtException');
    });
    
    process.on('unhandledRejection', async (reason, promise) => {
      console.error('❌ 未处理的Promise拒绝:', reason);
      await gracefulShutdown('unhandledRejection');
    });
  }

  // 启动健康检查机制
  startHealthCheck() {
    // 每2分钟检查一次session健康状态
    this.healthCheckInterval = setInterval(async () => {
      console.log('🔍 开始健康检查...');
      
      let healthyCount = 0;
      let unhealthyCount = 0;
      let reconnectedCount = 0;
      
      for (const [sessionId, session] of this.sessions) {
        try {
          // 检查客户端是否还活跃
          const isRegistered = session.client.isRegistered === true;
          const hasInfo = session.client.info && session.client.info.wid;
          const isHealthy = Boolean(isRegistered || hasInfo);
          
          if (isHealthy) {
            healthyCount++;
            // 更新最后使用时间
            session.lastUsed = new Date();
          } else {
            console.log(`⚠️ Session ${sessionId} 不健康，尝试重连`);
            unhealthyCount++;
            
            // 尝试重连
            const reconnectResult = await this.reconnectSessionInternal(sessionId, session);
            if (reconnectResult.success) {
              reconnectedCount++;
              console.log(`✅ Session ${sessionId} 重连成功`);
            } else {
              console.log(`❌ Session ${sessionId} 重连失败，将被清理`);
              // 清理失效的session
              await this.cleanupSessionInternal(sessionId, session);
            }
          }
        } catch (error) {
          console.error(`❌ 健康检查失败: ${sessionId}`, error.message);
          unhealthyCount++;
          
          // 清理失效的session
          await this.cleanupSessionInternal(sessionId, session);
        }
      }
      
      console.log(`🔍 健康检查完成: 健康 ${healthyCount} 个, 不健康 ${unhealthyCount} 个, 重连成功 ${reconnectedCount} 个`);
    }, 120000); // 每2分钟检查一次
    
    console.log(`🔍 健康检查机制已启动: ${this.serviceId}`);
  }

  // 停止健康检查
  stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log(`🔍 健康检查机制已停止: ${this.serviceId}`);
    }
  }

  // 内部重连方法
  async reconnectSessionInternal(sessionId, session) {
    try {
      console.log(`🔄 内部重连Session: ${sessionId}`);
      
      // 先销毁旧的客户端
      if (session.client) {
        try {
          await session.client.destroy();
        } catch (destroyError) {
          console.log(`⚠️ 销毁旧客户端失败: ${sessionId}`, destroyError.message);
        }
      }
      
      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 创建新的客户端
      const client = new Client({
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: this.sessionDataDir
        }),
        puppeteer: {
          headless: true,
          executablePath: getChromePath(),
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',
            '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          ]
        }
      });

      // 设置事件监听器
      client.on('ready', () => {
        console.log(`Session ${sessionId} 重连后已准备就绪`);
      });

      client.on('disconnected', (reason) => {
        console.log(`Session ${sessionId} 重连后断开:`, reason);
        this.sessions.delete(sessionId);
      });

      // 初始化客户端
      await client.initialize();
      
      // 等待客户端准备就绪
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('重连超时'));
        }, 30000);
        
        if (client.isRegistered === true || (client.info && client.info.wid)) {
          clearTimeout(timeout);
          resolve();
        } else {
          client.once('ready', () => {
            clearTimeout(timeout);
            resolve();
          });
        }
      });

      // 验证重连是否成功
      const isRegistered = client.isRegistered === true;
      const hasInfo = client.info && client.info.wid;
      const isAuthenticated = Boolean(isRegistered || hasInfo);
      
      if (isAuthenticated) {
        // 更新session信息
        session.client = client;
        session.lastUsed = new Date();
        session.status = 'active';
        
        // 通知Go后端更新状态
        await this.updateSessionStatus(sessionId, 'active', session.phoneNumber, session.tenantId);
        
        return { success: true };
      } else {
        await client.destroy();
        return { success: false, error: '重连后未认证' };
      }
      
    } catch (error) {
      console.error(`❌ 内部重连失败: ${sessionId}`, error.message);
      return { success: false, error: error.message };
    }
  }

  // 内部清理方法
  async cleanupSessionInternal(sessionId, session) {
    try {
      console.log(`🧹 内部清理Session: ${sessionId}`);
      
      // 销毁客户端
      if (session && session.client) {
        try {
          await session.client.destroy();
        } catch (destroyError) {
          console.log(`⚠️ 销毁客户端失败: ${sessionId}`, destroyError.message);
        }
      }
      
      // 从内存中移除
      this.sessions.delete(sessionId);
      
      // 通知Go后端清理
      await this.notifyGoCleanupSession(sessionId, session?.phoneNumber, session?.tenantId);
      
      console.log(`✅ Session内部清理完成: ${sessionId}`);
    } catch (error) {
      console.error(`❌ 内部清理失败: ${sessionId}`, error.message);
    }
  }

  // 按需恢复session
  async restoreSessionOnDemand(sessionId) {
    try {
      console.log(`🔄 按需恢复Session: ${sessionId}`);
      
      // 检查session是否已经在内存中
      if (this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId);
        console.log(`✅ Session ${sessionId} 已在内存中`);
        return {
          success: true,
          message: 'Session已存在',
          data: {
            session_id: sessionId,
            status: 'active',
            client_info: await this.getClientInfoInternal(session.client)
          }
        };
      }
      
      // 从Go后端获取session信息
      const sessionInfo = await this.getSessionInfoFromBackend(sessionId);
      if (!sessionInfo || !sessionInfo.is_session_valid) {
        return {
          success: false,
          error: 'Session不存在或无效'
        };
      }
      
      // 检查并发限制
      this.checkConcurrencyLimit();
      
      // 恢复session
      const result = await this.restoreSessionWithRetry(sessionInfo, 2); // 减少重试次数
      if (result.success) {
        console.log(`✅ 按需恢复Session成功: ${sessionId}`);
        
        const session = this.sessions.get(sessionId);
        return {
          success: true,
          message: 'Session恢复成功',
          data: {
            session_id: sessionId,
            status: 'active',
            client_info: await this.getClientInfoInternal(session.client)
          }
        };
      } else {
        console.log(`❌ 按需恢复Session失败: ${sessionId}`);
        return {
          success: false,
          error: result.error || 'Session恢复失败'
        };
      }
      
    } catch (error) {
      console.error(`❌ 按需恢复Session异常: ${sessionId}`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 从Go后端获取session信息
  async getSessionInfoFromBackend(sessionId) {
    try {
      const response = await fetch(getBackendApiUrl(`/api/whatsapp/sessions/${sessionId}/status`), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        console.error(`获取Session信息失败: ${sessionId}, HTTP ${response.status}`);
        return null;
      }
      
      const result = await response.json();
      if (result.success && result.data && result.data.session) {
        const session = result.data.session;
        return {
          session_id: session.session_id,
          status: session.status,
          phone_number: session.phone_number,
          tenant_id: session.tenant_id,
          is_session_valid: session.status === 'active' || (session.status === 'disconnected' && session.phone_number),
          created_at: session.created_at,
          last_used: session.updated_at
        };
      }
      
      return null;
    } catch (error) {
      console.error(`获取Session信息异常: ${sessionId}`, error.message);
      return null;
    }
  }

  // 获取服务状态
  getServiceStatus() {
    return {
      service_id: this.serviceId,
      active_sessions: Array.from(this.sessions.keys()),
      session_count: this.sessions.size,
      max_sessions: this.maxConcurrentSessions,
      process_pid: process.pid,
      uptime: process.uptime(),
      memory_usage: process.memoryUsage()
    };
  }
} 