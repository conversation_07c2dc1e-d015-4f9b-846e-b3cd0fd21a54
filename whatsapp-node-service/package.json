{"name": "whatsapp-node-service", "version": "1.0.0", "description": "WhatsApp Node.js服务，与Go后端集成", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "node test/test-basic.js"}, "keywords": ["whatsapp", "node", "api", "service"], "author": "Your Name", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.1.1", "p-limit": "^6.2.0", "path": "^0.12.7", "puppeteer": "^21.5.2", "qrcode": "^1.5.3", "whatsapp-web.js": "^1.23.0"}, "devDependencies": {"nodemon": "^3.0.1"}}