#!/bin/bash

# 多租户功能测试脚本
API_BASE_URL="http://localhost:8081/api"

echo "=== Hive SaaS 多租户功能测试 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local test_name="$1"
    local endpoint="$2"
    local method="${3:-GET}"
    local data="${4:-}"
    local expected_code="${5:-200}"
    
    echo -e "${BLUE}测试: ${test_name}${NC}"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $TOKEN" \
            -d "$data" \
            "$API_BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Authorization: Bearer $TOKEN" \
            "$API_BASE_URL$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "$expected_code" ]; then
        echo -e "${GREEN}✓ 通过${NC}"
        echo "响应: $body" | head -c 200
        echo ""
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
        echo ""
    fi
}

# 1. 测试服务状态
echo -e "${YELLOW}1. 检查服务状态${NC}"
if curl -s "$API_BASE_URL/health" > /dev/null; then
    echo -e "${GREEN}✓ 后端服务运行正常${NC}"
else
    echo -e "${RED}✗ 后端服务未运行${NC}"
    exit 1
fi
echo ""

# 2. 测试不同租户的用户登录
echo -e "${YELLOW}2. 测试不同租户的用户登录${NC}"

# 企业版租户用户登录
echo "测试企业版租户用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"admin_Hive SaaS","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    echo -e "${GREEN}✓ 企业版超级管理员登录成功${NC}"
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    USER_INFO=$(echo "$LOGIN_RESPONSE" | grep -o '"user_info":{[^}]*}')
    echo "用户信息: $USER_INFO"
else
    echo -e "${RED}✗ 企业版超级管理员登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
fi
echo ""

# 专业版租户用户登录
echo "测试专业版租户用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"manager_测试公司A","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    echo -e "${GREEN}✓ 专业版管理员登录成功${NC}"
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    USER_INFO=$(echo "$LOGIN_RESPONSE" | grep -o '"user_info":{[^}]*}')
    echo "用户信息: $USER_INFO"
else
    echo -e "${RED}✗ 专业版管理员登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
fi
echo ""

# 基础版租户用户登录
echo "测试基础版租户用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"manager_测试公司B","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    echo -e "${GREEN}✓ 基础版管理员登录成功${NC}"
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    USER_INFO=$(echo "$LOGIN_RESPONSE" | grep -o '"user_info":{[^}]*}')
    echo "用户信息: $USER_INFO"
else
    echo -e "${RED}✗ 基础版管理员登录失败${NC}"
    echo "响应: $LOGIN_RESPONSE"
fi
echo ""

# 3. 测试租户管理功能
echo -e "${YELLOW}3. 测试租户管理功能${NC}"

# 使用企业版超级管理员登录
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"admin_Hive SaaS","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✓ 获取到管理员Token${NC}"
    
    # 测试获取可访问租户
    test_api "获取可访问租户列表" "/tenant/accessible"
    
    # 测试获取当前租户信息
    test_api "获取当前租户信息" "/tenant/current"
    
    # 测试获取租户列表
    test_api "获取所有租户列表" "/tenants"
    
    # 测试租户切换
    echo -e "${BLUE}测试租户切换功能${NC}"
    SWITCH_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{"tenant_id":2,"reason":"功能测试切换"}' \
        "$API_BASE_URL/tenant/switch")
    
    if echo "$SWITCH_RESPONSE" | grep -q '"code":200'; then
        echo -e "${GREEN}✓ 租户切换成功${NC}"
        NEW_TOKEN=$(echo "$SWITCH_RESPONSE" | grep -o '"new_token":"[^"]*"' | cut -d'"' -f4)
        TOKEN="$NEW_TOKEN"
        
        # 验证切换后的租户信息
        test_api "验证切换后的租户信息" "/tenant/current"
    else
        echo -e "${RED}✗ 租户切换失败${NC}"
        echo "响应: $SWITCH_RESPONSE"
    fi
    
    # 测试获取切换历史
    test_api "获取租户切换历史" "/tenant/switch-history"
    
else
    echo -e "${RED}✗ 无法获取管理员Token${NC}"
fi
echo ""

# 4. 测试数据隔离
echo -e "${YELLOW}4. 测试数据隔离功能${NC}"

# 使用不同租户的用户登录并查看用户列表
echo "测试企业版租户用户列表..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"admin_Hive SaaS","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    test_api "企业版租户用户列表" "/users"
fi

echo "测试专业版租户用户列表..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"manager_测试公司A","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    test_api "专业版租户用户列表" "/users"
fi

echo "测试基础版租户用户列表..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"manager_测试公司B","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    test_api "基础版租户用户列表" "/users"
fi
echo ""

# 5. 测试权限控制
echo -e "${YELLOW}5. 测试权限控制功能${NC}"

# 测试不同角色的权限
echo "测试超级管理员权限..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"admin_Hive SaaS","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    test_api "超级管理员访问租户管理" "/tenant/accessible"
    test_api "超级管理员访问系统配置" "/system/config"
fi

echo "测试普通用户权限..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"user_Hive SaaS","password":"user123"}' \
    "$API_BASE_URL/auth/login")

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    test_api "普通用户访问用户列表" "/users"
    test_api "普通用户访问租户管理" "/tenant/accessible" "GET" "" "403"
fi
echo ""

# 6. 显示测试用户信息
echo -e "${YELLOW}6. 测试用户信息${NC}"
echo "企业版租户 (Hive SaaS):"
echo "  - 超级管理员: admin_Hive SaaS / admin123"
echo "  - 管理员: manager_Hive SaaS / admin123"
echo "  - 部门经理: manager_Hive SaaS / user123"
echo "  - 普通用户: user_Hive SaaS / user123"
echo "  - 访客: guest_Hive SaaS / guest123"
echo ""

echo "专业版租户 (测试公司A):"
echo "  - 管理员: manager_测试公司A / admin123"
echo "  - 部门经理: manager_测试公司A / user123"
echo "  - 普通用户: user_测试公司A / user123"
echo ""

echo "基础版租户 (测试公司B):"
echo "  - 管理员: manager_测试公司B / admin123"
echo "  - 普通用户: user_测试公司B / user123"
echo ""

echo "专业版租户 (创业公司C):"
echo "  - 管理员: manager_创业公司C / admin123"
echo "  - 部门经理: manager_创业公司C / user123"
echo "  - 普通用户: user_创业公司C / user123"
echo ""

echo "企业版租户 (企业集团D):"
echo "  - 超级管理员: admin_企业集团D / admin123"
echo "  - 管理员: manager_企业集团D / admin123"
echo "  - 部门经理: manager_企业集团D / user123"
echo "  - 普通用户: user_企业集团D / user123"
echo "  - 访客: guest_企业集团D / guest123"
echo ""

echo -e "${GREEN}=== 多租户功能测试完成 ===${NC}" 