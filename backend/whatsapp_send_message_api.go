package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
	MessageType string `json:"message_type" binding:"required"`
	Message     string `json:"message,omitempty"`
}

// SendMessageResponse 发送消息响应
type SendMessageResponse struct {
	MessageID string `json:"message_id"`
	Status    string `json:"status"`
	Timestamp string `json:"timestamp"`
}

// SendHistoryRecord 发送历史记录
type SendHistoryRecord struct {
	ID          uint      `json:"id"`
	AccountID   uint      `json:"account_id"`
	PhoneNumber string    `json:"phone_number"`
	MessageType string    `json:"message_type"`
	Content     string    `json:"content"`
	Status      string    `json:"status"`
	Error       string    `json:"error,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
}

// SendHistoryResponse 发送历史响应
type SendHistoryResponse struct {
	History []WhatsAppSendHistory `json:"history"`
	Total   int64                 `json:"total"`
}

// 发送消息API
func sendWhatsAppMessageAPI(c *gin.Context) {
	// 获取账号ID
	accountIDStr := c.Param("id")
	accountID, err := strconv.ParseUint(accountIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的账号ID",
		})
		return
	}

	// 检查账号是否存在
	var account WhatsAppAccount
	if err := db.Where("id = ?", accountID).First(&account).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "账号不存在",
		})
		return
	}

	// 检查账号连接状态
	if account.ConnectionStatus != "connected" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "账号未连接，无法发送消息",
		})
		return
	}

	// 解析表单数据
	phoneNumber := c.PostForm("phone_number")
	messageType := c.PostForm("message_type")
	message := c.PostForm("message")
	filePathParam := c.PostForm("file_path") // 用于图片消息的文件路径

	if phoneNumber == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "手机号不能为空",
		})
		return
	}

	if messageType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "消息类型不能为空",
		})
		return
	}

	// 处理不同类型的消息
	var messageContent string
	var filePath string

	switch messageType {
	case "text":
		if message == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "文本消息内容不能为空",
			})
			return
		}
		messageContent = message

	case "image":
		// 处理图片消息 - 支持两种方式：文件路径或文件上传
		if filePathParam != "" {
			// 使用已上传的文件路径
			filePath = filePathParam
			messageContent = message // 图片描述

			// 验证文件是否存在
			if _, err := os.Stat(filePath); os.IsNotExist(err) {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "图片文件不存在",
				})
				return
			}
		} else {
			// 处理文件上传
			file, header, err := c.Request.FormFile("file")
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "图片文件上传失败",
				})
				return
			}
			defer file.Close()

			// 创建上传目录（在storage目录）
			uploadDir := "../storage/uploads/whatsapp"
			if err := os.MkdirAll(uploadDir, 0755); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "创建上传目录失败",
				})
				return
			}

			// 生成文件名
			ext := filepath.Ext(header.Filename)
			filename := fmt.Sprintf("%d_%s%s", time.Now().Unix(), phoneNumber, ext)
			filePath = filepath.Join(uploadDir, filename)

			// 保存文件
			dst, err := os.Create(filePath)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "保存文件失败",
				})
				return
			}
			defer dst.Close()

			if _, err := io.Copy(dst, file); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "保存文件失败",
				})
				return
			}

			messageContent = header.Filename
		}

	case "video", "file":
		// 处理文件上传
		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "文件上传失败",
			})
			return
		}
		defer file.Close()

		// 创建上传目录（在storage目录）
		uploadDir := "../storage/uploads/whatsapp"
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "创建上传目录失败",
			})
			return
		}

		// 生成文件名
		ext := filepath.Ext(header.Filename)
		filename := fmt.Sprintf("%d_%s%s", time.Now().Unix(), phoneNumber, ext)
		filePath = filepath.Join(uploadDir, filename)

		// 保存文件
		dst, err := os.Create(filePath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "保存文件失败",
			})
			return
		}
		defer dst.Close()

		if _, err := io.Copy(dst, file); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "保存文件失败",
			})
			return
		}

		messageContent = header.Filename

	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "不支持的消息类型",
		})
		return
	}

	// 调用WhatsApp服务发送消息
	success, messageID, errorMsg := sendMessageViaWhatsAppService(accountID, phoneNumber, messageType, messageContent, filePath)

	// 记录发送历史
	historyRecord := WhatsAppSendHistory{
		AccountID:   uint(accountID),
		PhoneNumber: phoneNumber,
		MessageType: messageType,
		Content:     messageContent,
		Status:      "success",
		Timestamp:   time.Now(),
	}

	if !success {
		historyRecord.Status = "failed"
		historyRecord.Error = errorMsg
	}

	// 保存到数据库
	if err := db.Create(&historyRecord).Error; err != nil {
		fmt.Printf("保存发送历史失败: %v\n", err)
	}

	// 返回响应
	if success {
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"message": "消息发送成功",
			"data": SendMessageResponse{
				MessageID: messageID,
				Status:    "success",
				Timestamp: time.Now().Format(time.RFC3339),
			},
		})
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": errorMsg,
		})
	}
}

// 获取发送历史API
func getSendHistoryAPI(c *gin.Context) {
	// 获取账号ID
	accountIDStr := c.Param("id")
	accountID, err := strconv.ParseUint(accountIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的账号ID",
		})
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	offset := (page - 1) * size

	// 查询发送历史
	var records []WhatsAppSendHistory
	var total int64

	// 查询总数
	if err := db.Table("whats_app_send_histories").Where("account_id = ?", accountID).Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询总数失败",
		})
		return
	}

	// 查询记录
	if err := db.Table("whats_app_send_histories").Where("account_id = ?", accountID).
		Order("created_at DESC").
		Offset(offset).
		Limit(size).
		Find(&records).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询发送历史失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"message": "success",
		"data": SendHistoryResponse{
			History: records,
			Total:   total,
		},
	})
}

// 通过WhatsApp服务发送消息
func sendMessageViaWhatsAppService(accountID uint64, phoneNumber, messageType, messageContent, filePath string) (bool, string, string) {
	// 构建请求数据
	requestData := map[string]interface{}{
		"account_id":    accountID,
		"phone_number":  phoneNumber,
		"message_type":  messageType,
		"message":       messageContent,
		"file_path":     filePath,
	}

	// 发送请求到WhatsApp服务
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return false, "", "序列化请求数据失败"
	}

	resp, err := http.Post("http://localhost:3000/api/whatsapp/send-message", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return false, "", "连接WhatsApp服务失败"
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "", "读取响应失败"
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return false, "", "解析响应失败"
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		errorMsg := "发送失败"
		if msg, ok := response["message"].(string); ok {
			errorMsg = msg
		}
		return false, "", errorMsg
	}

	// 提取消息ID
	messageID := ""
	if data, ok := response["data"].(map[string]interface{}); ok {
		if id, ok := data["message_id"].(string); ok {
			messageID = id
		}
	}

	return true, messageID, ""
} 