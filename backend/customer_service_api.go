package main

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// 获取客服列表
func getCustomerServiceList(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 构建查询：获取具有客服角色的用户
	var users []User
	query := db.Preload("Tenant").Preload("Roles")

	// 权限控制：系统管理员可以查看所有，租户管理员只能查看自己租户的
	if user.UserType == "system" && user.Role == "super_admin" {
		// 系统管理员可以查看所有客服
		if tenantID := c.Query("tenant_id"); tenantID != "" {
			if tid, err := strconv.ParseUint(tenantID, 10, 32); err == nil {
				query = query.Where("tenant_id = ?", uint(tid))
			}
		}
	} else {
		// 租户管理员只能查看自己租户的客服
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法访问其他租户的客服")
			return
		}
		query = query.Where("tenant_id = ?", *user.TenantID)
	}

	// 只获取具有客服角色的用户
	query = query.Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("roles.name = ?", "customer_service")

	// 搜索过滤
	if account := c.Query("service_account"); account != "" {
		query = query.Where("users.username LIKE ?", "%"+account+"%")
	}
	if nickname := c.Query("service_nickname"); nickname != "" {
		query = query.Where("users.real_name LIKE ?", "%"+nickname+"%")
	}
	if status := c.Query("status"); status != "" {
		query = query.Where("users.status = ?", status == "active")
	}

	if err := query.Find(&users).Error; err != nil {
		errorResponse(c, 500, "获取客服列表失败")
		return
	}

	// 获取客服档案信息
	var services []map[string]interface{}
	for _, u := range users {
		var profile CustomerServiceProfile
		db.Preload("Group").Where("user_id = ?", u.ID).First(&profile)

		service := map[string]interface{}{
			"id":                      u.ID,
			"service_account":         u.Username,
			"service_nickname":        u.RealName,
			"email":                   u.Email,
			"phone":                   u.Phone,
			"status":                  getStatusFromUser(u.Status),
			"tenant_id":               u.TenantID,
			"tenant":                  u.Tenant,
			"service_type":            profile.ServiceType,
			"group_id":                profile.GroupID,
			"group":                   profile.Group,
			"daily_assign_limit":      profile.DailyAssignLimit,
			"is_assign_new_session":   profile.IsAssignNewSession,
			"hide_customer_ws_number": profile.HideCustomerWSNumber,
			"is_customer_group_send":  profile.IsCustomerGroupSend,
			"disabled_at":             profile.DisabledAt,
			"disabled_reason":         profile.DisabledReason,
			"created_at":              u.CreatedAt,
			"updated_at":              u.UpdatedAt,
		}
		services = append(services, service)
	}

	successResponse(c, gin.H{
		"services": services,
		"total":    len(services),
	})
}

// 辅助函数：从用户状态转换为客服状态
func getStatusFromUser(userStatus bool) string {
	if userStatus {
		return "active"
	}
	return "disabled"
}

// 根据ID获取客服详情
func getCustomerServiceById(c *gin.Context) {
	id := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var serviceUser User
	if err := db.Preload("Tenant").Preload("Roles").First(&serviceUser, id).Error; err != nil {
		errorResponse(c, 404, "客服不存在")
		return
	}

	// 检查是否为客服角色
	isCustomerService := false
	for _, role := range serviceUser.Roles {
		if role.Name == "customer_service" {
			isCustomerService = true
			break
		}
	}
	if !isCustomerService {
		errorResponse(c, 404, "用户不是客服")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || serviceUser.TenantID == nil || *serviceUser.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权访问此客服")
			return
		}
	}

	// 获取客服档案
	var profile CustomerServiceProfile
	db.Preload("Group").Where("user_id = ?", serviceUser.ID).First(&profile)

	service := map[string]interface{}{
		"id":                      serviceUser.ID,
		"service_account":         serviceUser.Username,
		"service_nickname":        serviceUser.RealName,
		"email":                   serviceUser.Email,
		"phone":                   serviceUser.Phone,
		"status":                  getStatusFromUser(serviceUser.Status),
		"tenant_id":               serviceUser.TenantID,
		"tenant":                  serviceUser.Tenant,
		"service_type":            profile.ServiceType,
		"group_id":                profile.GroupID,
		"group":                   profile.Group,
		"daily_assign_limit":      profile.DailyAssignLimit,
		"is_assign_new_session":   profile.IsAssignNewSession,
		"hide_customer_ws_number": profile.HideCustomerWSNumber,
		"is_customer_group_send":  profile.IsCustomerGroupSend,
		"disabled_at":             profile.DisabledAt,
		"disabled_reason":         profile.DisabledReason,
		"created_at":              serviceUser.CreatedAt,
		"updated_at":              serviceUser.UpdatedAt,
	}

	successResponse(c, service)
}

// 创建客服
func createCustomerService(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var req struct {
		ServiceAccount        string `json:"service_account" binding:"required"`
		ServiceNickname       string `json:"service_nickname" binding:"required"`
		Email                 string `json:"email" binding:"required,email"`
		Phone                 string `json:"phone"`
		Password              string `json:"password" binding:"required,min=6"`
		TenantID              uint   `json:"tenant_id"`
		ServiceType           string `json:"service_type"`
		GroupID               *uint  `json:"group_id"`
		DailyAssignLimit      int    `json:"daily_assign_limit"`
		IsAssignNewSession    bool   `json:"is_assign_new_session"`
		HideCustomerWSNumber  bool   `json:"hide_customer_ws_number"`
		IsCustomerGroupSend   bool   `json:"is_customer_group_send"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 设置租户ID
	var tenantID uint
	if user.UserType == "system" && user.Role == "super_admin" {
		// 系统管理员需要指定租户ID
		if req.TenantID == 0 {
			errorResponse(c, 400, "系统管理员创建客服时必须指定租户ID")
			return
		}
		tenantID = req.TenantID
	} else {
		// 租户管理员只能在自己的租户中创建客服
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法在其他租户中创建客服")
			return
		}
		tenantID = *user.TenantID
	}

	// 检查用户名是否已存在
	var existingUser User
	if err := db.Where("username = ?", req.ServiceAccount).First(&existingUser).Error; err == nil {
		errorResponse(c, 400, "用户名已存在")
		return
	}

	// 检查邮箱是否已存在
	if err := db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		errorResponse(c, 400, "邮箱已存在")
		return
	}

	// 开始事务
	tx := db.Begin()

	// 创建用户
	newUser := User{
		TenantID: &tenantID,
		Username: req.ServiceAccount,
		Email:    req.Email,
		Password: req.Password, // 注意：实际应用中需要加密密码
		RealName: req.ServiceNickname,
		Phone:    req.Phone,
		Status:   true,
		UserType: "tenant",
		Role:     "user", // 保持兼容性
	}

	if err := tx.Create(&newUser).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "创建用户失败")
		return
	}

	// 获取客服角色
	var customerServiceRole Role
	if err := tx.Where("tenant_id = ? AND name = ?", tenantID, "customer_service").First(&customerServiceRole).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "客服角色不存在")
		return
	}

	// 分配客服角色
	userRole := UserRole{
		UserID: newUser.ID,
		RoleID: customerServiceRole.ID,
	}
	if err := tx.Create(&userRole).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "分配角色失败")
		return
	}

	// 创建客服档案
	profile := CustomerServiceProfile{
		UserID:                newUser.ID,
		GroupID:               req.GroupID,
		ServiceType:           req.ServiceType,
		DailyAssignLimit:      req.DailyAssignLimit,
		IsAssignNewSession:    req.IsAssignNewSession,
		HideCustomerWSNumber:  req.HideCustomerWSNumber,
		IsCustomerGroupSend:   req.IsCustomerGroupSend,
	}

	// 设置默认值
	if profile.ServiceType == "" {
		profile.ServiceType = "regular"
	}
	if profile.DailyAssignLimit == 0 {
		profile.DailyAssignLimit = 50
	}

	if err := tx.Create(&profile).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "创建客服档案失败")
		return
	}

	// 提交事务
	tx.Commit()

	// 更新分组的客服数量
	if profile.GroupID != nil {
		updateGroupServiceCount(*profile.GroupID)
	}

	// 返回创建的客服信息
	service := map[string]interface{}{
		"id":                      newUser.ID,
		"service_account":         newUser.Username,
		"service_nickname":        newUser.RealName,
		"email":                   newUser.Email,
		"phone":                   newUser.Phone,
		"status":                  "active",
		"tenant_id":               newUser.TenantID,
		"service_type":            profile.ServiceType,
		"group_id":                profile.GroupID,
		"daily_assign_limit":      profile.DailyAssignLimit,
		"is_assign_new_session":   profile.IsAssignNewSession,
		"hide_customer_ws_number": profile.HideCustomerWSNumber,
		"is_customer_group_send":  profile.IsCustomerGroupSend,
		"created_at":              newUser.CreatedAt,
		"updated_at":              newUser.UpdatedAt,
	}

	successResponse(c, service)
}

// 更新客服
func updateCustomerService(c *gin.Context) {
	id := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var serviceUser User
	if err := db.First(&serviceUser, id).Error; err != nil {
		errorResponse(c, 404, "客服不存在")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || serviceUser.TenantID == nil || *serviceUser.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权修改此客服")
			return
		}
	}

	var req struct {
		ServiceNickname       string `json:"service_nickname"`
		Email                 string `json:"email"`
		Phone                 string `json:"phone"`
		Status                string `json:"status"`
		ServiceType           string `json:"service_type"`
		GroupID               *uint  `json:"group_id"`
		DailyAssignLimit      int    `json:"daily_assign_limit"`
		IsAssignNewSession    bool   `json:"is_assign_new_session"`
		HideCustomerWSNumber  bool   `json:"hide_customer_ws_number"`
		IsCustomerGroupSend   bool   `json:"is_customer_group_send"`
		DisabledReason        string `json:"disabled_reason"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取客服档案
	var profile CustomerServiceProfile
	if err := db.Where("user_id = ?", serviceUser.ID).First(&profile).Error; err != nil {
		errorResponse(c, 404, "客服档案不存在")
		return
	}

	// 记录原分组ID
	oldGroupID := profile.GroupID

	// 开始事务
	tx := db.Begin()

	// 更新用户信息
	if req.ServiceNickname != "" {
		serviceUser.RealName = req.ServiceNickname
	}
	if req.Email != "" {
		serviceUser.Email = req.Email
	}
	if req.Phone != "" {
		serviceUser.Phone = req.Phone
	}
	if req.Status != "" {
		serviceUser.Status = req.Status == "active"
	}

	if err := tx.Save(&serviceUser).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "更新用户信息失败")
		return
	}

	// 更新客服档案
	if req.ServiceType != "" {
		profile.ServiceType = req.ServiceType
	}
	if req.GroupID != nil {
		profile.GroupID = req.GroupID
	}
	if req.DailyAssignLimit > 0 {
		profile.DailyAssignLimit = req.DailyAssignLimit
	}
	profile.IsAssignNewSession = req.IsAssignNewSession
	profile.HideCustomerWSNumber = req.HideCustomerWSNumber
	profile.IsCustomerGroupSend = req.IsCustomerGroupSend

	// 如果状态改为禁用，记录禁用时间和原因
	if req.Status == "disabled" {
		now := time.Now()
		profile.DisabledAt = &now
		profile.DisabledReason = req.DisabledReason
	} else if req.Status == "active" {
		profile.DisabledAt = nil
		profile.DisabledReason = ""
	}

	if err := tx.Save(&profile).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "更新客服档案失败")
		return
	}

	// 提交事务
	tx.Commit()

	// 更新分组的客服数量
	if oldGroupID != nil {
		updateGroupServiceCount(*oldGroupID)
	}
	if profile.GroupID != nil && (oldGroupID == nil || *oldGroupID != *profile.GroupID) {
		updateGroupServiceCount(*profile.GroupID)
	}

	// 返回更新后的客服信息
	service := map[string]interface{}{
		"id":                      serviceUser.ID,
		"service_account":         serviceUser.Username,
		"service_nickname":        serviceUser.RealName,
		"email":                   serviceUser.Email,
		"phone":                   serviceUser.Phone,
		"status":                  getStatusFromUser(serviceUser.Status),
		"tenant_id":               serviceUser.TenantID,
		"service_type":            profile.ServiceType,
		"group_id":                profile.GroupID,
		"daily_assign_limit":      profile.DailyAssignLimit,
		"is_assign_new_session":   profile.IsAssignNewSession,
		"hide_customer_ws_number": profile.HideCustomerWSNumber,
		"is_customer_group_send":  profile.IsCustomerGroupSend,
		"disabled_at":             profile.DisabledAt,
		"disabled_reason":         profile.DisabledReason,
		"updated_at":              serviceUser.UpdatedAt,
	}

	successResponse(c, service)
}

// 删除客服
func deleteCustomerService(c *gin.Context) {
	id := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var serviceUser User
	if err := db.First(&serviceUser, id).Error; err != nil {
		errorResponse(c, 404, "客服不存在")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || serviceUser.TenantID == nil || *serviceUser.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权删除此客服")
			return
		}
	}

	// 获取客服档案
	var profile CustomerServiceProfile
	db.Where("user_id = ?", serviceUser.ID).First(&profile)
	groupID := profile.GroupID

	// 开始事务
	tx := db.Begin()

	// 删除客服档案
	if err := tx.Where("user_id = ?", serviceUser.ID).Delete(&CustomerServiceProfile{}).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "删除客服档案失败")
		return
	}

	// 删除用户角色关联
	if err := tx.Where("user_id = ?", serviceUser.ID).Delete(&UserRole{}).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "删除用户角色失败")
		return
	}

	// 删除用户
	if err := tx.Delete(&serviceUser).Error; err != nil {
		tx.Rollback()
		errorResponse(c, 500, "删除用户失败")
		return
	}

	// 提交事务
	tx.Commit()

	// 更新分组的客服数量
	if groupID != nil {
		updateGroupServiceCount(*groupID)
	}

	successResponse(c, gin.H{"message": "客服删除成功"})
}

// 批量操作客服
func batchUpdateCustomerServices(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var req struct {
		ServiceIDs []uint `json:"service_ids" binding:"required"`
		Action     string `json:"action" binding:"required"`
		Data       map[string]interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 权限检查：确保所有客服都属于当前用户的租户
	var users []User
	query := db.Where("id IN ?", req.ServiceIDs)
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法操作其他租户的客服")
			return
		}
		query = query.Where("tenant_id = ?", *user.TenantID)
	}

	if err := query.Find(&users).Error; err != nil {
		errorResponse(c, 500, "查询客服失败")
		return
	}

	if len(users) != len(req.ServiceIDs) {
		errorResponse(c, 403, "部分客服不存在或无权操作")
		return
	}

	// 执行批量操作
	switch req.Action {
	case "enable":
		// 更新用户状态
		if err := db.Model(&User{}).Where("id IN ?", req.ServiceIDs).Update("status", true).Error; err != nil {
			errorResponse(c, 500, "批量启用失败")
			return
		}
		// 更新客服档案
		db.Model(&CustomerServiceProfile{}).Where("user_id IN ?", req.ServiceIDs).Updates(map[string]interface{}{
			"disabled_at": nil,
			"disabled_reason": "",
		})
	case "disable":
		// 更新用户状态
		if err := db.Model(&User{}).Where("id IN ?", req.ServiceIDs).Update("status", false).Error; err != nil {
			errorResponse(c, 500, "批量禁用失败")
			return
		}
		// 更新客服档案
		now := time.Now()
		updates := map[string]interface{}{
			"disabled_at": &now,
		}
		if reason, ok := req.Data["reason"].(string); ok {
			updates["disabled_reason"] = reason
		}
		db.Model(&CustomerServiceProfile{}).Where("user_id IN ?", req.ServiceIDs).Updates(updates)
	case "set_daily_limit":
		if limit, ok := req.Data["daily_assign_limit"].(float64); ok {
			if err := db.Model(&CustomerServiceProfile{}).Where("user_id IN ?", req.ServiceIDs).Update("daily_assign_limit", int(limit)).Error; err != nil {
				errorResponse(c, 500, "批量设置分配上限失败")
				return
			}
		}
	case "set_assign_session":
		if assign, ok := req.Data["is_assign_new_session"].(bool); ok {
			if err := db.Model(&CustomerServiceProfile{}).Where("user_id IN ?", req.ServiceIDs).Update("is_assign_new_session", assign).Error; err != nil {
				errorResponse(c, 500, "批量设置分配会话失败")
				return
			}
		}
	case "move_to_group":
		if groupID, ok := req.Data["group_id"].(float64); ok {
			if err := db.Model(&CustomerServiceProfile{}).Where("user_id IN ?", req.ServiceIDs).Update("group_id", uint(groupID)).Error; err != nil {
				errorResponse(c, 500, "批量移动分组失败")
				return
			}
		}
	default:
		errorResponse(c, 400, "不支持的操作类型")
		return
	}

	successResponse(c, gin.H{"message": fmt.Sprintf("成功更新 %d 个客服", len(req.ServiceIDs))})
}

// 更新分组的客服数量
func updateGroupServiceCount(groupID uint) {
	var count int64
	db.Model(&CustomerServiceProfile{}).Where("group_id = ?", groupID).Count(&count)
	db.Model(&CustomerServiceGroup{}).Where("id = ?", groupID).Update("service_count", count)
}
