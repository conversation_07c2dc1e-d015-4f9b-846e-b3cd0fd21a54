package main

import (
	"fmt"
	"strconv"
	"time"
	"github.com/gin-gonic/gin"
)

// 获取租户列表
func getTenantList(c *gin.Context) {
	var tenants []Tenant
	result := db.Find(&tenants)
	if result.Error != nil {
		errorResponse(c, 500, "Failed to get tenant list")
		return
	}

	successResponse(c, tenants)
}

// 根据ID获取租户
func getTenantById(c *gin.Context) {
	id := c.Param("id")
	tenantID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		errorResponse(c, 400, "Invalid tenant ID")
		return
	}

	var tenant Tenant
	result := db.First(&tenant, tenantID)
	if result.Error != nil {
		errorResponse(c, 404, "Tenant not found")
		return
	}

	successResponse(c, tenant)
}

// 创建租户
func createTenant(c *gin.Context) {
	var tenant Tenant
	if err := c.Should<PERSON>indJ<PERSON>N(&tenant); err != nil {
		errorResponse(c, 400, "Invalid request data")
		return
	}

	// 验证必填字段
	if tenant.Name == "" {
		errorResponse(c, 400, "Tenant name is required")
		return
	}

	// 检查租户名称是否已存在
	var existingTenant Tenant
	if err := db.Where("name = ?", tenant.Name).First(&existingTenant).Error; err == nil {
		errorResponse(c, 400, "Tenant name already exists")
		return
	}

	// 设置默认值
	if tenant.PlanType == "" {
		tenant.PlanType = "basic"
	}
	if tenant.Status == "" {
		tenant.Status = "active"
	}
	if tenant.MaxAccounts == 0 {
		tenant.MaxAccounts = 1
	}
	if tenant.MaxStorage == 0 {
		tenant.MaxStorage = ********** // 1GB
	}

	result := db.Create(&tenant)
	if result.Error != nil {
		errorResponse(c, 500, "Failed to create tenant")
		return
	}

	successResponse(c, tenant)
}

// 更新租户
func updateTenant(c *gin.Context) {
	id := c.Param("id")
	tenantID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		errorResponse(c, 400, "Invalid tenant ID")
		return
	}

	var tenant Tenant
	if err := db.First(&tenant, tenantID).Error; err != nil {
		errorResponse(c, 404, "Tenant not found")
		return
	}

	var updateData Tenant
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "Invalid request data")
		return
	}

	// 更新字段
	if updateData.Name != "" {
		// 检查名称是否已被其他租户使用
		var existingTenant Tenant
		if err := db.Where("name = ? AND id != ?", updateData.Name, tenantID).First(&existingTenant).Error; err == nil {
			errorResponse(c, 400, "Tenant name already exists")
			return
		}
		tenant.Name = updateData.Name
	}
	if updateData.Domain != "" {
		tenant.Domain = updateData.Domain
	}
	if updateData.PlanType != "" {
		tenant.PlanType = updateData.PlanType
	}
	if updateData.Status != "" {
		tenant.Status = updateData.Status
	}
	if updateData.MaxAccounts > 0 {
		tenant.MaxAccounts = updateData.MaxAccounts
	}
	if updateData.MaxStorage > 0 {
		tenant.MaxStorage = updateData.MaxStorage
	}

	result := db.Save(&tenant)
	if result.Error != nil {
		errorResponse(c, 500, "Failed to update tenant")
		return
	}

	successResponse(c, tenant)
}

// 删除租户
func deleteTenant(c *gin.Context) {
	id := c.Param("id")
	tenantID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		errorResponse(c, 400, "Invalid tenant ID")
		return
	}

	var tenant Tenant
	if err := db.First(&tenant, tenantID).Error; err != nil {
		errorResponse(c, 404, "Tenant not found")
		return
	}

	// 检查是否有用户关联到此租户
	var userCount int64
	db.Model(&User{}).Where("tenant_id = ?", tenantID).Count(&userCount)
	if userCount > 0 {
		errorResponse(c, 400, "Cannot delete tenant with existing users")
		return
	}

	result := db.Delete(&tenant)
	if result.Error != nil {
		errorResponse(c, 500, "Failed to delete tenant")
		return
	}

	successResponse(c, gin.H{"message": "Tenant deleted successfully"})
}

// 获取当前租户信息
func getCurrentTenant(c *gin.Context) {
	// 从JWT token中获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "User not authenticated")
		return
	}

	var user User
	if err := db.Preload("Tenant").First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "User not found")
		return
	}

	// 系统级超级管理员可能没有租户ID
	if user.TenantID == nil {
		// 返回特殊的系统租户信息
		systemTenant := map[string]interface{}{
			"id":          0,
			"name":        "系统管理",
			"domain":      "system",
			"plan_type":   "system",
			"status":      "active",
			"is_system":   true,
			"description": "系统级超级管理员，拥有所有租户的管理权限",
		}
		successResponse(c, systemTenant)
		return
	}

	successResponse(c, user.Tenant)
}

// 管理员切换租户
func switchTenant(c *gin.Context) {
	// 检查是否为管理员或超级管理员
	role, exists := c.Get("role")
	if !exists || (role != "admin" && role != "super_admin") {
		errorResponse(c, 403, "需要管理员权限")
		return
	}

	var req struct {
		TenantID uint   `json:"tenant_id"`
		Reason   string `json:"reason"` // 切换原因
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取当前用户信息
	userID, _ := c.Get("user_id")
	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 记录租户切换历史
	var fromTenantID uint
	if user.TenantID != nil {
		fromTenantID = *user.TenantID
	}

	// 处理切换到系统管理的情况（tenantId = 0）
	if req.TenantID == 0 {
		// 只有系统级超级管理员可以回到系统管理
		if user.UserType != "system" || user.Role != "super_admin" {
			errorResponse(c, 403, "只有系统级超级管理员可以回到系统管理")
			return
		}
		
		// 清除用户的租户ID，回到系统管理
		user.TenantID = nil
		if err := db.Save(&user).Error; err != nil {
			errorResponse(c, 500, "切换租户失败")
			return
		}
		
		// 记录切换历史
		history := TenantSwitchHistory{
			UserID:       user.ID,
			FromTenantID: fromTenantID,
			ToTenantID:   0, // 系统管理
			SwitchTime:   time.Now(),
			IPAddress:    c.ClientIP(),
			UserAgent:    c.GetHeader("User-Agent"),
			Reason:       req.Reason,
		}
		db.Create(&history)

		// 生成新的JWT token（不包含租户ID）
		token, err := generateToken(user)
		if err != nil {
			errorResponse(c, 500, "生成新token失败")
			return
		}

		// 返回系统管理信息
		systemTenant := map[string]interface{}{
			"id":          0,
			"name":        "系统管理",
			"domain":      "system",
			"plan_type":   "system",
			"status":      "active",
			"is_system":   true,
			"description": "系统级超级管理员，拥有所有租户的管理权限",
		}

		successResponse(c, gin.H{
			"message":     "切换到系统管理成功",
			"new_token":   token,
			"tenant_info": systemTenant,
			"switch_time": history.SwitchTime,
		})
		return
	}

	// 验证租户是否存在
	var tenant Tenant
	if err := db.First(&tenant, req.TenantID).Error; err != nil {
		errorResponse(c, 404, "租户不存在")
		return
	}

	// 验证租户状态
	if tenant.Status != "active" {
		errorResponse(c, 400, "租户状态异常，无法切换")
		return
	}
	
	// 系统级超级管理员可以切换到任何租户，不需要检查角色
	if user.UserType == "system" && user.Role == "super_admin" {
		// 系统级超级管理员保持原有角色，不改变
		user.TenantID = &req.TenantID
	} else {
		// 租户级管理员只能管理自己的租户，不能切换到其他租户
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法访问其他租户")
			return
		}
		
		// 检查是否尝试切换到其他租户
		fmt.Printf("switchTenant: user.TenantID=%d, req.TenantID=%d\n", *user.TenantID, req.TenantID)
		if *user.TenantID != req.TenantID {
			fmt.Printf("switchTenant: permission denied, tenant mismatch\n")
			errorResponse(c, 403, "租户级管理员只能管理自己的租户")
			return
		}
		
		// 租户级管理员已经在自己的租户中，不需要切换
		successResponse(c, gin.H{
			"message":     "已在当前租户中",
			"tenant_info": tenant,
		})
		return
	}
	
	if err := db.Save(&user).Error; err != nil {
		errorResponse(c, 500, "切换租户失败")
		return
	}
	
	history := TenantSwitchHistory{
		UserID:       user.ID,
		FromTenantID: fromTenantID,
		ToTenantID:   req.TenantID,
		SwitchTime:   time.Now(),
		IPAddress:    c.ClientIP(),
		UserAgent:    c.GetHeader("User-Agent"),
		Reason:       req.Reason,
	}
	db.Create(&history)

	// 生成新的JWT token（包含新的租户ID）
	token, err := generateToken(user)
	if err != nil {
		errorResponse(c, 500, "生成新token失败")
		return
	}

	successResponse(c, gin.H{
		"message":     "租户切换成功",
		"new_token":   token,
		"tenant_info": tenant,
		"switch_time": history.SwitchTime,
	})
}

// 获取管理员可访问的租户列表
func getAccessibleTenants(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var tenants []Tenant
	
	// 只有系统级超级管理员可以访问所有租户
	if user.UserType == "system" && user.Role == "super_admin" {
		result := db.Where("status = ?", "active").Find(&tenants)
		if result.Error != nil {
			errorResponse(c, 500, "获取租户列表失败")
			return
		}
	} else {
		// 租户级管理员只能访问自己的租户
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法访问其他租户")
			return
		}
		
		var tenant Tenant
		if err := db.First(&tenant, user.TenantID).Error; err != nil {
			errorResponse(c, 404, "当前租户不存在")
			return
		}
		
		tenants = []Tenant{tenant}
	}

	successResponse(c, tenants)
}

// 获取当前用户的租户信息（包含切换历史）
func getCurrentTenantWithHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.Preload("Tenant").First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 获取所有可访问的租户（仅系统级超级管理员）
	var accessibleTenants []Tenant
	if user.UserType == "system" && user.Role == "super_admin" {
		db.Where("status = ?", "active").Find(&accessibleTenants)
	} else if user.TenantID != nil {
		// 租户级用户只能访问自己的租户
		var tenant Tenant
		if err := db.First(&tenant, user.TenantID).Error; err == nil {
			accessibleTenants = []Tenant{tenant}
		}
	}

	// 系统级超级管理员可能没有租户ID
	var currentTenant interface{}
	if user.TenantID == nil {
		// 返回特殊的系统租户信息
		currentTenant = map[string]interface{}{
			"id":          0,
			"name":        "系统管理",
			"domain":      "system",
			"plan_type":   "system",
			"status":      "active",
			"is_system":   true,
			"description": "系统级超级管理员，拥有所有租户的管理权限",
		}
	} else {
		currentTenant = user.Tenant
	}

	successResponse(c, gin.H{
		"current_tenant":      currentTenant,
		"accessible_tenants":  accessibleTenants,
		"can_switch_tenant":   user.Role == "super_admin",
	})
}

// 获取租户切换历史
func getTenantSwitchHistory(c *gin.Context) {
	// 检查是否为管理员
	role, exists := c.Get("role")
	if !exists || (role != "admin" && role != "super_admin") {
		errorResponse(c, 403, "需要管理员权限")
		return
	}

	userID, _ := c.Get("user_id")
	
	var history []TenantSwitchHistory
	result := db.Preload("FromTenant").Preload("ToTenant").
		Where("user_id = ?", userID).
		Order("switch_time DESC").
		Limit(50).
		Find(&history)
	
	if result.Error != nil {
		errorResponse(c, 500, "获取切换历史失败")
		return
	}

	successResponse(c, history)
}

// 获取所有租户切换历史（超级管理员）
func getAllTenantSwitchHistory(c *gin.Context) {
	// 检查是否为超级管理员
	role, exists := c.Get("role")
	if !exists || role != "super_admin" {
		errorResponse(c, 403, "需要超级管理员权限")
		return
	}

	var history []TenantSwitchHistory
	result := db.Preload("User").Preload("FromTenant").Preload("ToTenant").
		Order("switch_time DESC").
		Limit(100).
		Find(&history)
	
	if result.Error != nil {
		errorResponse(c, 500, "获取切换历史失败")
		return
	}

	successResponse(c, history)
} 