package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"errors"
)

// Node服务配置
const (
	NodeServiceURL = "http://localhost:3000"
	NodeServiceTimeout = 60 * time.Second // 增加超时时间到60秒
)

// Node服务请求结构
type NodeServiceRequest struct {
	SessionID string `json:"session_id"`
	PhoneNumber string `json:"phone_number"`
	TenantID uint `json:"tenant_id"`
	Action string `json:"action"` // create, connect, disconnect, send_message, get_info
	Data map[string]interface{} `json:"data,omitempty"`
}

// Node服务响应结构
type NodeServiceResponse struct {
	Success bool `json:"success"`
	Message string `json:"message"`
	Data map[string]interface{} `json:"data,omitempty"`
	Error string `json:"error,omitempty"`
}

// 调用Node服务
func callNodeService(req NodeServiceRequest) (*NodeServiceResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	client := &http.Client{
		Timeout: NodeServiceTimeout,
	}

	resp, err := client.Post(
		fmt.Sprintf("%s/api/whatsapp/action", NodeServiceURL),
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("调用Node服务失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var nodeResp NodeServiceResponse
	if err := json.Unmarshal(body, &nodeResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &nodeResp, nil
}

// 创建WhatsApp Session
func createWhatsAppSession(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以创建WhatsApp Session")
		return
	}

	var req struct {
		AccountName string `json:"account_name" binding:"required"`
		GroupID     *uint  `json:"group_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 生成Session ID (Node.js的LocalAuth会自动添加session-前缀，所以这里不加)
	sessionID := fmt.Sprintf("%d-%d", *user.TenantID, time.Now().Unix())

	// 调用Node服务创建Session
	nodeReq := NodeServiceRequest{
		SessionID:   sessionID,
		PhoneNumber: "", // 暂时为空，登录后获取
		TenantID:    *user.TenantID,
		Action:      "create",
		Data: map[string]interface{}{
			"account_name": req.AccountName,
		},
	}
	
	// 调试信息
	fmt.Printf("发送到Node服务的请求: SessionID=%s, PhoneNumber=%s\n", nodeReq.SessionID, nodeReq.PhoneNumber)

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("创建Session失败: %v", err))
		return
	}

	if !nodeResp.Success {
		errorResponse(c, 500, fmt.Sprintf("Node服务错误: %s", nodeResp.Error))
		return
	}

	// 创建Session记录
	now := time.Now()
	expiresAt := now.Add(10 * time.Minute) // 10分钟过期
	
	// 安全地获取二维码数据
	var qrCode string
	if qrData, ok := nodeResp.Data["qr_code"]; ok && qrData != nil {
		if qrStr, ok := qrData.(string); ok {
			qrCode = qrStr
		}
	}
	
	session := WhatsAppSession{
		TenantID:        *user.TenantID,
		SessionID:       sessionID,
		Status:          "pending",
		QRCode:          qrCode,
		QRCodeExpiresAt: &expiresAt,
		CreatedAt:       now,
		UpdatedAt:       now,
		ExpiresAt:       &expiresAt,
	}

	if err := db.Create(&session).Error; err != nil {
		errorResponse(c, 500, "创建WhatsApp Session失败")
		return
	}

	successResponse(c, gin.H{
		"session": session,
		"message": "WhatsApp Session创建成功，请扫描二维码登录",
	})
}

// 检查Session状态
func checkSessionStatus(c *gin.Context) {
	sessionID := c.Param("session_id")
	
	var session WhatsAppSession
	if err := db.Where("session_id = ?", sessionID).First(&session).Error; err != nil {
		errorResponse(c, 404, "Session不存在")
		return
	}

	// 检查Session是否过期
	if session.ExpiresAt != nil && time.Now().After(*session.ExpiresAt) {
		session.Status = "expired"
		db.Save(&session)
	}

	successResponse(c, gin.H{
		"session": session,
		"is_expired": session.Status == "expired",
	})
}

// 创建WhatsApp Account（扫码成功后调用）
func createWhatsAppAccountFromSession(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var req struct {
		SessionID   string `json:"session_id" binding:"required"`
		AccountName string `json:"account_name" binding:"required"`
		GroupID     *uint  `json:"group_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 查找Session
	var session WhatsAppSession
	if err := db.Where("session_id = ? AND tenant_id = ?", req.SessionID, *user.TenantID).First(&session).Error; err != nil {
		errorResponse(c, 404, "Session不存在或已过期")
		return
	}

	// 检查Session状态
	if session.Status != "active" {
		errorResponse(c, 400, "Session未激活，请先完成扫码登录")
		return
	}

	// 创建Account记录
	now := time.Now()
	account := WhatsAppAccount{
		TenantID:          *user.TenantID,
		SessionID:         req.SessionID,
		GroupID:           req.GroupID,
		AccountName:       req.AccountName,
		PhoneNumber:       session.PhoneNumber,
		Status:            "connected",
		ConnectionStatus:  "connected",
		AccountStatus:     "normal",
		IsActive:          true,
		AutoReconnect:     true,
		SessionStoragePath: session.SessionID, // 使用SessionID作为存储路径
		SessionStorageType: "local",
		SessionCreatedAt:   &now,
		IsSessionValid:     true,
		LoginTime:          &now,
		LastActivity:       &now,
	}

	// 从Session中获取客户端信息（Node.js已经发送过的信息）
	fmt.Printf("创建账号，Session ID: %s, Phone: %s\n", req.SessionID, session.PhoneNumber)
	
	// 使用手机号作为账号名称（如果用户输入的是默认名称）
	if req.AccountName == "新账号" || req.AccountName == "" || strings.Contains(req.AccountName, "新账号") {
		// 使用手机号作为账号名称
		if session.PhoneNumber != "" {
			account.AccountName = session.PhoneNumber
		}
	}
	
	// 从Session中读取客户端信息
	if session.ClientInfo != "" {
		var clientInfo map[string]interface{}
		if err := json.Unmarshal([]byte(session.ClientInfo), &clientInfo); err == nil {
			// 设置客户端信息
			if pushname, ok := clientInfo["pushname"].(string); ok && pushname != "" {
				account.PushName = pushname
				account.Nickname = pushname
				// 如果账号名称还是默认的，也可以考虑使用pushname
				if account.AccountName == session.PhoneNumber && pushname != "" {
					account.AccountName = fmt.Sprintf("%s (%s)", pushname, session.PhoneNumber)
				}
			} else {
				account.PushName = "用户"
				account.Nickname = "用户"
			}
			
			if platform, ok := clientInfo["platform"].(string); ok && platform != "" {
				account.Platform = platform
			} else {
				account.Platform = "android"
			}
			
			if wid, ok := clientInfo["wid"].(string); ok && wid != "" {
				account.WID = wid
			} else {
				account.WID = session.PhoneNumber + "@c.us"
			}
			
			// 设置头像
			if profilePicUrl, ok := clientInfo["profile_pic_url"].(string); ok && profilePicUrl != "" {
				account.Avatar = profilePicUrl
				account.ProfilePicURL = profilePicUrl
			}
			
			// 设置其他客户端信息
			if businessName, ok := clientInfo["business_name"].(string); ok {
				account.BusinessName = businessName
			}
			if description, ok := clientInfo["description"].(string); ok {
				account.Description = description
			}
			if email, ok := clientInfo["email"].(string); ok {
				account.Email = email
			}
			if website, ok := clientInfo["website"].(string); ok {
				account.Website = website
			}
			if address, ok := clientInfo["address"].(string); ok {
				account.Address = address
			}
			if category, ok := clientInfo["category"].(string); ok {
				account.Category = category
			}
			if subcategory, ok := clientInfo["subcategory"].(string); ok {
				account.Subcategory = subcategory
			}
			if isBusiness, ok := clientInfo["is_business"].(bool); ok {
				account.IsBusiness = isBusiness
			}
			if isEnterprise, ok := clientInfo["is_enterprise"].(bool); ok {
				account.IsEnterprise = isEnterprise
			}
			if isVerified, ok := clientInfo["is_verified"].(bool); ok {
				account.IsVerified = isVerified
			}
			if deviceCount, ok := clientInfo["device_count"].(float64); ok {
				account.DeviceCount = int(deviceCount)
			}
			
			fmt.Printf("从Session读取到客户端信息: pushname=%s, platform=%s, wid=%s, avatar=%s\n", 
				account.PushName, account.Platform, account.WID, account.Avatar)
		} else {
			fmt.Printf("解析Session客户端信息失败: %v\n", err)
			// 使用默认值
			account.PushName = "用户"
			account.Nickname = "用户"
			account.Platform = "android"
			account.WID = session.PhoneNumber + "@c.us"
		}
	} else {
		// Session中没有客户端信息，使用默认值
		account.PushName = "用户"
		account.Nickname = "用户"
		account.Platform = "android"
		account.WID = session.PhoneNumber + "@c.us"
	}

	if err := db.Create(&account).Error; err != nil {
		errorResponse(c, 500, "创建WhatsApp账号失败")
		return
	}

	// 保持Session状态为active，不改为completed
	// session.Status = "completed"  // 注释掉这行
	// db.Save(&session)            // 注释掉这行

	successResponse(c, gin.H{
		"account": account,
		"message": "WhatsApp账号创建成功",
	})
}

// 连接WhatsApp账号（Node服务版本）
func connectWhatsAppAccountNode(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	accountID := c.Param("id")
	var account WhatsAppAccount
	
	// 构建查询条件
	query := db.Where("id = ?", accountID)
	if user.TenantID != nil {
		query = query.Where("tenant_id = ?", *user.TenantID)
	}
	
	if err := query.First(&account).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 调用Node服务连接
	nodeReq := NodeServiceRequest{
		SessionID:   account.SessionID,
		PhoneNumber: account.PhoneNumber,
		TenantID:    account.TenantID, // 使用账号的租户ID
		Action:      "connect",
	}

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("连接失败: %v", err))
		return
	}

	if !nodeResp.Success {
		// 检查是否是session不存在的错误
		if strings.Contains(nodeResp.Error, "Session文件不存在") || 
		   strings.Contains(nodeResp.Error, "Session未找到") ||
		   strings.Contains(nodeResp.Error, "session") {
			// 返回特殊错误码，让前端弹出扫码界面
			errorResponse(c, 404, "Session不存在，需要重新扫码")
			return
		}
		
		// 检查是否是Chrome进程冲突
		if strings.Contains(nodeResp.Error, "SingletonLock") || 
		   strings.Contains(nodeResp.Error, "Failed to launch") ||
		   strings.Contains(nodeResp.Error, "browser process") {
			// 返回特殊错误码，让前端弹出扫码界面
			errorResponse(c, 404, "Chrome进程冲突，需要重新扫码")
			return
		}
		
		errorResponse(c, 500, fmt.Sprintf("Node服务错误: %s", nodeResp.Error))
		return
	}

	// 更新账号状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":             "connected",
		"connection_status":  "connected",
		"last_activity":      &now,
		"login_time":         &now,
		"is_session_valid":   true,
		"session_last_used":  &now,
		"reconnect_attempts": 0,
		"error":              "",
	}

	// 更新客户端信息
	if clientInfo, ok := nodeResp.Data["client_info"].(map[string]interface{}); ok {
		updates["w_id"] = clientInfo["wid"] // 修正字段名
		updates["phone_number"] = clientInfo["phone_number"] // 更新手机号
		updates["platform"] = clientInfo["platform"]
		updates["push_name"] = clientInfo["pushname"] // 修正字段名
		
		// 更新账号名称逻辑
		phoneNumber := ""
		pushname := ""
		if phone, ok := clientInfo["phone_number"].(string); ok {
			phoneNumber = phone
		}
		if push, ok := clientInfo["pushname"].(string); ok {
			pushname = push
		}
		
		// 如果当前账号名称是默认的或包含"新账号"，则更新为更有意义的名称
		if account.AccountName == "新账号" || account.AccountName == "" || 
		   strings.Contains(account.AccountName, "新账号") || account.AccountName == phoneNumber {
			if pushname != "" && phoneNumber != "" {
				updates["account_name"] = fmt.Sprintf("%s (%s)", pushname, phoneNumber)
			} else if phoneNumber != "" {
				updates["account_name"] = phoneNumber
			} else if pushname != "" {
				updates["account_name"] = pushname
			}
		}
		
		// 同时更新昵称字段
		if pushname != "" {
			updates["nickname"] = pushname
		}
		updates["business_name"] = clientInfo["business_name"]
		updates["description"] = clientInfo["description"]
		updates["email"] = clientInfo["email"]
		updates["website"] = clientInfo["website"]
		updates["address"] = clientInfo["address"]
		updates["category"] = clientInfo["category"]
		updates["subcategory"] = clientInfo["subcategory"]
		updates["is_business"] = clientInfo["is_business"]
		updates["is_enterprise"] = clientInfo["is_enterprise"]
		updates["is_verified"] = clientInfo["is_verified"]
		updates["device_count"] = clientInfo["device_count"]
		updates["profile_pic_url"] = clientInfo["profile_pic_url"]
		// 同时更新头像字段
		if profilePicUrl, ok := clientInfo["profile_pic_url"].(string); ok && profilePicUrl != "" {
			updates["avatar"] = profilePicUrl
		}
	}

	if err := db.Model(&account).Updates(updates).Error; err != nil {
		errorResponse(c, 500, "更新账号状态失败")
		return
	}

	successResponse(c, gin.H{
		"message": "WhatsApp账号连接成功",
		"account": account,
	})
}

// 断开WhatsApp账户连接
func disconnectWhatsAppAccountNode(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以断开WhatsApp账户")
		return
	}

	var req struct {
		AccountID uint `json:"account_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 查找账户
	var account WhatsAppAccount
	query := db.Where("id = ?", req.AccountID)
	if user.TenantID != nil {
		query = query.Where("tenant_id = ?", *user.TenantID)
	}
	if err := query.First(&account).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账户不存在")
		return
	}

	// 调用Node服务断开连接
	nodeReq := NodeServiceRequest{
		SessionID:   account.SessionID,
		PhoneNumber: account.PhoneNumber,
		TenantID:    account.TenantID,
		Action:      "disconnect",
	}

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("调用Node服务失败: %v", err))
		return
	}

	if !nodeResp.Success {
		errorResponse(c, 500, fmt.Sprintf("断开连接失败: %s", nodeResp.Error))
		return
	}

	// 更新数据库状态
	updates := map[string]interface{}{
		"status":            "disconnected",
		"connection_status": "disconnected",
		"is_session_valid":  false,
		"session_last_used": time.Now(),
	}

	if err := db.Model(&account).Updates(updates).Error; err != nil {
		errorResponse(c, 500, "更新账户状态失败")
		return
	}

	successResponse(c, gin.H{
		"message": "WhatsApp账户已断开连接",
		"data": gin.H{
			"account_id": account.ID,
			"status":     "disconnected",
		},
	})
}

// 重连WhatsApp账户
func reconnectWhatsAppAccountNode(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以重连WhatsApp账户")
		return
	}

	accountID := c.Param("id")
	
	// 查找账户
	var account WhatsAppAccount
	query := db.Where("id = ?", accountID)
	if user.TenantID != nil {
		query = query.Where("tenant_id = ?", *user.TenantID)
	}
	if err := query.First(&account).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账户不存在")
		return
	}

	// 检查session文件是否存在
	if account.SessionID == "" {
		errorResponse(c, 400, "账户没有关联的session，无法重连")
		return
	}

	// 优先尝试按需恢复
	restoreResp, err := callNodeServiceRestore(account.SessionID)
	if err == nil && restoreResp.Success {
		// 按需恢复成功
		fmt.Printf("按需恢复成功: %s\n", account.SessionID)
		
		// 更新数据库状态
		updates := map[string]interface{}{
			"status":            "connected",
			"connection_status": "connected",
			"is_session_valid":  true,
			"session_last_used": time.Now(),
			"error":             "",
		}

		// 更新客户端信息
		if clientInfo, ok := restoreResp.Data["client_info"].(map[string]interface{}); ok {
			updates["w_id"] = clientInfo["wid"]
			updates["phone_number"] = clientInfo["phone_number"]
			updates["platform"] = clientInfo["platform"]
			updates["push_name"] = clientInfo["pushname"]
			
			// 更新账号名称逻辑
			phoneNumber := ""
			pushname := ""
			if phone, ok := clientInfo["phone_number"].(string); ok {
				phoneNumber = phone
			}
			if push, ok := clientInfo["pushname"].(string); ok {
				pushname = push
			}
			
			// 如果当前账号名称是默认的或包含"新账号"，则更新为更有意义的名称
			if account.AccountName == "新账号" || account.AccountName == "" || 
			   strings.Contains(account.AccountName, "新账号") || account.AccountName == phoneNumber {
				if pushname != "" && phoneNumber != "" {
					updates["account_name"] = fmt.Sprintf("%s (%s)", pushname, phoneNumber)
				} else if phoneNumber != "" {
					updates["account_name"] = phoneNumber
				} else if pushname != "" {
					updates["account_name"] = pushname
				}
			}
			
			if pushname != "" {
				updates["nickname"] = pushname
			}
			if profilePicUrl, ok := clientInfo["profile_pic_url"].(string); ok && profilePicUrl != "" {
				updates["avatar"] = profilePicUrl
			}
		}

		if err := db.Model(&account).Updates(updates).Error; err != nil {
			errorResponse(c, 500, "更新账户状态失败")
			return
		}

		successResponse(c, gin.H{
			"message": "WhatsApp账户按需恢复成功",
			"data": gin.H{
				"account_id": account.ID,
				"status":     "connected",
				"method":     "on_demand_restore",
			},
		})
		return
	}

	// 按需恢复失败，尝试传统重连
	fmt.Printf("按需恢复失败，尝试传统重连: %s\n", account.SessionID)
	
	// 调用Node服务重连
	nodeReq := NodeServiceRequest{
		SessionID:   account.SessionID,
		PhoneNumber: account.PhoneNumber,
		TenantID:    account.TenantID,
		Action:      "reconnect",
	}

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("调用Node服务失败: %v", err))
		return
	}

	if !nodeResp.Success {
		// 检查是否是session不存在的错误
		if strings.Contains(nodeResp.Error, "Session文件不存在") || 
		   strings.Contains(nodeResp.Error, "Session未找到") ||
		   strings.Contains(nodeResp.Error, "session") {
			// 返回特殊错误码，让前端弹出扫码界面
			errorResponse(c, 404, "Session不存在，需要重新扫码")
			return
		}
		errorResponse(c, 500, fmt.Sprintf("重连失败: %s", nodeResp.Error))
		return
	}

	// 更新数据库状态
	updates := map[string]interface{}{
		"status":            "connected",
		"connection_status": "connected",
		"is_session_valid":  true,
		"session_last_used": time.Now(),
	}

	if err := db.Model(&account).Updates(updates).Error; err != nil {
		errorResponse(c, 500, "更新账户状态失败")
		return
	}

	successResponse(c, gin.H{
		"message": "WhatsApp账户重连成功",
		"data": gin.H{
			"account_id": account.ID,
			"status":     "connected",
			"method":     "traditional_reconnect",
		},
	})
}

// 调用Node服务按需恢复
func callNodeServiceRestore(sessionID string) (*NodeServiceResponse, error) {
	client := &http.Client{
		Timeout: 30 * time.Second, // 按需恢复可能需要更长时间
	}

	resp, err := client.Post(
		fmt.Sprintf("%s/api/whatsapp/session/%s/restore", NodeServiceURL, sessionID),
		"application/json",
		strings.NewReader("{}"),
	)
	if err != nil {
		return nil, fmt.Errorf("调用Node服务按需恢复失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取按需恢复响应失败: %v", err)
	}

	var nodeResp NodeServiceResponse
	if err := json.Unmarshal(body, &nodeResp); err != nil {
		return nil, fmt.Errorf("解析按需恢复响应失败: %v", err)
	}

	return &nodeResp, nil
}

// 发送消息
func sendWhatsAppMessage(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	accountID := c.Param("id")
	var account WhatsAppAccount
	
	// 构建查询条件
	query := db.Where("id = ?", accountID)
	if user.TenantID != nil {
		query = query.Where("tenant_id = ?", *user.TenantID)
	}
	
	if err := query.First(&account).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	var req struct {
		To      string `json:"to" binding:"required"`
		Message string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 调用Node服务发送消息
	nodeReq := NodeServiceRequest{
		SessionID:   account.SessionID,
		PhoneNumber: account.PhoneNumber,
		TenantID:    account.TenantID, // 使用账号的租户ID
		Action:      "send_message",
		Data: map[string]interface{}{
			"to":      req.To,
			"message": req.Message,
		},
	}

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("发送消息失败: %v", err))
		return
	}

	if !nodeResp.Success {
		errorResponse(c, 500, fmt.Sprintf("Node服务错误: %s", nodeResp.Error))
		return
	}

	// 更新最后活动时间
	now := time.Now()
	db.Model(&account).Update("last_activity", &now)

	successResponse(c, gin.H{
		"message": "消息发送成功",
		"message_id": nodeResp.Data["message_id"],
	})
}

// 获取账号信息
func getWhatsAppAccountInfo(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	accountID := c.Param("id")
	var account WhatsAppAccount
	
	// 构建查询条件
	query := db.Where("id = ?", accountID)
	if user.TenantID != nil {
		query = query.Where("tenant_id = ?", *user.TenantID)
	}
	
	if err := query.First(&account).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 调用Node服务获取信息
	nodeReq := NodeServiceRequest{
		SessionID:   account.SessionID,
		PhoneNumber: account.PhoneNumber,
		TenantID:    account.TenantID, // 使用账号的租户ID
		Action:      "get_info",
	}

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("获取信息失败: %v", err))
		return
	}

	if !nodeResp.Success {
		errorResponse(c, 500, fmt.Sprintf("Node服务错误: %s", nodeResp.Error))
		return
	}

	// 更新客户端信息
	if clientInfo, ok := nodeResp.Data["client_info"].(map[string]interface{}); ok {
		updates := map[string]interface{}{
			"w_id":                clientInfo["wid"], // 修正字段名
			"phone_number":        clientInfo["phone_number"], // 更新手机号
			"platform":            clientInfo["platform"],
			"push_name":           clientInfo["pushname"], // 修正字段名
			"business_name":       clientInfo["business_name"],
			"description":         clientInfo["description"],
			"email":               clientInfo["email"],
			"website":             clientInfo["website"],
			"address":             clientInfo["address"],
			"category":            clientInfo["category"],
			"subcategory":         clientInfo["subcategory"],
			"is_business":         clientInfo["is_business"],
			"is_enterprise":       clientInfo["is_enterprise"],
			"is_verified":         clientInfo["is_verified"],
			"device_count":        clientInfo["device_count"],
			"profile_pic_url":     clientInfo["profile_pic_url"],
			"profile_pic_updated_at": time.Now(),
		}

		// 更新账号名称逻辑
		phoneNumber := ""
		pushname := ""
		if phone, ok := clientInfo["phone_number"].(string); ok {
			phoneNumber = phone
		}
		if push, ok := clientInfo["pushname"].(string); ok {
			pushname = push
		}
		
		// 如果当前账号名称是默认的或包含"新账号"，则更新为更有意义的名称
		if account.AccountName == "新账号" || account.AccountName == "" || 
		   strings.Contains(account.AccountName, "新账号") || account.AccountName == phoneNumber {
			if pushname != "" && phoneNumber != "" {
				updates["account_name"] = fmt.Sprintf("%s (%s)", pushname, phoneNumber)
			} else if phoneNumber != "" {
				updates["account_name"] = phoneNumber
			} else if pushname != "" {
				updates["account_name"] = pushname
			}
		}

		// 同时更新昵称和头像字段
		if pushname != "" {
			updates["nickname"] = pushname
		}
		if profilePicUrl, ok := clientInfo["profile_pic_url"].(string); ok && profilePicUrl != "" {
			updates["avatar"] = profilePicUrl
		}

		db.Model(&account).Updates(updates)
	}

	successResponse(c, gin.H{
		"account": account,
		"client_info": nodeResp.Data["client_info"],
	})
}

// 清理Session
func cleanupWhatsAppSession(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以清理Session")
		return
	}

	accountID := c.Param("id")
	var account WhatsAppAccount
	
	// 构建查询条件
	query := db.Where("id = ?", accountID)
	if user.TenantID != nil {
		query = query.Where("tenant_id = ?", *user.TenantID)
	}
	
	if err := query.First(&account).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 调用Node服务清理Session
	nodeReq := NodeServiceRequest{
		SessionID:   account.SessionID,
		PhoneNumber: account.PhoneNumber,
		TenantID:    account.TenantID, // 使用账号的租户ID
		Action:      "cleanup",
	}

	nodeResp, err := callNodeService(nodeReq)
	if err != nil {
		errorResponse(c, 500, fmt.Sprintf("清理Session失败: %v", err))
		return
	}

	// 记录清理日志
	cleanupLog := SessionCleanupLog{
		AccountID:     account.ID,
		SessionID:     account.SessionID,
		CleanupType:   "manual_cleanup",
		FileSizeFreed: int64(nodeResp.Data["file_size_freed"].(float64)),
		FileCountFreed: int(nodeResp.Data["file_count_freed"].(float64)),
		CleanupReason: "手动清理",
		CleanedAt:     time.Now(),
	}

	db.Create(&cleanupLog)

	// 更新账号状态
	updates := map[string]interface{}{
		"session_id":           "",
		"session_storage_path": "",
		"session_created_at":   nil,
		"session_last_used":    nil,
		"session_file_size":    0,
		"session_file_count":   0,
		"is_session_valid":     false,
		"status":               "disconnected",
		"connection_status":    "disconnected",
	}

	db.Model(&account).Updates(updates)

	successResponse(c, gin.H{
		"message": "Session清理成功",
		"freed_size": nodeResp.Data["file_size_freed"],
		"freed_count": nodeResp.Data["file_count_freed"],
	})
} 

// 清理过期Session
func cleanupExpiredSessions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以清理Session")
		return
	}

	// 查找过期的Session
	var expiredSessions []WhatsAppSession
	now := time.Now()
	
	result := db.Where("tenant_id = ? AND (expires_at < ? OR status = 'expired')", *user.TenantID, now).Find(&expiredSessions)
	if result.Error != nil {
		errorResponse(c, 500, "查询过期Session失败")
		return
	}

	// 清理过期Session
	var cleanedCount int64
	for _, session := range expiredSessions {
		// 调用Node服务清理Session
		nodeReq := NodeServiceRequest{
			SessionID:   session.SessionID,
			PhoneNumber: session.PhoneNumber,
			TenantID:    *user.TenantID,
			Action:      "cleanup",
		}
		
		_, err := callNodeService(nodeReq)
		if err != nil {
			fmt.Printf("清理Session失败: %s, 错误: %v\n", session.SessionID, err)
			continue
		}
		
		// 更新关联的Account记录
		var accounts []WhatsAppAccount
		if err := db.Where("session_id = ?", session.SessionID).Find(&accounts).Error; err == nil {
			for _, account := range accounts {
				// 找到关联的Account，更新其session相关字段
				updates := map[string]interface{}{
					"session_storage_path": "",
					"session_created_at":   nil,
					"session_last_used":    nil,
					"session_file_size":    0,
					"session_file_count":   0,
					"is_session_valid":     false,
					"status":               "disconnected",
					"connection_status":    "disconnected",
				}
				
				// 先清空session_id，避免UNIQUE约束冲突
				if err := db.Model(&account).Update("session_id", "").Error; err != nil {
					fmt.Printf("清空Account session_id失败: %s, 错误: %v\n", account.AccountName, err)
					continue
				}
				
				// 然后更新其他字段
				if err := db.Model(&account).Updates(updates).Error; err != nil {
					fmt.Printf("更新Account失败: %s, 错误: %v\n", account.AccountName, err)
				} else {
					fmt.Printf("已更新关联Account: %s\n", account.AccountName)
				}
			}
		}
		
		// 删除Session记录
		db.Delete(&session)
		cleanedCount++
	}

	successResponse(c, gin.H{
		"cleaned_count": cleanedCount,
		"total_expired": len(expiredSessions),
		"message": fmt.Sprintf("成功清理 %d 个过期Session", cleanedCount),
	})
} 

// 更新Session状态
func updateSessionStatus(c *gin.Context) {
	sessionID := c.Param("session_id")
	
	var req struct {
		Status      string                 `json:"status" binding:"required"`
		PhoneNumber string                 `json:"phone_number"`
		Error       string                 `json:"error"`
		ClientInfo  map[string]interface{} `json:"client_info"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}
	
	var session WhatsAppSession
	if err := db.Where("session_id = ?", sessionID).First(&session).Error; err != nil {
		errorResponse(c, 404, "Session不存在")
		return
	}
	
	// 更新Session状态
	session.Status = req.Status
	if req.PhoneNumber != "" {
		session.PhoneNumber = req.PhoneNumber
	}
	if req.Error != "" {
		session.Error = req.Error
	}
	session.UpdatedAt = time.Now()
	
	if err := db.Save(&session).Error; err != nil {
		errorResponse(c, 500, "更新Session状态失败")
		return
	}
	
	// 同时更新对应的WhatsAppAccount状态
	var account WhatsAppAccount
	if err := db.Where("session_id = ?", sessionID).First(&account).Error; err == nil {
		// 找到对应的Account，更新其状态
		if req.Status == "active" {
			account.ConnectionStatus = "connected"
			account.Status = "connected"
		} else if req.Status == "disconnected" {
			account.ConnectionStatus = "disconnected"
			account.Status = "disconnected"
		}
		account.UpdatedAt = time.Now()
		db.Save(&account)
		fmt.Printf("已同步更新Account状态: %d -> %s\n", account.ID, req.Status)
	}
	
	// 如果有客户端信息，保存到Session中（可以用于后续创建Account时使用）
	if req.ClientInfo != nil {
		// 将客户端信息保存到Session中，用于后续创建Account
		fmt.Printf("收到客户端信息: %+v\n", req.ClientInfo)
		
		// 将客户端信息序列化为JSON字符串保存到Session中
		if clientInfoJSON, err := json.Marshal(req.ClientInfo); err == nil {
			// 保存客户端信息到Session
			session.ClientInfo = string(clientInfoJSON)
			db.Save(&session)
			fmt.Printf("客户端信息已保存到Session: %s\n", sessionID)
		}
	}
	
	successResponse(c, gin.H{
		"session": session,
		"message": "Session状态更新成功",
	})
} 

// 修复孤立Account记录
func fixOrphanedAccounts(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以执行此操作")
		return
	}

	// 查找孤立的Account记录（session_id不为空但对应的Session不存在）
	var orphanedAccounts []WhatsAppAccount
	query := db.Table("whats_app_accounts").
		Joins("LEFT JOIN whats_app_sessions ON whats_app_accounts.session_id = whats_app_sessions.session_id").
		Where("whats_app_accounts.session_id != '' AND whats_app_sessions.session_id IS NULL")
	
	if user.TenantID != nil {
		query = query.Where("whats_app_accounts.tenant_id = ?", *user.TenantID)
	}
	
	if err := query.Find(&orphanedAccounts).Error; err != nil {
		errorResponse(c, 500, "查询孤立Account失败")
		return
	}

	var fixedCount int64
	for _, account := range orphanedAccounts {
		// 更新Account的session相关字段
		updates := map[string]interface{}{
			"session_id":           "",
			"session_storage_path": "",
			"session_created_at":   nil,
			"session_last_used":    nil,
			"session_file_size":    0,
			"session_file_count":   0,
			"is_session_valid":     false,
			"status":               "disconnected",
			"connection_status":    "disconnected",
		}
		
		if err := db.Model(&account).Updates(updates).Error; err != nil {
			fmt.Printf("修复Account失败: %s, 错误: %v\n", account.AccountName, err)
			continue
		}
		
		fixedCount++
		fmt.Printf("已修复孤立Account: %s (原session_id: %s)\n", account.AccountName, account.SessionID)
	}

	successResponse(c, gin.H{
		"fixed_count": fixedCount,
		"total_orphaned": len(orphanedAccounts),
		"message": fmt.Sprintf("成功修复 %d 个孤立Account", fixedCount),
	})
} 

// 处理Node服务的Session清理通知
func handleNodeSessionCleanup(c *gin.Context) {
	sessionID := c.Param("session_id")
	
	// 删除数据库中的Session记录
	var session WhatsAppSession
	if err := db.Where("session_id = ?", sessionID).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Session记录不存在，这是正常的（可能已经被清理了）
			successResponse(c, gin.H{
				"message": "Session记录不存在或已被清理",
				"session_id": sessionID,
			})
			return
		}
		errorResponse(c, 500, fmt.Sprintf("查询Session失败: %v", err))
		return
	}

	// 删除Session记录
	if err := db.Delete(&session).Error; err != nil {
		errorResponse(c, 500, fmt.Sprintf("删除Session失败: %v", err))
		return
	}

	// 同时清理相关的Account记录（如果有的话）
	var accounts []WhatsAppAccount
	if err := db.Where("session_id = ?", sessionID).Find(&accounts).Error; err == nil {
		for _, account := range accounts {
			// 更新Account状态为disconnected
			updates := map[string]interface{}{
				"status":            "disconnected",
				"connection_status": "disconnected",
				"is_session_valid":  false,
				"session_last_used": time.Now(),
			}
			db.Model(&account).Updates(updates)
		}
	}

	successResponse(c, gin.H{
		"message": "Session记录已删除",
		"session_id": sessionID,
		"accounts_updated": len(accounts),
	})
} 

// 获取所有Session状态（内部接口，不需要认证）
func getAllSessionsStatusInternal(c *gin.Context) {
	var sessions []WhatsAppSession
	if err := db.Find(&sessions).Error; err != nil {
		errorResponse(c, 500, fmt.Sprintf("获取Session列表失败: %v", err))
		return
	}

	// 转换为响应格式
	var sessionList []gin.H
	for _, session := range sessions {
		sessionList = append(sessionList, gin.H{
			"session_id":       session.SessionID,
			"status":           session.Status,
			"phone_number":     session.PhoneNumber,
			"tenant_id":        session.TenantID,
			"is_session_valid": session.Status == "active" || (session.Status == "disconnected" && session.PhoneNumber != ""),
			"created_at":       session.CreatedAt,
			"last_used":        session.UpdatedAt,
		})
	}

	successResponse(c, gin.H{
		"success": true,
		"data":    sessionList,
	})
}

// 获取所有Session状态（需要认证）
func getAllSessionsStatus(c *gin.Context) {
	var sessions []WhatsAppSession
	if err := db.Find(&sessions).Error; err != nil {
		errorResponse(c, 500, fmt.Sprintf("获取Session列表失败: %v", err))
		return
	}

	// 转换为响应格式
	var sessionList []gin.H
	for _, session := range sessions {
		sessionList = append(sessionList, gin.H{
			"session_id":       session.SessionID,
			"status":           session.Status,
			"phone_number":     session.PhoneNumber,
			"tenant_id":        session.TenantID,
			"is_session_valid": session.Status == "active" || (session.Status == "disconnected" && session.PhoneNumber != ""),
			"created_at":       session.CreatedAt,
			"last_used":        session.UpdatedAt,
		})
	}

	successResponse(c, gin.H{
		"success": true,
		"data":    sessionList,
	})
}

// Node服务重启通知接口
func handleNodeServiceRestart(c *gin.Context) {
	var req struct {
		ServiceID      string   `json:"service_id" binding:"required"`
		Timestamp      string   `json:"timestamp" binding:"required"`
		ActiveSessions []string `json:"active_sessions"`
		ProcessPID     int      `json:"process_pid"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}
	
	fmt.Printf("收到Node服务重启通知: ServiceID=%s, ActiveSessions=%v\n", req.ServiceID, req.ActiveSessions)
	
	// 解析时间戳
	startTime, err := time.Parse(time.RFC3339, req.Timestamp)
	if err != nil {
		startTime = time.Now()
	}
	
	// 将活跃session列表转换为JSON字符串
	activeSessionsJSON := "[]"
	if len(req.ActiveSessions) > 0 {
		if jsonBytes, err := json.Marshal(req.ActiveSessions); err == nil {
			activeSessionsJSON = string(jsonBytes)
		}
	}
	
	// 更新或创建Node服务状态记录
	var nodeStatus NodeServiceStatus
	result := db.Where("service_id = ?", req.ServiceID).First(&nodeStatus)
	
	if result.Error != nil {
		// 创建新记录
		nodeStatus = NodeServiceStatus{
			ServiceID:      req.ServiceID,
			Status:         "running",
			LastSeen:       time.Now(),
			ActiveSessions: activeSessionsJSON,
			ProcessPID:     req.ProcessPID,
			StartTime:      startTime,
			RestartCount:   1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}
		db.Create(&nodeStatus)
		fmt.Printf("创建Node服务状态记录: %s\n", req.ServiceID)
	} else {
		// 更新现有记录
		nodeStatus.Status = "running"
		nodeStatus.LastSeen = time.Now()
		nodeStatus.ActiveSessions = activeSessionsJSON
		nodeStatus.ProcessPID = req.ProcessPID
		nodeStatus.StartTime = startTime
		nodeStatus.RestartCount++
		nodeStatus.UpdatedAt = time.Now()
		db.Save(&nodeStatus)
		fmt.Printf("更新Node服务状态记录: %s (重启次数: %d)\n", req.ServiceID, nodeStatus.RestartCount)
	}
	
	// 标记不在active_sessions中的session为需要恢复
	go func() {
		markSessionsForRecovery(req.ActiveSessions)
	}()
	
	successResponse(c, gin.H{
		"message": "Node服务状态已更新",
		"service_id": req.ServiceID,
		"restart_count": nodeStatus.RestartCount,
	})
}

// Node服务心跳接口
func handleNodeServiceHeartbeat(c *gin.Context) {
	var req struct {
		ServiceID      string   `json:"service_id" binding:"required"`
		ActiveSessions []string `json:"active_sessions"`
		ProcessPID     int      `json:"process_pid"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}
	
	// 将活跃session列表转换为JSON字符串
	activeSessionsJSON := "[]"
	if len(req.ActiveSessions) > 0 {
		if jsonBytes, err := json.Marshal(req.ActiveSessions); err == nil {
			activeSessionsJSON = string(jsonBytes)
		}
	}
	
	// 更新Node服务状态
	var nodeStatus NodeServiceStatus
	result := db.Where("service_id = ?", req.ServiceID).First(&nodeStatus)
	
	if result.Error != nil {
		// 如果记录不存在，创建一个
		nodeStatus = NodeServiceStatus{
			ServiceID:      req.ServiceID,
			Status:         "running",
			LastSeen:       time.Now(),
			ActiveSessions: activeSessionsJSON,
			ProcessPID:     req.ProcessPID,
			StartTime:      time.Now(),
			RestartCount:   0,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}
		db.Create(&nodeStatus)
	} else {
		// 更新心跳时间和活跃session
		nodeStatus.Status = "running"
		nodeStatus.LastSeen = time.Now()
		nodeStatus.ActiveSessions = activeSessionsJSON
		nodeStatus.ProcessPID = req.ProcessPID
		nodeStatus.UpdatedAt = time.Now()
		db.Save(&nodeStatus)
	}
	
	successResponse(c, gin.H{
		"message": "心跳已更新",
		"service_id": req.ServiceID,
	})
}

// 获取Node服务状态
func getNodeServiceStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限
	if user.Role != "admin" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以查看Node服务状态")
		return
	}
	
	var nodeStatuses []NodeServiceStatus
	if err := db.Find(&nodeStatuses).Error; err != nil {
		errorResponse(c, 500, "获取Node服务状态失败")
		return
	}
	
	// 检查服务是否在线（超过2分钟没有心跳认为离线）
	now := time.Now()
	for i := range nodeStatuses {
		if now.Sub(nodeStatuses[i].LastSeen) > 2*time.Minute {
			nodeStatuses[i].Status = "offline"
		}
	}
	
	successResponse(c, gin.H{
		"services": nodeStatuses,
		"total": len(nodeStatuses),
	})
}

// 标记需要恢复的session
func markSessionsForRecovery(activeSessions []string) {
	fmt.Printf("开始标记需要恢复的Session，当前活跃: %v\n", activeSessions)
	
	// 创建活跃session的map用于快速查找
	activeMap := make(map[string]bool)
	for _, sessionID := range activeSessions {
		activeMap[sessionID] = true
	}
	
	// 查找所有应该活跃但不在活跃列表中的session
	var sessions []WhatsAppSession
	db.Where("status IN (?) AND phone_number != ''", []string{"active", "connected"}).Find(&sessions)
	
	var needRecoveryCount int
	for _, session := range sessions {
		if !activeMap[session.SessionID] {
			// 这个session应该活跃但不在Node服务的活跃列表中，标记为需要恢复
			fmt.Printf("标记Session需要恢复: %s (状态: %s, 手机号: %s)\n", 
				session.SessionID, session.Status, session.PhoneNumber)
			
			// 更新session状态为disconnected，但保留手机号等信息
			updates := map[string]interface{}{
				"status": "disconnected",
				"error":  "Node服务重启，需要恢复连接",
				"updated_at": time.Now(),
			}
			db.Model(&session).Updates(updates)
			
			// 同时更新关联的Account状态
			var accounts []WhatsAppAccount
			if err := db.Where("session_id = ?", session.SessionID).Find(&accounts).Error; err == nil {
				for _, account := range accounts {
					accountUpdates := map[string]interface{}{
						"status": "disconnected",
						"connection_status": "disconnected",
						"is_session_valid": false,
						"error": "Node服务重启，需要恢复连接",
						"updated_at": time.Now(),
					}
					if err := db.Model(&account).Updates(accountUpdates).Error; err != nil {
						fmt.Printf("更新Account状态失败: %s, 错误: %v\n", account.AccountName, err)
					} else {
						fmt.Printf("已更新Account状态: %s\n", account.AccountName)
					}
				}
			}
			
			needRecoveryCount++
		}
	}
	
	fmt.Printf("标记完成，共 %d 个Session需要恢复\n", needRecoveryCount)
} 