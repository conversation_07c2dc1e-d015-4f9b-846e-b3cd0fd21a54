package main

import (
	"time"

	"github.com/gin-gonic/gin"
)

// 获取WhatsApp分组列表
func getWhatsAppGroupList(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var groups []WhatsAppGroup
	
	// 所有用户都只查看当前租户的分组
	if user.TenantID == nil {
		errorResponse(c, 403, "用户必须属于某个租户才能查看WhatsApp分组")
		return
	}
	
	result := db.Preload("Tenant").Preload("Accounts").Where("tenant_id = ?", *user.TenantID).Order("sort_order ASC").Find(&groups)
	if result.Error != nil {
		errorResponse(c, 500, "获取WhatsApp分组列表失败")
		return
	}

	successResponse(c, gin.H{
		"groups": groups,
		"total":  len(groups),
	})
}

// 根据ID获取WhatsApp分组
func getWhatsAppGroupById(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var group WhatsAppGroup
	result := db.Preload("Tenant").Preload("Accounts").First(&group, id)
	if result.Error != nil {
		errorResponse(c, 404, "WhatsApp分组不存在")
		return
	}

	// 所有用户都只能查看当前租户的分组
	if user.TenantID == nil {
		errorResponse(c, 403, "用户必须属于某个租户才能查看WhatsApp分组")
		return
	}
	
	if group.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权访问此WhatsApp分组")
		return
	}

	successResponse(c, group)
}

// 创建WhatsApp分组
func createWhatsAppGroup(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以创建WhatsApp分组
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以创建WhatsApp分组")
		return
	}

	var group WhatsAppGroup
	if err := c.ShouldBindJSON(&group); err != nil {
		errorResponse(c, 400, "请求数据格式错误")
		return
	}

	// 验证必填字段
	if group.Name == "" {
		errorResponse(c, 400, "分组名称不能为空")
		return
	}

	// 设置租户ID
	if user.TenantID != nil {
		group.TenantID = *user.TenantID
	} else {
		errorResponse(c, 400, "用户必须属于某个租户")
		return
	}

	// 检查分组名称是否已存在（同一租户内）
	var existingGroup WhatsAppGroup
	if err := db.Where("tenant_id = ? AND name = ?", group.TenantID, group.Name).First(&existingGroup).Error; err == nil {
		errorResponse(c, 400, "分组名称已存在")
		return
	}

	// 设置默认值
	if group.Color == "" {
		group.Color = "#409EFF"
	}
	if group.SortOrder == 0 {
		// 获取当前最大排序值
		var maxSortOrder int
		db.Model(&WhatsAppGroup{}).Where("tenant_id = ?", group.TenantID).Select("COALESCE(MAX(sort_order), 0)").Scan(&maxSortOrder)
		group.SortOrder = maxSortOrder + 1
	}
	group.CreatedAt = time.Now()
	group.UpdatedAt = time.Now()

	result := db.Create(&group)
	if result.Error != nil {
		errorResponse(c, 500, "创建WhatsApp分组失败")
		return
	}

	successResponse(c, group)
}

// 更新WhatsApp分组
func updateWhatsAppGroup(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以更新WhatsApp分组
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以更新WhatsApp分组")
		return
	}

	var group WhatsAppGroup
	if err := db.First(&group, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp分组不存在")
		return
	}

	// 租户级用户只能更新自己租户的分组
	if user.TenantID != nil && group.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权更新此WhatsApp分组")
		return
	}

	var updateData WhatsAppGroup
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "请求数据格式错误")
		return
	}

	// 检查分组名称是否已存在（同一租户内，排除当前分组）
	if updateData.Name != "" && updateData.Name != group.Name {
		var existingGroup WhatsAppGroup
		if err := db.Where("tenant_id = ? AND name = ? AND id != ?", group.TenantID, updateData.Name, group.ID).First(&existingGroup).Error; err == nil {
			errorResponse(c, 400, "分组名称已存在")
			return
		}
	}

	// 只允许更新特定字段
	updates := map[string]interface{}{
		"name":        updateData.Name,
		"description": updateData.Description,
		"color":       updateData.Color,
		"status":      updateData.Status,
		"sort_order":  updateData.SortOrder,
		"updated_at":  time.Now(),
	}

	result := db.Model(&group).Updates(updates)
	if result.Error != nil {
		errorResponse(c, 500, "更新WhatsApp分组失败")
		return
	}

	successResponse(c, group)
}

// 删除WhatsApp分组
func deleteWhatsAppGroup(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以删除WhatsApp分组
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以删除WhatsApp分组")
		return
	}

	var group WhatsAppGroup
	if err := db.First(&group, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp分组不存在")
		return
	}

	// 租户级用户只能删除自己租户的分组
	if user.TenantID != nil && group.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权删除此WhatsApp分组")
		return
	}

	// 检查是否为默认分组
	if group.Name == "默认分组" {
		errorResponse(c, 400, "不能删除默认分组")
		return
	}

	// 检查分组下是否有账号
	var accountCount int64
	db.Model(&WhatsAppAccount{}).Where("group_id = ?", group.ID).Count(&accountCount)
	if accountCount > 0 {
		errorResponse(c, 400, "分组下还有账号，无法删除")
		return
	}

	result := db.Delete(&group)
	if result.Error != nil {
		errorResponse(c, 500, "删除WhatsApp分组失败")
		return
	}

	successResponse(c, gin.H{"message": "WhatsApp分组删除成功"})
}

// 获取分组下的账号列表
func getGroupAccounts(c *gin.Context) {
	groupID := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 先检查分组是否存在
	var group WhatsAppGroup
	if err := db.First(&group, groupID).Error; err != nil {
		errorResponse(c, 404, "WhatsApp分组不存在")
		return
	}

	// 检查权限：所有用户都只能访问当前租户的分组
	if user.TenantID == nil || group.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权访问此WhatsApp分组")
		return
	}

	// 获取分组下的账号
	var accounts []WhatsAppAccount
	result := db.Where("group_id = ?", groupID).Find(&accounts)
	if result.Error != nil {
		errorResponse(c, 500, "获取分组账号失败")
		return
	}

	successResponse(c, gin.H{
		"group":    group,
		"accounts": accounts,
		"total":    len(accounts),
	})
}

// 批量移动账号到分组
func moveAccountsToGroup(c *gin.Context) {
	groupID := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以移动账号
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以移动账号")
		return
	}

	// 检查分组是否存在
	var group WhatsAppGroup
	if err := db.First(&group, groupID).Error; err != nil {
		errorResponse(c, 404, "WhatsApp分组不存在")
		return
	}

	// 检查权限
	if user.TenantID != nil && group.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权操作此WhatsApp分组")
		return
	}

	// 解析请求数据
	var request struct {
		AccountIDs []uint `json:"account_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		errorResponse(c, 400, "请求数据格式错误")
		return
	}

	// 验证账号是否属于当前租户
	var accountCount int64
	db.Model(&WhatsAppAccount{}).Where("id IN ? AND tenant_id = ?", request.AccountIDs, group.TenantID).Count(&accountCount)
	if int(accountCount) != len(request.AccountIDs) {
		errorResponse(c, 400, "部分账号不属于当前租户")
		return
	}

	// 批量更新账号分组
	result := db.Model(&WhatsAppAccount{}).Where("id IN ?", request.AccountIDs).Update("group_id", groupID)
	if result.Error != nil {
		errorResponse(c, 500, "移动账号失败")
		return
	}

	successResponse(c, gin.H{
		"message": "账号移动成功",
		"moved_count": result.RowsAffected,
	})
}

// 重新排序分组
func reorderGroups(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以重新排序分组
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以重新排序分组")
		return
	}

	// 解析请求数据
	var request struct {
		GroupOrders []struct {
			ID        uint `json:"id"`
			SortOrder int  `json:"sort_order"`
		} `json:"group_orders" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		errorResponse(c, 400, "请求数据格式错误")
		return
	}

	// 验证分组是否属于当前租户
	if user.TenantID != nil {
		var groupIDs []uint
		for _, order := range request.GroupOrders {
			groupIDs = append(groupIDs, order.ID)
		}

		var groupCount int64
		db.Model(&WhatsAppGroup{}).Where("id IN ? AND tenant_id = ?", groupIDs, *user.TenantID).Count(&groupCount)
		if int(groupCount) != len(request.GroupOrders) {
			errorResponse(c, 400, "部分分组不属于当前租户")
			return
		}
	}

	// 批量更新排序
	for _, order := range request.GroupOrders {
		db.Model(&WhatsAppGroup{}).Where("id = ?", order.ID).Update("sort_order", order.SortOrder)
	}

	successResponse(c, gin.H{"message": "分组排序更新成功"})
} 