package main

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 获取角色列表
func getRoleList(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	fmt.Printf("getRoleList: user=%s, user_type=%s, role=%s, tenant_id=%v\n", user.Username, user.UserType, user.Role, user.TenantID)

	var roles []Role
	
	// 系统级超级管理员可以查看所有角色
	if user.UserType == "system" && user.Role == "super_admin" {
		fmt.Printf("getRoleList: system super admin, showing all roles\n")
		result := db.Preload("Permissions").Find(&roles)
		if result.Error != nil {
			errorResponse(c, 500, "获取角色列表失败")
			return
		}
	} else {
		// 租户级管理员只能查看自己租户的角色
		if user.TenantID == nil {
			fmt.Printf("getRoleList: tenant admin has no tenant_id\n")
			errorResponse(c, 403, "租户级管理员无法访问其他租户的角色")
			return
		}
		
		fmt.Printf("getRoleList: tenant admin, showing roles for tenant_id=%d\n", *user.TenantID)
		result := db.Preload("Permissions").Where("tenant_id = ?", *user.TenantID).Find(&roles)
		if result.Error != nil {
			errorResponse(c, 500, "获取角色列表失败")
			return
		}
	}

	fmt.Printf("getRoleList: found %d roles\n", len(roles))
	successResponse(c, gin.H{
		"roles": roles,
		"total": len(roles),
	})
}

// 根据ID获取角色
func getRoleById(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var role Role
	result := db.Preload("Permissions").First(&role, id)
	if result.Error != nil {
		errorResponse(c, 404, "角色不存在")
		return
	}

	// 系统级超级管理员可以查看所有角色
	if user.UserType == "system" && user.Role == "super_admin" {
		// 可以查看所有角色
	} else {
		// 租户级管理员只能查看自己租户的角色
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法访问其他租户的角色")
			return
		}
		
		if role.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权访问此角色")
			return
		}
	}

	successResponse(c, role)
}

// 创建角色
func createRole(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var newRole Role
	if err := c.ShouldBindJSON(&newRole); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 系统级超级管理员可以为任何租户创建角色
	if user.UserType == "system" && user.Role == "super_admin" {
		// 可以创建角色，但需要指定租户ID
		if newRole.TenantID == 0 {
			errorResponse(c, 400, "系统级超级管理员创建角色时必须指定租户ID")
			return
		}
	} else {
		// 租户级管理员只能在自己的租户中创建角色
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法在其他租户中创建角色")
			return
		}
		
		// 强制设置为当前用户的租户ID
		newRole.TenantID = *user.TenantID
	}

	// 检查角色名在指定租户中是否存在
	var existingRole Role
	if err := db.Where("name = ? AND tenant_id = ?", newRole.Name, newRole.TenantID).First(&existingRole).Error; err == nil {
		errorResponse(c, 400, "该租户中角色名已存在")
		return
	}

	result := db.Create(&newRole)
	if result.Error != nil {
		errorResponse(c, 500, "创建角色失败")
		return
	}

	successResponse(c, newRole)
}

// 更新角色
func updateRole(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var role Role
	
	// 查找角色
	result := db.First(&role, id)
	if result.Error != nil {
		errorResponse(c, 404, "角色不存在")
		return
	}

	// 系统级超级管理员可以更新所有角色
	if user.UserType == "system" && user.Role == "super_admin" {
		// 可以更新所有角色
	} else {
		// 租户级管理员只能更新自己租户的角色
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法更新其他租户的角色")
			return
		}
		
		if role.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权更新此角色")
			return
		}
	}

	// 绑定更新数据
	var updateData Role
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 更新字段
	role.DisplayName = updateData.DisplayName
	role.Description = updateData.Description
	role.Status = updateData.Status

	// 保存更新
	db.Save(&role)
	
	successResponse(c, role)
}

// 删除角色
func deleteRole(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查是否为系统预定义角色
	var role Role
	if err := db.First(&role, id).Error; err != nil {
		errorResponse(c, 404, "角色不存在")
		return
	}

	// 系统级超级管理员可以删除所有角色
	if user.UserType == "system" && user.Role == "super_admin" {
		// 可以删除所有角色
	} else {
		// 租户级管理员只能删除自己租户的角色
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法删除其他租户的角色")
			return
		}
		
		if role.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权删除此角色")
			return
		}
	}

	// 防止删除系统默认角色
	if role.Name == "admin" || role.Name == "manager" || role.Name == "user" {
		errorResponse(c, 400, "不能删除系统默认角色")
		return
	}

	result := db.Delete(&Role{}, id)
	if result.Error != nil {
		errorResponse(c, 500, "删除角色失败")
		return
	}

	if result.RowsAffected == 0 {
		errorResponse(c, 404, "角色不存在")
		return
	}

	successResponse(c, gin.H{"message": "角色删除成功"})
}

// 获取权限列表
func getPermissionList(c *gin.Context) {
	var permissions []Permission
	result := db.Find(&permissions)
	if result.Error != nil {
		errorResponse(c, 500, "获取权限列表失败")
		return
	}

	// 按资源分组
	groupedPermissions := make(map[string][]Permission)
	for _, perm := range permissions {
		groupedPermissions[perm.Resource] = append(groupedPermissions[perm.Resource], perm)
	}

	successResponse(c, gin.H{
		"permissions": permissions,
		"grouped":     groupedPermissions,
		"total":       len(permissions),
	})
}

// 分配角色权限
func assignRolePermissions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var req struct {
		RoleID        uint   `json:"role_id" binding:"required"`
		PermissionIDs []uint `json:"permission_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 查找角色
	var role Role
	if err := db.First(&role, req.RoleID).Error; err != nil {
		errorResponse(c, 404, "角色不存在")
		return
	}

	// 系统级超级管理员可以为所有角色分配权限
	if user.UserType == "system" && user.Role == "super_admin" {
		// 可以分配权限
	} else {
		// 租户级管理员只能为自己的租户角色分配权限
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法为其他租户的角色分配权限")
			return
		}
		
		if role.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权为此角色分配权限")
			return
		}
	}

	// 查找权限
	var permissions []Permission
	if len(req.PermissionIDs) > 0 {
		if err := db.Where("id IN ?", req.PermissionIDs).Find(&permissions).Error; err != nil {
			errorResponse(c, 400, "权限不存在")
			return
		}
	}

	// 清除原有权限关联
	db.Model(&role).Association("Permissions").Clear()
	
	// 分配新权限
	if len(permissions) > 0 {
		db.Model(&role).Association("Permissions").Append(&permissions)
	}

	// 返回更新后的角色信息
	db.Preload("Permissions").First(&role, req.RoleID)
	successResponse(c, gin.H{
		"message": "权限分配成功",
		"role":    role,
	})
}

// 获取用户权限列表
func getUserPermissionsAPI(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未登录")
		return
	}

	// 转换userID为uint
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}

	permissions := getUserPermissions(uid)
	
	successResponse(c, gin.H{
		"permissions": permissions,
		"total":       len(permissions),
	})
}

// 分配用户角色
func assignUserRoles(c *gin.Context) {
	currentUserID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var currentUser User
	if err := db.First(&currentUser, currentUserID).Error; err != nil {
		errorResponse(c, 404, "当前用户不存在")
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		errorResponse(c, 400, "用户ID格式错误")
		return
	}

	var req struct {
		RoleIDs []uint `json:"role_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 查找目标用户
	var targetUser User
	if err := db.First(&targetUser, uint(userID)).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 系统级超级管理员可以为任何用户分配角色
	if currentUser.UserType == "system" && currentUser.Role == "super_admin" {
		// 可以分配角色
	} else {
		// 租户级管理员只能为自己的租户用户分配角色
		if currentUser.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法为其他租户的用户分配角色")
			return
		}
		
		if targetUser.TenantID == nil || *targetUser.TenantID != *currentUser.TenantID {
			errorResponse(c, 403, "无权为此用户分配角色")
			return
		}
	}

	// 查找角色
	var roles []Role
	if len(req.RoleIDs) > 0 {
		if err := db.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
			errorResponse(c, 400, "角色不存在")
			return
		}

		// 验证角色权限
		if currentUser.UserType != "system" || currentUser.Role != "super_admin" {
			// 租户级管理员只能分配自己租户的角色
			for _, role := range roles {
				if role.TenantID != *currentUser.TenantID {
					errorResponse(c, 403, "无权分配其他租户的角色")
					return
				}
			}
		}
	}

	// 清除原有角色关联
	db.Model(&targetUser).Association("Roles").Clear()
	
	// 分配新角色
	if len(roles) > 0 {
		db.Model(&targetUser).Association("Roles").Append(&roles)
	}

	successResponse(c, gin.H{
		"message": "角色分配成功",
		"user_id": userID,
		"roles":   roles,
	})
} 