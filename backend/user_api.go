package main

import (
	"os"
	"time"
	"github.com/gin-gonic/gin"
)

// 更新个人资料
func updateProfile(c *gin.Context) {
	userID, _ := c.Get("user_id")
	
	var user User
	result := db.First(&user, userID)
	if result.Error != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 绑定更新数据
	var updateData struct {
		Email      string `json:"email"`
		RealName   string `json:"real_name"`
		Phone      string `json:"phone"`
		Avatar     string `json:"avatar"`
		Department string `json:"department"`
		Position   string `json:"position"`
		Bio        string `json:"bio"`
	}
	
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 更新个人资料字段（不允许修改用户名、角色等敏感信息）
	user.Email = updateData.Email
	user.RealName = updateData.RealName
	user.Phone = updateData.Phone
	user.Avatar = updateData.Avatar
	user.Department = updateData.Department
	user.Position = updateData.Position
	user.Bio = updateData.Bio

	// 保存更新
	result = db.Save(&user)
	if result.Error != nil {
		errorResponse(c, 500, "更新个人资料失败")
		return
	}
	
	// 不返回密码
	user.Password = ""
	successResponse(c, user)
}

// 获取用户列表
func getUserList(c *gin.Context) {
	// 从JWT token中获取当前用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "User not authenticated")
		return
	}

	var currentUser User
	if err := db.Preload("Tenant").First(&currentUser, userID).Error; err != nil {
		errorResponse(c, 404, "Current user not found")
		return
	}

	var users []User

	// 系统级超级管理员可以查看所有用户
	if currentUser.UserType == "system" && currentUser.Role == "super_admin" {
		// 排除具有客服角色的用户
		result := db.Preload("Roles").Preload("Tenant").
			Where("id NOT IN (?)",
				db.Table("user_roles").
					Select("user_id").
					Joins("JOIN roles ON user_roles.role_id = roles.id").
					Where("roles.name = ?", "customer_service")).
			Find(&users)
		if result.Error != nil {
			errorResponse(c, 500, "获取用户列表失败")
			return
		}
	} else {
		// 租户级管理员只能查看自己租户的用户
		if currentUser.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法查看其他租户的用户")
			return
		}

		// 排除具有客服角色的用户
		result := db.Preload("Roles").Preload("Tenant").
			Where("tenant_id = ?", currentUser.TenantID).
			Where("id NOT IN (?)",
				db.Table("user_roles").
					Select("user_id").
					Joins("JOIN roles ON user_roles.role_id = roles.id").
					Where("roles.name = ?", "customer_service")).
			Find(&users)
		if result.Error != nil {
			errorResponse(c, 500, "获取用户列表失败")
			return
		}
	}

	successResponse(c, gin.H{
		"users": users,
		"total": len(users),
	})
}

// 根据ID获取用户
func getUserById(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var currentUser User
	if err := db.First(&currentUser, userID).Error; err != nil {
		errorResponse(c, 404, "当前用户不存在")
		return
	}

	var user User
	result := db.First(&user, id)
	if result.Error != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 系统级超级管理员可以查看所有用户
	if currentUser.UserType == "system" && currentUser.Role == "super_admin" {
		// 可以查看所有用户
	} else {
		// 租户级管理员只能查看自己租户的用户
		if currentUser.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法查看其他租户的用户")
			return
		}
		
		if user.TenantID == nil || *user.TenantID != *currentUser.TenantID {
			errorResponse(c, 403, "无权查看此用户")
			return
		}
	}

	successResponse(c, user)
}

// 创建用户
func createUser(c *gin.Context) {
	// 从JWT token中获取当前用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "User not authenticated")
		return
	}

	var currentUser User
	if err := db.Preload("Tenant").First(&currentUser, userID).Error; err != nil {
		errorResponse(c, 404, "Current user not found")
		return
	}

	var newUser User
	if err := c.ShouldBindJSON(&newUser); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 系统级超级管理员可以为任何租户创建用户
	if currentUser.UserType == "system" && currentUser.Role == "super_admin" {
		// 需要指定租户ID
		if newUser.TenantID == nil {
			errorResponse(c, 400, "系统级超级管理员创建用户时必须指定租户ID")
			return
		}
	} else {
		// 租户级管理员只能在自己的租户中创建用户
		if currentUser.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法在其他租户中创建用户")
			return
		}
		
		// 强制设置为当前用户的租户ID
		newUser.TenantID = currentUser.TenantID
	}

	// 检查用户名是否存在（在同一租户内）
	var existingUser User
	if err := db.Where("(username = ? OR email = ?) AND tenant_id = ?", newUser.Username, newUser.Email, newUser.TenantID).First(&existingUser).Error; err == nil {
		errorResponse(c, 400, "用户名或邮箱已存在")
		return
	}

	// 加密密码
	if newUser.Password != "" {
		hashedPassword, err := hashPassword(newUser.Password)
		if err != nil {
			errorResponse(c, 500, "密码加密失败")
			return
		}
		newUser.Password = hashedPassword
	}

	result := db.Create(&newUser)
	if result.Error != nil {
		errorResponse(c, 500, "创建用户失败")
		return
	}

	// 不返回密码
	newUser.Password = ""
	successResponse(c, newUser)
}

// 更新用户
func updateUser(c *gin.Context) {
	id := c.Param("id")
	var user User
	
	// 查找用户
	result := db.First(&user, id)
	if result.Error != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 保存原密码
	oldPassword := user.Password

	// 绑定更新数据
	var updateData User
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 更新基本字段
	user.Email = updateData.Email
	user.Role = updateData.Role
	user.Status = updateData.Status
	
	// 更新个人资料字段
	user.RealName = updateData.RealName
	user.Phone = updateData.Phone
	user.Avatar = updateData.Avatar
	user.Department = updateData.Department
	user.Position = updateData.Position
	user.Bio = updateData.Bio

	// 处理密码更新
	if updateData.Password != "" {
		hashedPassword, err := hashPassword(updateData.Password)
		if err != nil {
			errorResponse(c, 500, "密码加密失败")
			return
		}
		user.Password = hashedPassword
	} else {
		// 如果没有提供新密码，保持原密码
		user.Password = oldPassword
	}

	// 保存更新
	result = db.Save(&user)
	if result.Error != nil {
		errorResponse(c, 500, "更新用户失败")
		return
	}
	
	// 不返回密码
	user.Password = ""
	successResponse(c, user)
}

// 删除用户
func deleteUser(c *gin.Context) {
	id := c.Param("id")
	result := db.Delete(&User{}, id)
	if result.Error != nil {
		errorResponse(c, 500, "删除用户失败")
		return
	}

	if result.RowsAffected == 0 {
		errorResponse(c, 404, "用户不存在")
		return
	}

	successResponse(c, gin.H{"message": "用户删除成功"})
}

// 获取系统信息
func getSystemInfo(c *gin.Context) {
	var userCount int64
	db.Model(&User{}).Count(&userCount)

	successResponse(c, gin.H{
		"user_count":    userCount,
		"database_type": "SQLite",
		"version":       "v1.0.0",
		"uptime":        time.Now().Unix(),
	})
}

// 系统监控API
func getSystemMonitor(c *gin.Context) {
	var userCount, activeUserCount, todayLoginCount, logCount, todayLogCount int64
	var dbSize int64

	// 用户总数
	db.Model(&User{}).Count(&userCount)
	// 活跃用户（最近7天登录）
	db.Raw("SELECT COUNT(*) FROM users WHERE last_login >= date('now', '-7 day')").Scan(&activeUserCount)
	// 今日登录用户
	db.Raw("SELECT COUNT(*) FROM users WHERE last_login >= date('now', 'start of day')").Scan(&todayLoginCount)
	// 操作日志总数
	db.Model(&OperationLog{}).Count(&logCount)
	// 今日操作日志
	db.Raw("SELECT COUNT(*) FROM operation_logs WHERE created_at >= date('now', 'start of day')").Scan(&todayLogCount)
	// 数据库文件大小
	if info, err := os.Stat("hive.db"); err == nil {
		dbSize = info.Size()
	}

	successResponse(c, gin.H{
		"user_count": userCount,
		"active_user_count": activeUserCount,
		"today_login_count": todayLoginCount,
		"log_count": logCount,
		"today_log_count": todayLogCount,
		"db_size": dbSize,
		"server_time": time.Now().Format("2006-01-02 15:04:05"),
		"uptime": time.Since(startTime).Seconds(),
	})
}

var startTime = time.Now() 