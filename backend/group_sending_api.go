package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 群发任务请求结构
type CreateGroupSendingTaskRequest struct {
	TaskName          string    `json:"task_name" binding:"required"`
	AccountUsage      string    `json:"account_usage" binding:"required"` // auto, manual
	SelectedAccountID *uint     `json:"selected_account_id"`
	CustomerFile      string    `json:"customer_file" binding:"required"`
	CustomerInterval  int       `json:"customer_interval" binding:"required"`
	SendingTimes      int       `json:"sending_times" binding:"required"`
	SendingTimesType  string    `json:"sending_times_type" binding:"required"` // limited, unlimited
	SendingMethod     string    `json:"sending_method" binding:"required"`     // one_by_one, after_reply
	SentenceInterval  int       `json:"sentence_interval" binding:"required"`
	ScheduledTime     *time.Time `json:"scheduled_time"`
	NumberDetection   bool      `json:"number_detection"`
	Statements        string    `json:"statements" binding:"required"` // JSON格式的语句配置
}

// 群发任务响应结构
type GroupSendingTaskResponse struct {
	ID                uint      `json:"id"`
	TaskName          string    `json:"task_name"`
	TaskID            string    `json:"task_id"`
	Status            string    `json:"status"`
	Progress          float64   `json:"progress"`
	CustomerCount     int       `json:"customer_count"`
	ReachedCount      int       `json:"reached_count"`
	SentMessages      int       `json:"sent_messages"`
	FailedMessages    int       `json:"failed_messages"`
	PendingMessages   int       `json:"pending_messages"`
	AvailableMessages int       `json:"available_messages"`
	StartTime         *time.Time `json:"start_time"`
	EndTime           *time.Time `json:"end_time"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// 批量操作请求结构
type BatchOperationRequest struct {
	TaskIDs []string `json:"task_ids" binding:"required"`
}

// 创建群发任务
func createGroupSendingTaskAPI(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户未认证",
		})
		return
	}

	var req CreateGroupSendingTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	// 生成任务ID
	taskID := fmt.Sprintf("task_%d_%d", user.TenantID, time.Now().Unix())

	// 解析客户文件，获取客户数量
	customers, err := parseCustomerFile(req.CustomerFile)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "客户文件解析失败: " + err.Error(),
		})
		return
	}

	// 创建群发任务
	task := GroupSendingTask{
		TenantID:          *user.TenantID,
		TaskName:          req.TaskName,
		TaskID:            taskID,
		CreatorID:         user.ID,
		CreatorName:       user.RealName,
		AccountUsage:      req.AccountUsage,
		SelectedAccountID: req.SelectedAccountID,
		CustomerFile:      req.CustomerFile,
		CustomerCount:     len(customers),
		CustomerInterval:  req.CustomerInterval,
		SendingTimes:      req.SendingTimes,
		SendingTimesType:  req.SendingTimesType,
		SendingMethod:     req.SendingMethod,
		SentenceInterval:  req.SentenceInterval,
		ScheduledTime:     req.ScheduledTime,
		NumberDetection:   req.NumberDetection,
		Statements:        req.Statements,
		Status:            "pending",
		Progress:          0,
		PendingMessages:   len(customers) * req.SendingTimes,
		AvailableMessages: len(customers) * req.SendingTimes,
		SentMessages:      0,
		FailedMessages:    0,
	}

	if err := db.Create(&task).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建群发任务失败",
		})
		return
	}

	// 解析语句配置并保存
	var statements []map[string]interface{}
	if err := json.Unmarshal([]byte(req.Statements), &statements); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "语句配置格式错误",
		})
		return
	}

	// 保存语句项
	for i, stmt := range statements {
		statement := GroupSendingStatement{
			TaskID:       task.ID,
			Order:        i + 1,
			Type:         stmt["type"].(string),
			MaterialName: stmt["material_name"].(string),
			Content:      stmt["content"].(string),
			FileURL:      stmt["file_url"].(string),
		}
		if duration, ok := stmt["duration"].(float64); ok {
			statement.Duration = int(duration)
		}

		if err := db.Create(&statement).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "保存语句配置失败",
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "群发任务创建成功",
		"data":    task,
	})
}

// 启动群发任务
func startGroupSendingTaskAPI(c *gin.Context) {
	_, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户未认证",
		})
		return
	}

	taskID := c.Param("id")
	var task GroupSendingTask

	// 获取任务信息
	if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务状态
	if task.Status != "pending" && task.Status != "paused" && task.Status != "terminated" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "任务状态不允许启动",
		})
		return
	}

	// 检查账号状态
	var account WhatsAppAccount
	if task.AccountUsage == "manual" && task.SelectedAccountID != nil {
		if err := db.First(&account, *task.SelectedAccountID).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "选择的账号不存在",
			})
			return
		}
		if account.ConnectionStatus != "connected" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "选择的账号未连接",
			})
			return
		}
	} else {
		// 自动选择账号
		if err := db.Where("tenant_id = ? AND connection_status = ?", task.TenantID, "connected").First(&account).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "没有可用的连接账号",
			})
			return
		}
		task.SelectedAccountID = &account.ID
	}

	// 更新任务状态
	now := time.Now()
	task.Status = "running"
	task.StartTime = &now
	task.Progress = 0

	if err := db.Save(&task).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "启动任务失败",
		})
		return
	}

	// 启动群发任务
	go executeGroupSendingTask(task.ID, account.ID)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "群发任务启动成功",
		"data":    task,
	})
}

// 暂停群发任务
func pauseGroupSendingTaskAPI(c *gin.Context) {
	taskID := c.Param("id")
	var task GroupSendingTask

	if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	if task.Status != "running" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "任务状态不允许暂停",
		})
		return
	}

	task.Status = "paused"
	if err := db.Save(&task).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "暂停任务失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "群发任务暂停成功",
		"data":    task,
	})
}

// 停止群发任务
func stopGroupSendingTaskAPI(c *gin.Context) {
	taskID := c.Param("id")
	var task GroupSendingTask

	if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	if task.Status == "completed" || task.Status == "terminated" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "任务已完成或已终止",
		})
		return
	}

	now := time.Now()
	task.Status = "terminated"
	task.EndTime = &now

	if err := db.Save(&task).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "停止任务失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "群发任务停止成功",
		"data":    task,
	})
}

// 获取群发任务列表
func getGroupSendingTasksAPI(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户未认证",
		})
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	offset := (page - 1) * size

	var tasks []GroupSendingTask
	var total int64

	query := db.Where("tenant_id = ?", user.TenantID)
	query.Model(&GroupSendingTask{}).Count(&total)
	query.Offset(offset).Limit(size).Order("created_at DESC").Find(&tasks)

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"tasks": tasks,
			"total": total,
			"page":  page,
			"size":  size,
		},
	})
}

// 获取群发任务详情
func getGroupSendingTaskDetailAPI(c *gin.Context) {
	taskID := c.Param("id")
	var task GroupSendingTask

	if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 获取语句配置
	var statements []GroupSendingStatement
	db.Where("task_id = ?", task.ID).Order("`order`").Find(&statements)

	// 获取发送日志
	var logs []GroupSendingLog
	db.Where("task_id = ?", task.ID).Order("created_at DESC").Limit(100).Find(&logs)

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"task":       task,
			"statements": statements,
			"logs":       logs,
		},
	})
}

// 解析客户文件
func parseCustomerFile(filePath string) ([]string, error) {
	// 检查文件是否存在
	if filePath == "" {
		return nil, fmt.Errorf("文件路径为空")
	}

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 按行分割内容
	lines := strings.Split(string(content), "\n")
	var phoneNumbers []string

	for i, line := range lines {
		// 去除空白字符
		line = strings.TrimSpace(line)
		
		// 跳过空行
		if line == "" {
			continue
		}

		// 验证手机号格式（简单验证）
		if !isValidPhoneNumber(line) {
			fmt.Printf("警告: 第%d行手机号格式不正确: %s\n", i+1, line)
			continue
		}

		phoneNumbers = append(phoneNumbers, line)
	}

	if len(phoneNumbers) == 0 {
		return nil, fmt.Errorf("文件中没有找到有效的手机号")
	}

	fmt.Printf("成功解析客户文件: %s，共%d个手机号\n", filePath, len(phoneNumbers))
	return phoneNumbers, nil
}

// 验证手机号格式
func isValidPhoneNumber(phone string) bool {
	// 移除所有非数字字符
	cleaned := strings.ReplaceAll(phone, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")
	cleaned = strings.ReplaceAll(cleaned, "+", "")
	
	// 检查是否全为数字
	for _, char := range cleaned {
		if char < '0' || char > '9' {
			return false
		}
	}
	
	// 检查长度（支持国际号码格式）
	length := len(cleaned)
	if length < 10 || length > 15 {
		return false
	}
	
	return true
}

// 执行群发任务
func executeGroupSendingTask(taskID uint, accountID uint) {
	var task GroupSendingTask
	if err := db.First(&task, taskID).Error; err != nil {
		fmt.Printf("任务不存在: %d\n", taskID)
		return
	}

	// 获取客户列表
	customers, err := parseCustomerFile(task.CustomerFile)
	if err != nil {
		fmt.Printf("解析客户文件失败: %v\n", err)
		return
	}

	// 获取语句配置
	var statements []GroupSendingStatement
	db.Where("task_id = ?", taskID).Order("`order`").Find(&statements)

	// 获取账号信息
	var account WhatsAppAccount
	if err := db.First(&account, accountID).Error; err != nil {
		fmt.Printf("账号不存在: %d\n", accountID)
		return
	}

	// 开始发送消息
	for i, customer := range customers {
		// 检查任务状态
		if err := db.First(&task, taskID).Error; err != nil {
			fmt.Printf("任务不存在: %d\n", taskID)
			return
		}

		if task.Status == "paused" || task.Status == "terminated" {
			fmt.Printf("任务已暂停或终止: %s\n", task.Status)
			return
		}

		// 发送消息给每个客户
		for j := 0; j < task.SendingTimes; j++ {
			for _, stmt := range statements {
				// 调用Node服务发送消息
				success, messageID, errorMsg := sendMessageViaNodeService(accountID, customer, stmt.Type, stmt.Content, stmt.FileURL)

				// 记录发送日志
				now := time.Now()
				log := GroupSendingLog{
					TaskID:      taskID,
					AccountID:   accountID,
					PhoneNumber: customer,
					Message:     stmt.Content,
					Status:      "success",
					ErrorMsg:    "",
					SentAt:      &now,
				}

				if !success {
					log.Status = "failed"
					log.ErrorMsg = errorMsg
				} else {
					// 使用messageID记录成功发送的消息ID
					log.ErrorMsg = "MessageID: " + messageID
				}

				db.Create(&log)

				// 更新任务统计
				if success {
					task.SentMessages++
				} else {
					task.FailedMessages++
				}
				task.ReachedCount = i + 1
				task.Progress = float64(i+1) / float64(len(customers)) * 100
				db.Save(&task)

				// 语句间隔
				time.Sleep(time.Duration(task.SentenceInterval) * time.Second)
			}

			// 发送次数间隔
			if j < task.SendingTimes-1 {
				time.Sleep(time.Duration(task.CustomerInterval) * time.Second)
			}
		}

		// 客户间隔
		if i < len(customers)-1 {
			time.Sleep(time.Duration(task.CustomerInterval) * time.Second)
		}
	}

	// 任务完成
	now := time.Now()
	task.Status = "completed"
	task.EndTime = &now
	task.Progress = 100
	db.Save(&task)

	fmt.Printf("群发任务完成: %d\n", taskID)
}

// 检查用户是否有权限操作群发任务
func canUserOperateTask(user User, task GroupSendingTask) bool {
	// 1. 创建者可以操作自己的任务
	if task.CreatorID == user.ID {
		return true
	}
	
	// 2. 系统管理员可以操作任何任务
	if user.UserType == "system" && user.Role == "super_admin" {
		return true
	}
	
	// 3. 租户管理员可以操作同租户的任务
	if user.TenantID != nil && *user.TenantID == task.TenantID && (user.Role == "admin" || user.Role == "manager") {
		return true
	}
	
	return false
}

// 通过Node服务发送消息
func sendMessageViaNodeService(accountID uint, phoneNumber, messageType, content, fileURL string) (bool, string, string) {
	// 构建请求数据
	requestData := map[string]interface{}{
		"account_id":   accountID,
		"phone_number": phoneNumber,
		"message_type": messageType,
		"message":      content,
		"file_path":    fileURL,
	}

	// 发送请求到Node服务
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return false, "", "序列化请求数据失败"
	}

	resp, err := http.Post("http://localhost:3000/api/whatsapp/send-message", "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return false, "", "连接Node服务失败"
	}
	defer resp.Body.Close()

	// 读取响应
	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return false, "", "解析响应失败"
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		errorMsg := "发送失败"
		if msg, ok := response["message"].(string); ok {
			errorMsg = msg
		}
		return false, "", errorMsg
	}

	// 提取消息ID
	var messageID string
	if data, ok := response["data"].(map[string]interface{}); ok {
		if id, ok := data["message_id"].(string); ok {
			messageID = id
		}
	}

	return true, messageID, ""
}

// 删除群发任务
func deleteGroupSendingTaskAPI(c *gin.Context) {
	taskID := c.Param("id")
	
	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}
	
	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}
	
	var task GroupSendingTask
	if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
		errorResponse(c, 404, "任务不存在")
		return
	}
	
	// 获取用户信息以检查权限
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}
	
	// 检查权限：创建者可以删除，或者系统管理员/租户管理员可以删除同租户的任务
	canDelete := false
	
	// 1. 创建者可以删除自己的任务
	if task.CreatorID == uid {
		canDelete = true
	}
	
	// 2. 系统管理员可以删除任何任务
	if user.UserType == "system" && user.Role == "super_admin" {
		canDelete = true
	}
	
	// 3. 租户管理员可以删除同租户的任务
	if user.TenantID != nil && *user.TenantID == task.TenantID && (user.Role == "admin" || user.Role == "manager") {
		canDelete = true
	}
	
	if !canDelete {
		errorResponse(c, 403, "无权限删除此任务")
		return
	}
	
	// 如果任务正在运行，先终止它
	if task.Status == "running" {
		task.Status = "terminated"
		task.UpdatedAt = time.Now()
		db.Save(&task)
	}
	
	// 删除相关的语句和日志
	db.Where("task_id = ?", task.ID).Delete(&GroupSendingStatement{})
	db.Where("task_id = ?", task.ID).Delete(&GroupSendingLog{})
	
	// 删除任务
	if err := db.Delete(&task).Error; err != nil {
		errorResponse(c, 500, "删除任务失败")
		return
	}
	
	successResponse(c, gin.H{
		"message": "任务删除成功",
	})
}

// 重新开始群发任务
func restartGroupSendingTaskAPI(c *gin.Context) {
	taskID := c.Param("id")
	
	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}
	
	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}
	
	var task GroupSendingTask
	if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
		errorResponse(c, 404, "任务不存在")
		return
	}
	
	// 获取用户信息以检查权限
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}
	
	// 检查权限：创建者可以操作，或者系统管理员/租户管理员可以操作同租户的任务
	canOperate := false
	
	// 1. 创建者可以操作自己的任务
	if task.CreatorID == uid {
		canOperate = true
	}
	
	// 2. 系统管理员可以操作任何任务
	if user.UserType == "system" && user.Role == "super_admin" {
		canOperate = true
	}
	
	// 3. 租户管理员可以操作同租户的任务
	if user.TenantID != nil && *user.TenantID == task.TenantID && (user.Role == "admin" || user.Role == "manager") {
		canOperate = true
	}
	
	if !canOperate {
		errorResponse(c, 403, "无权限操作此任务")
		return
	}
	
	// 检查任务状态
	if task.Status != "completed" && task.Status != "terminated" {
		errorResponse(c, 400, "只有已完成或已终止的任务可以重新开始")
		return
	}
	
	// 重置任务状态
	task.Status = "pending"
	task.Progress = 0
	task.ReachedCount = 0
	task.SentMessages = 0
	task.FailedMessages = 0
	task.StartTime = nil
	task.EndTime = nil
	task.UpdatedAt = time.Now()
	
	if err := db.Save(&task).Error; err != nil {
		errorResponse(c, 500, "重新开始任务失败")
		return
	}
	
	// 清除之前的日志
	db.Where("task_id = ?", task.ID).Delete(&GroupSendingLog{})
	
	successResponse(c, gin.H{
		"message": "任务重新开始成功",
		"task":    task,
	})
} 

// 批量启动群发任务
func batchStartGroupSendingTasksAPI(c *gin.Context) {
	var req BatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	successCount := 0
	failedCount := 0
	errors := []string{}

	for _, taskID := range req.TaskIDs {
		var task GroupSendingTask
		if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 不存在", taskID))
			failedCount++
			continue
		}

		// 检查权限
		if !canUserOperateTask(user, task) {
			errors = append(errors, fmt.Sprintf("任务 %s 无权限操作", taskID))
			failedCount++
			continue
		}

		// 检查状态
		if task.Status != "pending" && task.Status != "paused" {
			errors = append(errors, fmt.Sprintf("任务 %s 状态不允许启动", taskID))
			failedCount++
			continue
		}

		// 检查账号状态
		var account WhatsAppAccount
		if err := db.First(&account, task.SelectedAccountID).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 关联的账号不存在", taskID))
			failedCount++
			continue
		}

		if account.ConnectionStatus != "connected" {
			errors = append(errors, fmt.Sprintf("任务 %s 关联的账号未连接", taskID))
			failedCount++
			continue
		}

		// 启动任务
		task.Status = "running"
		task.StartTime = &time.Time{}
		task.UpdatedAt = time.Now()
		
		if err := db.Save(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 启动失败", taskID))
			failedCount++
			continue
		}

		// 异步执行任务
		if task.SelectedAccountID != nil {
			go executeGroupSendingTask(task.ID, *task.SelectedAccountID)
		}
		successCount++
	}

	successResponse(c, gin.H{
		"message": fmt.Sprintf("批量启动完成: 成功 %d 个, 失败 %d 个", successCount, failedCount),
		"success_count": successCount,
		"failed_count": failedCount,
		"errors": errors,
	})
}

// 批量暂停群发任务
func batchPauseGroupSendingTasksAPI(c *gin.Context) {
	var req BatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	successCount := 0
	failedCount := 0
	errors := []string{}

	for _, taskID := range req.TaskIDs {
		var task GroupSendingTask
		if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 不存在", taskID))
			failedCount++
			continue
		}

		// 检查权限
		if !canUserOperateTask(user, task) {
			errors = append(errors, fmt.Sprintf("任务 %s 无权限操作", taskID))
			failedCount++
			continue
		}

		// 检查状态
		if task.Status != "running" {
			errors = append(errors, fmt.Sprintf("任务 %s 状态不允许暂停", taskID))
			failedCount++
			continue
		}

		// 暂停任务
		task.Status = "paused"
		task.UpdatedAt = time.Now()
		
		if err := db.Save(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 暂停失败", taskID))
			failedCount++
			continue
		}

		successCount++
	}

	successResponse(c, gin.H{
		"message": fmt.Sprintf("批量暂停完成: 成功 %d 个, 失败 %d 个", successCount, failedCount),
		"success_count": successCount,
		"failed_count": failedCount,
		"errors": errors,
	})
}

// 批量终止群发任务
func batchTerminateGroupSendingTasksAPI(c *gin.Context) {
	var req BatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	successCount := 0
	failedCount := 0
	errors := []string{}

	for _, taskID := range req.TaskIDs {
		var task GroupSendingTask
		if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 不存在", taskID))
			failedCount++
			continue
		}

		// 检查权限
		if !canUserOperateTask(user, task) {
			errors = append(errors, fmt.Sprintf("任务 %s 无权限操作", taskID))
			failedCount++
			continue
		}

		// 检查状态
		if task.Status != "running" && task.Status != "paused" {
			errors = append(errors, fmt.Sprintf("任务 %s 状态不允许终止", taskID))
			failedCount++
			continue
		}

		// 终止任务
		task.Status = "terminated"
		now := time.Now()
		task.EndTime = &now
		task.UpdatedAt = time.Now()
		
		if err := db.Save(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 终止失败", taskID))
			failedCount++
			continue
		}

		successCount++
	}

	successResponse(c, gin.H{
		"message": fmt.Sprintf("批量终止完成: 成功 %d 个, 失败 %d 个", successCount, failedCount),
		"success_count": successCount,
		"failed_count": failedCount,
		"errors": errors,
	})
}

// 批量重新开始群发任务
func batchRestartGroupSendingTasksAPI(c *gin.Context) {
	var req BatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	successCount := 0
	failedCount := 0
	errors := []string{}

	for _, taskID := range req.TaskIDs {
		var task GroupSendingTask
		if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 不存在", taskID))
			failedCount++
			continue
		}

		// 检查权限
		if !canUserOperateTask(user, task) {
			errors = append(errors, fmt.Sprintf("任务 %s 无权限操作", taskID))
			failedCount++
			continue
		}

		// 检查状态
		if task.Status != "completed" && task.Status != "terminated" {
			errors = append(errors, fmt.Sprintf("任务 %s 状态不允许重新开始", taskID))
			failedCount++
			continue
		}

		// 重置任务状态
		task.Status = "pending"
		task.Progress = 0
		task.ReachedCount = 0
		task.SentMessages = 0
		task.FailedMessages = 0
		task.StartTime = nil
		task.EndTime = nil
		task.UpdatedAt = time.Now()
		
		if err := db.Save(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 重新开始失败", taskID))
			failedCount++
			continue
		}

		// 清除之前的日志
		db.Where("task_id = ?", task.ID).Delete(&GroupSendingLog{})

		successCount++
	}

	successResponse(c, gin.H{
		"message": fmt.Sprintf("批量重新开始完成: 成功 %d 个, 失败 %d 个", successCount, failedCount),
		"success_count": successCount,
		"failed_count": failedCount,
		"errors": errors,
	})
}

// 批量删除群发任务
func batchDeleteGroupSendingTasksAPI(c *gin.Context) {
	var req BatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	// 转换userID为uint类型
	uid, ok := userID.(uint)
	if !ok {
		errorResponse(c, 500, "用户ID类型错误")
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, uid).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	successCount := 0
	failedCount := 0
	errors := []string{}

	for _, taskID := range req.TaskIDs {
		var task GroupSendingTask
		if err := db.Where("task_id = ?", taskID).First(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 不存在", taskID))
			failedCount++
			continue
		}

		// 检查权限
		if !canUserOperateTask(user, task) {
			errors = append(errors, fmt.Sprintf("任务 %s 无权限删除", taskID))
			failedCount++
			continue
		}

		// 如果任务正在运行，先终止它
		if task.Status == "running" {
			task.Status = "terminated"
			task.UpdatedAt = time.Now()
			db.Save(&task)
		}

		// 删除相关的语句和日志
		db.Where("task_id = ?", task.ID).Delete(&GroupSendingStatement{})
		db.Where("task_id = ?", task.ID).Delete(&GroupSendingLog{})
		
		// 删除任务
		if err := db.Delete(&task).Error; err != nil {
			errors = append(errors, fmt.Sprintf("任务 %s 删除失败", taskID))
			failedCount++
			continue
		}

		successCount++
	}

	successResponse(c, gin.H{
		"message": fmt.Sprintf("批量删除完成: 成功 %d 个, 失败 %d 个", successCount, failedCount),
		"success_count": successCount,
		"failed_count": failedCount,
		"errors": errors,
	})
} 

// 更新群发任务
func updateGroupSendingTaskAPI(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "用户未认证",
		})
		return
	}

	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "任务ID不能为空",
		})
		return
	}

	var req CreateGroupSendingTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取用户信息
	var user User
	if err := db.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	// 查找任务
	var task GroupSendingTask
	if err := db.Where("task_id = ? AND tenant_id = ?", taskID, *user.TenantID).First(&task).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务状态，只有pending状态的任务可以编辑
	if task.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "只有待开始状态的任务可以编辑",
		})
		return
	}

	// 解析客户文件，获取客户数量
	customers, err := parseCustomerFile(req.CustomerFile)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "客户文件解析失败: " + err.Error(),
		})
		return
	}

	// 更新任务
	updates := map[string]interface{}{
		"task_name":           req.TaskName,
		"account_usage":       req.AccountUsage,
		"selected_account_id": req.SelectedAccountID,
		"customer_file":       req.CustomerFile,
		"customer_count":      len(customers),
		"customer_interval":   req.CustomerInterval,
		"sending_times":       req.SendingTimes,
		"sending_times_type":  req.SendingTimesType,
		"sending_method":      req.SendingMethod,
		"sentence_interval":   req.SentenceInterval,
		"scheduled_time":      req.ScheduledTime,
		"number_detection":    req.NumberDetection,
		"statements":          req.Statements,
		"pending_messages":    len(customers),
		"available_messages":  len(customers),
		"updated_at":          time.Now(),
	}

	if err := db.Model(&task).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新任务失败: " + err.Error(),
		})
		return
	}

	// 获取更新后的任务详情
	var updatedTask GroupSendingTask
	if err := db.Preload("Tenant").Preload("Creator").Preload("SelectedAccount").First(&updatedTask, task.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取更新后的任务失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "群发任务更新成功",
		"data":    updatedTask,
	})
} 