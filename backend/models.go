package main

import (
	"fmt"
	"gorm.io/gorm"
	"time"
)

// 租户模型
type Tenant struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Domain      string    `json:"domain" gorm:"unique"`
	PlanType    string    `json:"plan_type" gorm:"default:basic"` // basic, professional, enterprise
	MaxAccounts int       `json:"max_accounts" gorm:"default:1"`
	MaxStorage  int64     `json:"max_storage" gorm:"default:**********"` // 1GB
	Status      string    `json:"status" gorm:"default:active"` // active, suspended, cancelled
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// 角色模型
type Role struct {
	ID          uint         `json:"id" gorm:"primaryKey"`
	TenantID    uint         `json:"tenant_id" gorm:"not null"` // 添加租户ID
	Name        string       `json:"name" gorm:"not null"`
	DisplayName string       `json:"display_name" gorm:"not null"`
	Description string       `json:"description"`
	Status      bool         `json:"status" gorm:"default:true"`
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	Tenant      Tenant       `json:"tenant" gorm:"foreignKey:TenantID"`
	gorm.Model
}

// 权限模型
type Permission struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"unique;not null"`
	DisplayName string `json:"display_name" gorm:"not null"`
	Resource    string `json:"resource" gorm:"not null"` // 资源名称，如 user, role, system
	Action      string `json:"action" gorm:"not null"`   // 操作名称，如 read, write, delete
	Description string `json:"description"`
	Roles       []Role `json:"roles" gorm:"many2many:role_permissions;"`
	gorm.Model
}

// 用户角色关联模型
type UserRole struct {
	ID     uint `json:"id" gorm:"primaryKey"`
	UserID uint `json:"user_id" gorm:"not null"`
	RoleID uint `json:"role_id" gorm:"not null"`
	User   User `json:"user" gorm:"foreignKey:UserID"`
	Role   Role `json:"role" gorm:"foreignKey:RoleID"`
	gorm.Model
}

// 更新用户模型，支持多角色和多租户
type User struct {
	ID         uint       `json:"id" gorm:"primaryKey"`
	TenantID   *uint      `json:"tenant_id"` // 允许为空，超级管理员可以不属于任何租户
	Username   string     `json:"username" gorm:"unique;not null"`
	Email      string     `json:"email" gorm:"unique;not null"`
	Password   string     `json:"-" gorm:"not null"` // 密码不返回到前端
	Role       string     `json:"role" gorm:"default:user"` // 保留兼容性
	UserType   string     `json:"user_type" gorm:"default:tenant"` // tenant: 租户用户, system: 系统用户
	Status     bool       `json:"status" gorm:"default:true"`

	// 个人资料字段
	RealName   string     `json:"real_name"`   // 真实姓名
	Phone      string     `json:"phone"`       // 手机号码
	Avatar     string     `json:"avatar"`      // 头像URL
	Department string     `json:"department"`  // 部门
	Position   string     `json:"position"`    // 职位
	Bio        string     `json:"bio"`         // 个人简介
	LastLogin  *time.Time `json:"last_login"`  // 最后登录时间

	Tenant     *Tenant    `json:"tenant" gorm:"foreignKey:TenantID"`
	Roles      []Role     `json:"roles" gorm:"many2many:user_roles;"`
	gorm.Model
}

// 客服档案模型 - 存储客服特有的字段，一对一关联User
type CustomerServiceProfile struct {
	ID                    uint                   `json:"id" gorm:"primaryKey"`
	UserID                uint                   `json:"user_id" gorm:"unique;not null;index"` // 关联用户ID
	GroupID               *uint                  `json:"group_id" gorm:"index"`                 // 所属分组
	ServiceType           string                 `json:"service_type" gorm:"default:regular"`   // regular: 普通客服, senior: 高级客服
	DailyAssignLimit      int                    `json:"daily_assign_limit" gorm:"default:50"`  // 每日自动分配上限
	IsAssignNewSession    bool                   `json:"is_assign_new_session" gorm:"default:true"` // 是否分配新会话
	HideCustomerWSNumber  bool                   `json:"hide_customer_ws_number" gorm:"default:false"` // 隐藏客户WS号
	IsCustomerGroupSend   bool                   `json:"is_customer_group_send" gorm:"default:false"` // 是否客户群发
	DisabledAt            *time.Time             `json:"disabled_at"`                           // 停用时间
	DisabledReason        string                 `json:"disabled_reason"`                       // 停用原因

	// 关联关系
	User                  User                   `json:"user" gorm:"foreignKey:UserID"`
	Group                 *CustomerServiceGroup  `json:"group" gorm:"foreignKey:GroupID"`

	gorm.Model
}

// 客服分组模型
type CustomerServiceGroup struct {
	ID                    uint                      `json:"id" gorm:"primaryKey"`
	TenantID              uint                      `json:"tenant_id" gorm:"not null;index"`
	GroupName             string                    `json:"group_name" gorm:"not null"`
	Description           string                    `json:"description"`
	DailyAssignLimit      int                       `json:"daily_assign_limit" gorm:"default:100"` // 每日自动分配上限
	IsAssignNewSession    bool                      `json:"is_assign_new_session" gorm:"default:true"` // 是否分配新会话
	ServiceCount          int                       `json:"service_count" gorm:"default:0"`        // 客服数量

	// 关联关系
	Tenant                *Tenant                   `json:"tenant" gorm:"foreignKey:TenantID"`
	CustomerServices      []CustomerServiceProfile  `json:"customer_services" gorm:"foreignKey:GroupID"`

	gorm.Model
}

// 初始化租户系统数据
func initTenantSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&Tenant{})
	if err != nil {
		return err
	}

	// 创建多个测试租户
	testTenants := []Tenant{
		{
			Name:        "Hive SaaS",
			Domain:      "hive-saas.com",
			PlanType:    "enterprise",
			MaxAccounts: 10,
			MaxStorage:  **********0, // 10GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "测试公司A",
			Domain:      "company-a.com",
			PlanType:    "professional",
			MaxAccounts: 5,
			MaxStorage:  **********, // 5GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "测试公司B",
			Domain:      "company-b.com",
			PlanType:    "basic",
			MaxAccounts: 3,
			MaxStorage:  **********, // 1GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "创业公司C",
			Domain:      "startup-c.com",
			PlanType:    "professional",
			MaxAccounts: 8,
			MaxStorage:  **********, // 8GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "企业集团D",
			Domain:      "enterprise-d.com",
			PlanType:    "enterprise",
			MaxAccounts: 20,
			MaxStorage:  ***********, // 20GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	for _, tenant := range testTenants {
		var existingTenant Tenant
		if err := db.Where("name = ?", tenant.Name).First(&existingTenant).Error; err != nil {
			db.Create(&tenant)
			fmt.Printf("Test tenant created: %s (%s)\n", tenant.Name, tenant.PlanType)
		}
	}

	return nil
}

// 初始化测试用户
func initTestUsers() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&User{})
	if err != nil {
		return err
	}

	// 创建系统级超级管理员（不属于任何租户）
	var systemSuperAdmin User
	if err := db.Where("username = ?", "admin").First(&systemSuperAdmin).Error; err != nil {
		// 加密密码
		hashedPassword, err := hashPassword("admin123")
		if err != nil {
			return err
		}
		
		// 创建系统级超级管理员
		systemSuperAdmin = User{
			TenantID:   nil, // 不属于任何租户
			Username:   "admin",
			Email:      "<EMAIL>",
			Password:   hashedPassword,
			Role:       "super_admin",
			UserType:   "system",
			Status:     true,
			RealName:   "系统超级管理员",
			Department: "系统管理部",
			Position:   "系统超级管理员",
			Bio:        "系统级超级管理员，拥有所有租户的管理权限",
		}
		db.Create(&systemSuperAdmin)
		fmt.Printf("System super admin created: %s\n", systemSuperAdmin.Username)
	}

	// 获取所有租户
	var tenants []Tenant
	db.Find(&tenants)

	// 为每个租户创建测试用户
	for _, tenant := range tenants {
		// 根据租户类型创建不同的用户
		var users []User

		switch tenant.PlanType {
		case "enterprise":
			// 企业版：管理员、客服
			users = []User{
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("admin_%s", tenant.Name),
					Email:      fmt.Sprintf("admin@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "admin",
					UserType:   "tenant",
					Status:     true,
					RealName:   "管理员",
					Department: "管理部",
					Position:   "管理员",
					Bio:        "租户管理员",
				},
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("customer_service_%s", tenant.Name),
					Email:      fmt.Sprintf("customer_service@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "customer_service",
					UserType:   "tenant",
					Status:     true,
					RealName:   "客服",
					Department: "客服部",
					Position:   "客服专员",
					Bio:        "客服专员",
				},
			}
		case "professional":
			// 专业版：管理员、客服
			users = []User{
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("admin_%s", tenant.Name),
					Email:      fmt.Sprintf("admin@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "admin",
					UserType:   "tenant",
					Status:     true,
					RealName:   "管理员",
					Department: "管理部",
					Position:   "管理员",
					Bio:        "租户管理员",
				},
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("customer_service_%s", tenant.Name),
					Email:      fmt.Sprintf("customer_service@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "customer_service",
					UserType:   "tenant",
					Status:     true,
					RealName:   "客服",
					Department: "客服部",
					Position:   "客服专员",
					Bio:        "客服专员",
				},
			}
		case "basic":
			// 基础版：管理员、客服
			users = []User{
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("admin_%s", tenant.Name),
					Email:      fmt.Sprintf("admin@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "admin",
					UserType:   "tenant",
					Status:     true,
					RealName:   "管理员",
					Department: "管理部",
					Position:   "管理员",
					Bio:        "租户管理员",
				},
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("customer_service_%s", tenant.Name),
					Email:      fmt.Sprintf("customer_service@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "customer_service",
					UserType:   "tenant",
					Status:     true,
					RealName:   "客服",
					Department: "客服部",
					Position:   "客服专员",
					Bio:        "客服专员",
				},
			}
		}

		// 创建用户
		for _, user := range users {
			var existingUser User
			if err := db.Where("username = ?", user.Username).First(&existingUser).Error; err != nil {
				// 加密密码
				hashedPassword, err := hashPassword(user.Password)
				if err != nil {
					return err
				}
				user.Password = hashedPassword
				db.Create(&user)
				fmt.Printf("Test user created: %s (%s) in tenant %s\n", user.Username, user.Role, tenant.Name)
			}
		}
	}

	return nil
}



// 初始化权限系统数据
func initPermissionSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&Role{}, &Permission{}, &UserRole{})
	if err != nil {
		return err
	}

	// 创建默认权限
	defaultPermissions := []Permission{
		{Name: "user.read", DisplayName: "查看用户", Resource: "user", Action: "read", Description: "查看用户列表和详情"},
		{Name: "user.write", DisplayName: "管理用户", Resource: "user", Action: "write", Description: "创建和编辑用户"},
		{Name: "user.delete", DisplayName: "删除用户", Resource: "user", Action: "delete", Description: "删除用户"},
		{Name: "role.read", DisplayName: "查看角色", Resource: "role", Action: "read", Description: "查看角色列表和详情"},
		{Name: "role.write", DisplayName: "管理角色", Resource: "role", Action: "write", Description: "创建和编辑角色"},
		{Name: "role.delete", DisplayName: "删除角色", Resource: "role", Action: "delete", Description: "删除角色"},
		{Name: "permission.read", DisplayName: "查看权限", Resource: "permission", Action: "read", Description: "查看权限配置"},
		{Name: "permission.write", DisplayName: "配置权限", Resource: "permission", Action: "write", Description: "配置角色权限"},
		{Name: "system.read", DisplayName: "查看系统", Resource: "system", Action: "read", Description: "查看系统信息"},
		{Name: "system.write", DisplayName: "管理系统", Resource: "system", Action: "write", Description: "系统配置管理"},
		{Name: "whatsapp.read", DisplayName: "查看WhatsApp", Resource: "whatsapp", Action: "read", Description: "查看WhatsApp账号和会话"},
		{Name: "whatsapp.write", DisplayName: "管理WhatsApp", Resource: "whatsapp", Action: "write", Description: "管理WhatsApp账号和会话"},
		{Name: "group_sending.read", DisplayName: "查看群发任务", Resource: "group_sending", Action: "read", Description: "查看群发任务列表和详情"},
		{Name: "group_sending.write", DisplayName: "管理群发任务", Resource: "group_sending", Action: "write", Description: "创建和编辑群发任务"},
		{Name: "group_sending.delete", DisplayName: "删除群发任务", Resource: "group_sending", Action: "delete", Description: "删除群发任务"},
		{Name: "group_sending.execute", DisplayName: "执行群发任务", Resource: "group_sending", Action: "execute", Description: "启动、暂停、终止群发任务"},
	}

	for _, perm := range defaultPermissions {
		var existingPerm Permission
		if err := db.Where("name = ?", perm.Name).First(&existingPerm).Error; err != nil {
			db.Create(&perm)
		}
	}

	// 获取所有租户
	var tenants []Tenant
	db.Find(&tenants)

	// 为每个租户创建角色
	for _, tenant := range tenants {
		// 根据租户类型创建不同的角色
		var roles []struct {
			Role        Role
			Permissions []string
		}

		switch tenant.PlanType {
		case "enterprise":
			// 企业版：管理员、客服
			roles = []struct {
				Role        Role
				Permissions []string
			}{
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "admin",
						DisplayName: "管理员",
						Description: "拥有用户管理权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "user.write", "user.delete", "system.read", "whatsapp.read", "whatsapp.write", "group_sending.read", "group_sending.write", "group_sending.delete", "group_sending.execute"},
				},
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "customer_service",
						DisplayName: "客服",
						Description: "客服权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "system.read"},
				},
			}
		case "professional":
			// 专业版：管理员、客服
			roles = []struct {
				Role        Role
				Permissions []string
			}{
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "admin",
						DisplayName: "管理员",
						Description: "拥有用户管理权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "user.write", "user.delete", "system.read", "whatsapp.read", "whatsapp.write", "group_sending.read", "group_sending.write", "group_sending.delete", "group_sending.execute"},
				},
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "customer_service",
						DisplayName: "客服",
						Description: "客服权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "system.read"},
				},
			}
		case "basic":
			// 基础版：管理员、客服
			roles = []struct {
				Role        Role
				Permissions []string
			}{
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "admin",
						DisplayName: "管理员",
						Description: "基础管理权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "user.write", "system.read", "whatsapp.read", "whatsapp.write", "group_sending.read", "group_sending.write", "group_sending.delete", "group_sending.execute"},
				},
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "customer_service",
						DisplayName: "客服",
						Description: "客服权限",
						Status:      true,
					},
					Permissions: []string{"user.read"},
				},
			}
		}

		// 为当前租户创建角色
		for _, roleData := range roles {
			var existingRole Role
			if err := db.Where("name = ? AND tenant_id = ?", roleData.Role.Name, tenant.ID).First(&existingRole).Error; err != nil {
				// 创建角色
				db.Create(&roleData.Role)
				
				// 分配权限
				var permissions []Permission
				db.Where("name IN ?", roleData.Permissions).Find(&permissions)
				db.Model(&roleData.Role).Association("Permissions").Append(&permissions)
				
				fmt.Printf("Role created for tenant %s: %s\n", tenant.Name, roleData.Role.DisplayName)
			}
		}
	}



	return nil
}

// 检查用户权限
func hasUserPermission(userID uint, permissionName string) bool {
	var count int64
	db.Table("users").
		Select("1").
		Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("users.id = ? AND permissions.name = ? AND roles.status = true", userID, permissionName).
		Count(&count)
	
	return count > 0
}

// 获取用户所有权限
func getUserPermissions(userID uint) []Permission {
	var permissions []Permission
	db.Table("permissions").
		Select("permissions.*").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN roles ON role_permissions.role_id = roles.id").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND roles.status = true", userID).
		Group("permissions.id").
		Find(&permissions)
	
	return permissions
}

// 系统配置模型
type SystemConfig struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Key         string `json:"key" gorm:"unique;not null"`     // 配置键名
	Value       string `json:"value" gorm:"type:text"`         // 配置值
	Type        string `json:"type" gorm:"default:string"`     // 配置类型：string, number, boolean, json
	Category    string `json:"category" gorm:"not null"`       // 配置分类：basic, mail, security, system
	DisplayName string `json:"display_name" gorm:"not null"`   // 显示名称
	Description string `json:"description"`                    // 配置描述
	IsPublic    bool   `json:"is_public" gorm:"default:false"` // 是否为公开配置（前端可访问）
	IsEditable  bool   `json:"is_editable" gorm:"default:true"` // 是否可编辑
	gorm.Model
}

// 初始化系统配置
func initSystemConfig() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&SystemConfig{})
	if err != nil {
		return err
	}

	// 初始化系统配置
	defaultConfigs := []SystemConfig{
		{Key: "site_name", Value: "Hive SaaS", Type: "string", Category: "basic", DisplayName: "网站名称", Description: "系统显示的名称", IsPublic: true, IsEditable: true},
		{Key: "site_description", Value: "专业的WhatsApp SaaS平台", Type: "string", Category: "basic", DisplayName: "网站描述", Description: "系统描述信息", IsPublic: true, IsEditable: true},
		{Key: "site_keywords", Value: "WhatsApp,SaaS,客服,营销", Type: "string", Category: "basic", DisplayName: "网站关键词", Description: "SEO关键词", IsPublic: true, IsEditable: true},
		{Key: "copyright", Value: "© 2024 Hive SaaS. All Rights Reserved.", Type: "string", Category: "basic", DisplayName: "版权信息", Description: "网站版权信息", IsPublic: true, IsEditable: true},
		{Key: "icp", Value: "", Type: "string", Category: "basic", DisplayName: "ICP备案", Description: "ICP备案信息", IsPublic: true, IsEditable: true},
		{Key: "contact_email", Value: "<EMAIL>", Type: "string", Category: "contact", DisplayName: "联系邮箱", Description: "客服联系邮箱", IsPublic: true, IsEditable: true},
		{Key: "contact_phone", Value: "", Type: "string", Category: "contact", DisplayName: "联系电话", Description: "客服联系电话", IsPublic: true, IsEditable: true},
		{Key: "contact_address", Value: "", Type: "string", Category: "contact", DisplayName: "联系地址", Description: "公司地址", IsPublic: true, IsEditable: true},
		{Key: "max_upload_size", Value: "10", Type: "number", Category: "upload", DisplayName: "最大上传大小(MB)", Description: "文件上传大小限制", IsPublic: false, IsEditable: true},
		{Key: "allowed_file_types", Value: "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx", Type: "string", Category: "upload", DisplayName: "允许的文件类型", Description: "允许上传的文件类型", IsPublic: false, IsEditable: true},
		{Key: "session_timeout", Value: "1440", Type: "number", Category: "security", DisplayName: "会话超时时间(分钟)", Description: "用户会话超时时间", IsPublic: false, IsEditable: true},
		{Key: "password_min_length", Value: "6", Type: "number", Category: "security", DisplayName: "密码最小长度", Description: "用户密码最小长度", IsPublic: false, IsEditable: true},
		{Key: "login_attempts_limit", Value: "5", Type: "number", Category: "security", DisplayName: "登录尝试次数限制", Description: "登录失败次数限制", IsPublic: false, IsEditable: true},
		{Key: "enable_registration", Value: "true", Type: "boolean", Category: "user", DisplayName: "启用用户注册", Description: "是否允许新用户注册", IsPublic: false, IsEditable: true},
		{Key: "enable_email_verification", Value: "false", Type: "boolean", Category: "user", DisplayName: "启用邮箱验证", Description: "注册时是否需要邮箱验证", IsPublic: false, IsEditable: true},
		{Key: "default_user_role", Value: "user", Type: "string", Category: "user", DisplayName: "默认用户角色", Description: "新用户的默认角色", IsPublic: false, IsEditable: true},
	}

	for _, config := range defaultConfigs {
		var existingConfig SystemConfig
		if err := db.Where("key = ?", config.Key).First(&existingConfig).Error; err != nil {
			db.Create(&config)
		}
	}

	return nil
} 

// 租户切换历史记录
type TenantSwitchHistory struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	FromTenantID uint     `json:"from_tenant_id"`
	ToTenantID  uint      `json:"to_tenant_id" gorm:"not null"`
	SwitchTime  time.Time `json:"switch_time" gorm:"not null"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	Reason      string    `json:"reason"` // 切换原因
	User        User      `json:"user" gorm:"foreignKey:UserID"`
	FromTenant  Tenant    `json:"from_tenant" gorm:"foreignKey:FromTenantID"`
	ToTenant    Tenant    `json:"to_tenant" gorm:"foreignKey:ToTenantID"`
	gorm.Model
}

// 初始化租户切换历史系统
func initTenantSwitchHistory() error {
	return db.AutoMigrate(&TenantSwitchHistory{})
} 

// WhatsApp账号分组模型
type WhatsAppGroup struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	TenantID    uint      `json:"tenant_id" gorm:"not null"` // 租户ID
	Name        string    `json:"name" gorm:"not null"`       // 分组名称
	Description string    `json:"description"`                 // 分组描述
	Color       string    `json:"color" gorm:"default:#409EFF"` // 分组颜色
	Status      bool      `json:"status" gorm:"default:true"` // 分组状态
	SortOrder   int       `json:"sort_order" gorm:"default:0"` // 排序
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	Tenant      Tenant           `json:"tenant" gorm:"foreignKey:TenantID"`
	Accounts    []WhatsAppAccount `json:"accounts" gorm:"foreignKey:GroupID"`
}

// WhatsApp Session模型 - 会话层
type WhatsAppSession struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	SessionID   string    `json:"session_id" gorm:"unique;not null"` // 唯一会话ID
	TenantID    uint      `json:"tenant_id" gorm:"not null"`         // 租户ID
	Status      string    `json:"status" gorm:"default:pending"`     // pending, active, expired, failed
	QRCode      string    `json:"qr_code" gorm:"type:text"`          // 二维码数据
	QRCodeExpiresAt *time.Time `json:"qr_code_expires_at"`           // 二维码过期时间
	PhoneNumber string    `json:"phone_number"`                       // 扫码后的手机号
	ClientInfo  string    `json:"client_info" gorm:"type:text"`      // 客户端信息JSON
	Error       string    `json:"error" gorm:"type:text"`            // 错误信息
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	ExpiresAt   *time.Time `json:"expires_at"`                       // Session过期时间
	
	Tenant      Tenant `json:"tenant" gorm:"foreignKey:TenantID"`
}

// WhatsApp Account模型 - 账号层
type WhatsAppAccount struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	TenantID          uint      `json:"tenant_id" gorm:"not null"` // 租户ID
	SessionID         string    `json:"session_id" gorm:"unique"`   // 关联的Session ID
	GroupID           *uint     `json:"group_id"`                   // 分组ID，可为空
	AccountName       string    `json:"account_name" gorm:"not null"` // 账号名称
	PhoneNumber       string    `json:"phone_number" gorm:"unique"` // 手机号码，允许为空，但如果有值则必须唯一
	WID               string    `json:"wid" gorm:"column:w_id"`     // WhatsApp ID
	AccountType       string    `json:"account_type" gorm:"default:personal"` // 账号类型：personal(个人版), business(商业版)
	Avatar            string    `json:"avatar" gorm:"type:text"`    // 头像URL
	Nickname          string    `json:"nickname"`                    // 昵称
	Remark            string    `json:"remark" gorm:"type:text"`    // 备注
	Status            string    `json:"status" gorm:"default:disconnected"` // 连接状态
	ConnectionStatus  string    `json:"connection_status" gorm:"default:disconnected"` // 连接状态：disconnected, connecting, connected, error, banned, offline
	AccountStatus     string    `json:"account_status" gorm:"default:normal"` // 账号状态：normal(正常), banned(封号)
	LastActivity      *time.Time `json:"last_activity"`              // 最后活动时间
	LoginTime         *time.Time `json:"login_time"`                 // 登录时间
	BanTime           *time.Time `json:"ban_time"`                   // 封号时间
	BanReason         string    `json:"ban_reason" gorm:"type:text"` // 封号原因
	Error             string    `json:"error" gorm:"type:text"`     // 错误信息
	IsActive          bool      `json:"is_active" gorm:"default:true"` // 是否启用
	AutoReconnect     bool      `json:"auto_reconnect" gorm:"default:true"` // 自动重连
	MaxReconnectAttempts int    `json:"max_reconnect_attempts" gorm:"default:3"` // 最大重连次数
	ReconnectAttempts int       `json:"reconnect_attempts" gorm:"default:0"` // 当前重连次数
	
	// Session管理相关字段
	SessionStoragePath string    `json:"session_storage_path"`       // Session存储路径
	SessionStorageType string    `json:"session_storage_type" gorm:"default:local"` // 存储类型：local, s3, oss
	SessionCreatedAt   *time.Time `json:"session_created_at"`        // Session创建时间
	SessionLastUsed    *time.Time `json:"session_last_used"`         // Session最后使用时间
	SessionFileSize    int64     `json:"session_file_size" gorm:"default:0"` // Session文件大小(字节)
	SessionFileCount   int       `json:"session_file_count" gorm:"default:0"` // Session文件数量
	IsSessionValid     bool      `json:"is_session_valid" gorm:"default:false"` // Session是否有效
	
	// WhatsApp客户端信息
	Platform          string    `json:"platform"`                    // 平台：android, ios, web
	PushName          string    `json:"pushname" gorm:"column:push_name"` // 显示名称
	BusinessName      string    `json:"business_name"`               // 商业名称
	Description       string    `json:"description" gorm:"type:text"` // 描述
	Email             string    `json:"email"`                       // 邮箱
	Website           string    `json:"website"`                     // 网站
	Address           string    `json:"address" gorm:"type:text"`    // 地址
	Category          string    `json:"category"`                    // 分类
	Subcategory       string    `json:"subcategory"`                 // 子分类
	IsBusiness        bool      `json:"is_business" gorm:"default:false"` // 是否商业账号
	IsEnterprise      bool      `json:"is_enterprise" gorm:"default:false"` // 是否企业账号
	IsVerified        bool      `json:"is_verified" gorm:"default:false"` // 是否已验证
	DeviceCount       int       `json:"device_count" gorm:"default:1"` // 设备数量
	ProfilePicURL     string    `json:"profile_pic_url"`             // 头像URL
	ProfilePicUpdatedAt *time.Time `json:"profile_pic_updated_at"`   // 头像更新时间
	
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	
	Tenant            Tenant        `json:"tenant" gorm:"foreignKey:TenantID"`
	Group             *WhatsAppGroup `json:"group" gorm:"foreignKey:GroupID"`
	Session           *WhatsAppSession `json:"session" gorm:"foreignKey:SessionID;references:SessionID"`
}

// Session清理日志表
type SessionCleanupLog struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	AccountID     uint      `json:"account_id" gorm:"not null"`
	SessionID     string    `json:"session_id" gorm:"not null"`
	CleanupType   string    `json:"cleanup_type" gorm:"not null"` // invalid_session, orphaned_session, account_deleted, size_limit
	FileSizeFreed int64     `json:"file_size_freed" gorm:"default:0"` // 释放的空间(字节)
	FileCountFreed int      `json:"file_count_freed" gorm:"default:0"` // 释放的文件数量
	CleanupReason string    `json:"cleanup_reason" gorm:"type:text"` // 清理原因
	CleanedAt     time.Time `json:"cleaned_at" gorm:"not null"`
	
	Account        WhatsAppAccount `json:"account" gorm:"foreignKey:AccountID"`
}

// Node服务状态管理表
type NodeServiceStatus struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	ServiceID      string    `json:"service_id" gorm:"unique;not null"` // Node服务实例ID
	Status         string    `json:"status" gorm:"default:unknown"`     // running, stopped, restarting, unknown
	LastSeen       time.Time `json:"last_seen"`                         // 最后心跳时间
	ActiveSessions string    `json:"active_sessions" gorm:"type:text"`  // JSON数组，存储活跃的session列表
	ProcessPID     int       `json:"process_pid" gorm:"default:0"`      // Node进程ID
	StartTime      time.Time `json:"start_time"`                        // 服务启动时间
	RestartCount   int       `json:"restart_count" gorm:"default:0"`    // 重启次数
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// 初始化WhatsApp账号和分组系统
func initWhatsAppSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&WhatsAppGroup{}, &WhatsAppAccount{}, &WhatsAppSession{}, &NodeServiceStatus{}, &SessionCleanupLog{}, &WhatsAppSendHistory{})
	if err != nil {
		return err
	}

	// 为每个租户创建默认分组
	var tenants []Tenant
	if err := db.Find(&tenants).Error; err != nil {
		return err
	}

	for _, tenant := range tenants {
		// 检查是否已有默认分组
		var existingGroup WhatsAppGroup
		if err := db.Where("tenant_id = ? AND name = ?", tenant.ID, "默认分组").First(&existingGroup).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建默认分组
				defaultGroup := WhatsAppGroup{
					TenantID:    tenant.ID,
					Name:        "默认分组",
					Description: "系统默认分组",
					Color:       "#409EFF",
					Status:      true,
					SortOrder:   0,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
				if err := db.Create(&defaultGroup).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
} 

// 群发任务模型
type GroupSendingTask struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	TenantID          uint      `json:"tenant_id" gorm:"not null"` // 租户ID
	TaskName          string    `json:"task_name" gorm:"not null"` // 任务名称
	TaskID            string    `json:"task_id" gorm:"unique;not null"` // 任务ID
	CreatorID         uint      `json:"creator_id" gorm:"not null"` // 创建者ID
	CreatorName       string    `json:"creator_name" gorm:"not null"` // 创建者名称
	
	// 账号配置
	AccountUsage      string    `json:"account_usage" gorm:"default:'auto'"` // 账号使用方式：auto(自动分配), manual(选择账号)
	SelectedAccountID *uint     `json:"selected_account_id"` // 选择的账号ID（手动模式）
	
	// 客户资源
	CustomerFile      string    `json:"customer_file" gorm:"type:text"` // 客户文件路径
	CustomerCount     int       `json:"customer_count" gorm:"default:0"` // 客户总数
	ReachedCount      int       `json:"reached_count" gorm:"default:0"` // 已触达客户数
	
	// 发送配置
	CustomerInterval  int       `json:"customer_interval" gorm:"default:60"` // 客户间隔（秒）
	SendingTimes      int       `json:"sending_times" gorm:"default:1"` // 发送次数
	SendingTimesType  string    `json:"sending_times_type" gorm:"default:'limited'"` // 发送次数类型：limited(限制), unlimited(不限)
	SendingMethod     string    `json:"sending_method" gorm:"default:'one_by_one'"` // 发送方式：one_by_one(逐条发送), after_reply(对方回复后发送)
	SentenceInterval  int       `json:"sentence_interval" gorm:"default:1"` // 语句间隔（秒）
	
	// 任务状态
	Status            string    `json:"status" gorm:"default:'pending'"` // 任务状态：pending(待开始), running(运行中), paused(暂停), completed(已完成), terminated(已终止)
	Progress          float64   `json:"progress" gorm:"default:0"` // 进度百分比
	
	// 发送统计
	PendingMessages   int       `json:"pending_messages" gorm:"default:0"` // 待发送条数
	AvailableMessages int       `json:"available_messages" gorm:"default:0"` // 可用发送条数
	SentMessages      int       `json:"sent_messages" gorm:"default:0"` // 已发送条数
	FailedMessages    int       `json:"failed_messages" gorm:"default:0"` // 失败条数
	
	// 时间配置
	ScheduledTime     *time.Time `json:"scheduled_time"` // 预约时间
	StartTime         *time.Time `json:"start_time"` // 开始时间
	EndTime           *time.Time `json:"end_time"` // 结束时间
	
	// 号码检测
	NumberDetection   bool      `json:"number_detection" gorm:"default:false"` // 是否启用号码检测
	
	// 群发语句配置
	Statements        string    `json:"statements" gorm:"type:text"` // 群发语句配置（JSON格式）
	
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	
	Tenant            Tenant    `json:"tenant" gorm:"foreignKey:TenantID"`
	Creator           User      `json:"creator" gorm:"foreignKey:CreatorID"`
	SelectedAccount   *WhatsAppAccount `json:"selected_account" gorm:"foreignKey:SelectedAccountID"`
}

// 群发语句项模型
type GroupSendingStatement struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	TaskID      uint   `json:"task_id" gorm:"not null"` // 关联的任务ID
	Order       int    `json:"order" gorm:"not null"` // 顺序
	Type        string `json:"type" gorm:"not null"` // 类型：text(文字), image(图片), video(视频), call(拨打电话)
	MaterialName string `json:"material_name"` // 素材名称
	Content     string `json:"content" gorm:"type:text"` // 内容
	FileURL     string `json:"file_url"` // 文件URL（图片/视频）
	Duration    int    `json:"duration"` // 持续时间（秒，用于视频）
	
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	Task        GroupSendingTask `json:"task" gorm:"foreignKey:TaskID"`
}

// 群发任务日志模型
type GroupSendingLog struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	TaskID      uint      `json:"task_id" gorm:"not null"` // 关联的任务ID
	AccountID   uint      `json:"account_id" gorm:"not null"` // 使用的账号ID
	PhoneNumber string    `json:"phone_number" gorm:"not null"` // 目标手机号
	Message     string    `json:"message" gorm:"type:text"` // 发送的消息内容
	Status      string    `json:"status" gorm:"not null"` // 状态：success(成功), failed(失败), pending(待发送)
	ErrorMsg    string    `json:"error_msg"` // 错误信息
	SentAt      *time.Time `json:"sent_at"` // 发送时间
	
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	Task        GroupSendingTask `json:"task" gorm:"foreignKey:TaskID"`
	Account     WhatsAppAccount  `json:"account" gorm:"foreignKey:AccountID"`
}

// 初始化群发任务系统
func initGroupSendingSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&GroupSendingTask{}, &GroupSendingStatement{}, &GroupSendingLog{})
	if err != nil {
		return err
	}

	// 检查是否已有测试数据
	var count int64
	db.Model(&GroupSendingTask{}).Count(&count)
	
	// 如果没有数据，创建一些测试数据
	if count == 0 {
		// 获取第一个租户和用户
		var tenant Tenant
		if err := db.First(&tenant).Error; err != nil {
			fmt.Println("未找到租户，跳过测试数据创建")
			return nil
		}
		
		var user User
		if err := db.Where("tenant_id = ?", tenant.ID).First(&user).Error; err != nil {
			fmt.Println("未找到用户，跳过测试数据创建")
			return nil
		}
		
		// 创建测试任务
		testTask := GroupSendingTask{
			TenantID:         tenant.ID,
			TaskName:         "测试群发任务",
			TaskID:           "test-task-001",
			CreatorID:        user.ID,
			CreatorName:      user.RealName,
			AccountUsage:     "auto",
			CustomerFile:     "test-data/customers.txt",
			CustomerCount:    10,
			CustomerInterval: 60,
			SendingTimes:     1,
			SendingTimesType: "limited",
			SendingMethod:    "one_by_one",
			SentenceInterval: 1,
			Status:           "pending",
			Progress:         0,
			PendingMessages:  10,
			AvailableMessages: 100,
			SentMessages:     0,
			FailedMessages:   0,
			NumberDetection:  false,
			Statements:       `[{"type":"text","content":"您好，这是一条测试消息，请忽略。","order":1}]`,
		}
		
		if err := db.Create(&testTask).Error; err != nil {
			fmt.Printf("创建测试任务失败: %v\n", err)
		} else {
			fmt.Println("创建测试群发任务成功")
		}
	}

	fmt.Println("群发任务系统初始化完成")
	return nil
}

// WhatsAppSendHistory WhatsApp发送历史
type WhatsAppSendHistory struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	AccountID   uint      `json:"account_id" gorm:"not null"`
	PhoneNumber string    `json:"phone_number" gorm:"not null"`
	MessageType string    `json:"message_type" gorm:"not null"`
	Content     string    `json:"content" gorm:"type:text"`
	Status      string    `json:"status" gorm:"not null"` // success, failed
	Error       string    `json:"error" gorm:"type:text"`
	Timestamp   time.Time `json:"timestamp"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	Account     WhatsAppAccount `json:"account" gorm:"foreignKey:AccountID"`
}

// TableName 指定表名
func (WhatsAppSendHistory) TableName() string {
	return "whats_app_send_histories"
} 