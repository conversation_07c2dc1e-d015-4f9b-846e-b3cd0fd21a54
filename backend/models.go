package main

import (
	"fmt"
	"gorm.io/gorm"
	"time"
)

// 租户模型
type Tenant struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Domain      string    `json:"domain" gorm:"unique"`
	PlanType    string    `json:"plan_type" gorm:"default:basic"` // basic, professional, enterprise
	MaxAccounts int       `json:"max_accounts" gorm:"default:1"`
	MaxStorage  int64     `json:"max_storage" gorm:"default:**********"` // 1GB
	Status      string    `json:"status" gorm:"default:active"` // active, suspended, cancelled
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// 角色模型
type Role struct {
	ID          uint         `json:"id" gorm:"primaryKey"`
	TenantID    uint         `json:"tenant_id" gorm:"not null"` // 添加租户ID
	Name        string       `json:"name" gorm:"not null"`
	DisplayName string       `json:"display_name" gorm:"not null"`
	Description string       `json:"description"`
	Status      bool         `json:"status" gorm:"default:true"`
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	Tenant      Tenant       `json:"tenant" gorm:"foreignKey:TenantID"`
	gorm.Model
}

// 权限模型
type Permission struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"unique;not null"`
	DisplayName string `json:"display_name" gorm:"not null"`
	Resource    string `json:"resource" gorm:"not null"` // 资源名称，如 user, role, system
	Action      string `json:"action" gorm:"not null"`   // 操作名称，如 read, write, delete
	Description string `json:"description"`
	Roles       []Role `json:"roles" gorm:"many2many:role_permissions;"`
	gorm.Model
}

// 用户角色关联模型
type UserRole struct {
	ID     uint `json:"id" gorm:"primaryKey"`
	UserID uint `json:"user_id" gorm:"not null"`
	RoleID uint `json:"role_id" gorm:"not null"`
	User   User `json:"user" gorm:"foreignKey:UserID"`
	Role   Role `json:"role" gorm:"foreignKey:RoleID"`
	gorm.Model
}

// 更新用户模型，支持多角色和多租户
type User struct {
	ID         uint       `json:"id" gorm:"primaryKey"`
	TenantID   *uint      `json:"tenant_id"` // 允许为空，超级管理员可以不属于任何租户
	Username   string     `json:"username" gorm:"unique;not null"`
	Email      string     `json:"email" gorm:"unique;not null"`
	Password   string     `json:"-" gorm:"not null"` // 密码不返回到前端
	Role       string     `json:"role" gorm:"default:user"` // 保留兼容性
	UserType   string     `json:"user_type" gorm:"default:tenant"` // tenant: 租户用户, system: 系统用户
	Status     bool       `json:"status" gorm:"default:true"`
	
	// 个人资料字段
	RealName   string     `json:"real_name"`   // 真实姓名
	Phone      string     `json:"phone"`       // 手机号码
	Avatar     string     `json:"avatar"`      // 头像URL
	Department string     `json:"department"`  // 部门
	Position   string     `json:"position"`    // 职位
	Bio        string     `json:"bio"`         // 个人简介
	LastLogin  *time.Time `json:"last_login"`  // 最后登录时间
	
	Tenant     *Tenant    `json:"tenant" gorm:"foreignKey:TenantID"`
	Roles      []Role     `json:"roles" gorm:"many2many:user_roles;"`
	gorm.Model
}

// 初始化租户系统数据
func initTenantSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&Tenant{})
	if err != nil {
		return err
	}

	// 创建多个测试租户
	testTenants := []Tenant{
		{
			Name:        "Hive SaaS",
			Domain:      "hive-saas.com",
			PlanType:    "enterprise",
			MaxAccounts: 10,
			MaxStorage:  **********0, // 10GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "测试公司A",
			Domain:      "company-a.com",
			PlanType:    "professional",
			MaxAccounts: 5,
			MaxStorage:  **********, // 5GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "测试公司B",
			Domain:      "company-b.com",
			PlanType:    "basic",
			MaxAccounts: 3,
			MaxStorage:  **********, // 1GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "创业公司C",
			Domain:      "startup-c.com",
			PlanType:    "professional",
			MaxAccounts: 8,
			MaxStorage:  **********, // 8GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "企业集团D",
			Domain:      "enterprise-d.com",
			PlanType:    "enterprise",
			MaxAccounts: 20,
			MaxStorage:  ***********, // 20GB
			Status:      "active",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	for _, tenant := range testTenants {
		var existingTenant Tenant
		if err := db.Where("name = ?", tenant.Name).First(&existingTenant).Error; err != nil {
			db.Create(&tenant)
			fmt.Printf("Test tenant created: %s (%s)\n", tenant.Name, tenant.PlanType)
		}
	}

	return nil
}

// 初始化测试用户
func initTestUsers() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&User{})
	if err != nil {
		return err
	}

	// 创建系统级超级管理员（不属于任何租户）
	var systemSuperAdmin User
	if err := db.Where("username = ?", "admin").First(&systemSuperAdmin).Error; err != nil {
		// 加密密码
		hashedPassword, err := hashPassword("admin123")
		if err != nil {
			return err
		}
		
		// 创建系统级超级管理员
		systemSuperAdmin = User{
			TenantID:   nil, // 不属于任何租户
			Username:   "admin",
			Email:      "<EMAIL>",
			Password:   hashedPassword,
			Role:       "super_admin",
			UserType:   "system",
			Status:     true,
			RealName:   "系统超级管理员",
			Department: "系统管理部",
			Position:   "系统超级管理员",
			Bio:        "系统级超级管理员，拥有所有租户的管理权限",
		}
		db.Create(&systemSuperAdmin)
		fmt.Printf("System super admin created: %s\n", systemSuperAdmin.Username)
	}

	// 获取所有租户
	var tenants []Tenant
	db.Find(&tenants)

	// 为每个租户创建测试用户
	for _, tenant := range tenants {
		// 根据租户类型创建不同的用户
		var users []User

		switch tenant.PlanType {
		case "enterprise":
			// 企业版：管理员、客服
			users = []User{
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("admin_%s", tenant.Name),
					Email:      fmt.Sprintf("admin@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "admin",
					UserType:   "tenant",
					Status:     true,
					RealName:   "管理员",
					Department: "管理部",
					Position:   "管理员",
					Bio:        "租户管理员",
				},
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("customer_service_%s", tenant.Name),
					Email:      fmt.Sprintf("customer_service@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "customer_service",
					UserType:   "tenant",
					Status:     true,
					RealName:   "客服",
					Department: "客服部",
					Position:   "客服专员",
					Bio:        "客服专员",
				},
			}
		case "professional":
			// 专业版：管理员、客服
			users = []User{
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("admin_%s", tenant.Name),
					Email:      fmt.Sprintf("admin@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "admin",
					UserType:   "tenant",
					Status:     true,
					RealName:   "管理员",
					Department: "管理部",
					Position:   "管理员",
					Bio:        "租户管理员",
				},
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("customer_service_%s", tenant.Name),
					Email:      fmt.Sprintf("customer_service@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "customer_service",
					UserType:   "tenant",
					Status:     true,
					RealName:   "客服",
					Department: "客服部",
					Position:   "客服专员",
					Bio:        "客服专员",
				},
			}
		case "basic":
			// 基础版：管理员、客服
			users = []User{
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("admin_%s", tenant.Name),
					Email:      fmt.Sprintf("admin@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "admin",
					UserType:   "tenant",
					Status:     true,
					RealName:   "管理员",
					Department: "管理部",
					Position:   "管理员",
					Bio:        "租户管理员",
				},
				{
					TenantID:   &tenant.ID,
					Username:   fmt.Sprintf("customer_service_%s", tenant.Name),
					Email:      fmt.Sprintf("customer_service@%s", tenant.Domain),
					Password:   "admin123",
					Role:       "customer_service",
					UserType:   "tenant",
					Status:     true,
					RealName:   "客服",
					Department: "客服部",
					Position:   "客服专员",
					Bio:        "客服专员",
				},
			}
		}

		// 创建用户
		for _, user := range users {
			var existingUser User
			if err := db.Where("username = ?", user.Username).First(&existingUser).Error; err != nil {
				// 加密密码
				hashedPassword, err := hashPassword(user.Password)
				if err != nil {
					return err
				}
				user.Password = hashedPassword
				db.Create(&user)
				fmt.Printf("Test user created: %s (%s) in tenant %s\n", user.Username, user.Role, tenant.Name)
			}
		}
	}

	return nil
}



// 初始化权限系统数据
func initPermissionSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&Role{}, &Permission{}, &UserRole{})
	if err != nil {
		return err
	}

	// 创建默认权限
	defaultPermissions := []Permission{
		{Name: "user.read", DisplayName: "查看用户", Resource: "user", Action: "read", Description: "查看用户列表和详情"},
		{Name: "user.write", DisplayName: "管理用户", Resource: "user", Action: "write", Description: "创建和编辑用户"},
		{Name: "user.delete", DisplayName: "删除用户", Resource: "user", Action: "delete", Description: "删除用户"},
		{Name: "role.read", DisplayName: "查看角色", Resource: "role", Action: "read", Description: "查看角色列表和详情"},
		{Name: "role.write", DisplayName: "管理角色", Resource: "role", Action: "write", Description: "创建和编辑角色"},
		{Name: "role.delete", DisplayName: "删除角色", Resource: "role", Action: "delete", Description: "删除角色"},
		{Name: "permission.read", DisplayName: "查看权限", Resource: "permission", Action: "read", Description: "查看权限配置"},
		{Name: "permission.write", DisplayName: "配置权限", Resource: "permission", Action: "write", Description: "配置角色权限"},
		{Name: "system.read", DisplayName: "查看系统", Resource: "system", Action: "read", Description: "查看系统信息"},
		{Name: "system.write", DisplayName: "管理系统", Resource: "system", Action: "write", Description: "系统配置管理"},
	}

	for _, perm := range defaultPermissions {
		var existingPerm Permission
		if err := db.Where("name = ?", perm.Name).First(&existingPerm).Error; err != nil {
			db.Create(&perm)
		}
	}

	// 获取所有租户
	var tenants []Tenant
	db.Find(&tenants)

	// 为每个租户创建角色
	for _, tenant := range tenants {
		// 根据租户类型创建不同的角色
		var roles []struct {
			Role        Role
			Permissions []string
		}

		switch tenant.PlanType {
		case "enterprise":
			// 企业版：管理员、客服
			roles = []struct {
				Role        Role
				Permissions []string
			}{
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "admin",
						DisplayName: "管理员",
						Description: "拥有用户管理权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "user.write", "user.delete", "system.read"},
				},
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "customer_service",
						DisplayName: "客服",
						Description: "客服权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "system.read"},
				},
			}
		case "professional":
			// 专业版：管理员、客服
			roles = []struct {
				Role        Role
				Permissions []string
			}{
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "admin",
						DisplayName: "管理员",
						Description: "拥有用户管理权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "user.write", "user.delete", "system.read"},
				},
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "customer_service",
						DisplayName: "客服",
						Description: "客服权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "system.read"},
				},
			}
		case "basic":
			// 基础版：管理员、客服
			roles = []struct {
				Role        Role
				Permissions []string
			}{
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "admin",
						DisplayName: "管理员",
						Description: "基础管理权限",
						Status:      true,
					},
					Permissions: []string{"user.read", "user.write", "system.read"},
				},
				{
					Role: Role{
						TenantID:    tenant.ID,
						Name:        "customer_service",
						DisplayName: "客服",
						Description: "客服权限",
						Status:      true,
					},
					Permissions: []string{"user.read"},
				},
			}
		}

		// 为当前租户创建角色
		for _, roleData := range roles {
			var existingRole Role
			if err := db.Where("name = ? AND tenant_id = ?", roleData.Role.Name, tenant.ID).First(&existingRole).Error; err != nil {
				// 创建角色
				db.Create(&roleData.Role)
				
				// 分配权限
				var permissions []Permission
				db.Where("name IN ?", roleData.Permissions).Find(&permissions)
				db.Model(&roleData.Role).Association("Permissions").Append(&permissions)
				
				fmt.Printf("Role created for tenant %s: %s\n", tenant.Name, roleData.Role.DisplayName)
			}
		}
	}



	return nil
}

// 检查用户权限
func hasUserPermission(userID uint, permissionName string) bool {
	var count int64
	db.Table("users").
		Select("1").
		Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("users.id = ? AND permissions.name = ? AND roles.status = true", userID, permissionName).
		Count(&count)
	
	return count > 0
}

// 获取用户所有权限
func getUserPermissions(userID uint) []Permission {
	var permissions []Permission
	db.Table("permissions").
		Select("permissions.*").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN roles ON role_permissions.role_id = roles.id").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND roles.status = true", userID).
		Group("permissions.id").
		Find(&permissions)
	
	return permissions
}

// 系统配置模型
type SystemConfig struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Key         string `json:"key" gorm:"unique;not null"`     // 配置键名
	Value       string `json:"value" gorm:"type:text"`         // 配置值
	Type        string `json:"type" gorm:"default:string"`     // 配置类型：string, number, boolean, json
	Category    string `json:"category" gorm:"not null"`       // 配置分类：basic, mail, security, system
	DisplayName string `json:"display_name" gorm:"not null"`   // 显示名称
	Description string `json:"description"`                    // 配置描述
	IsPublic    bool   `json:"is_public" gorm:"default:false"` // 是否为公开配置（前端可访问）
	IsEditable  bool   `json:"is_editable" gorm:"default:true"` // 是否可编辑
	gorm.Model
}

// 初始化系统配置
func initSystemConfig() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&SystemConfig{})
	if err != nil {
		return err
	}

	// 初始化系统配置
	defaultConfigs := []SystemConfig{
		{Key: "site_name", Value: "Hive SaaS", Type: "string", Category: "basic", DisplayName: "网站名称", Description: "系统显示的名称", IsPublic: true, IsEditable: true},
		{Key: "site_description", Value: "专业的WhatsApp SaaS平台", Type: "string", Category: "basic", DisplayName: "网站描述", Description: "系统描述信息", IsPublic: true, IsEditable: true},
		{Key: "site_keywords", Value: "WhatsApp,SaaS,客服,营销", Type: "string", Category: "basic", DisplayName: "网站关键词", Description: "SEO关键词", IsPublic: true, IsEditable: true},
		{Key: "copyright", Value: "© 2024 Hive SaaS. All Rights Reserved.", Type: "string", Category: "basic", DisplayName: "版权信息", Description: "网站版权信息", IsPublic: true, IsEditable: true},
		{Key: "icp", Value: "", Type: "string", Category: "basic", DisplayName: "ICP备案", Description: "ICP备案信息", IsPublic: true, IsEditable: true},
		{Key: "contact_email", Value: "<EMAIL>", Type: "string", Category: "contact", DisplayName: "联系邮箱", Description: "客服联系邮箱", IsPublic: true, IsEditable: true},
		{Key: "contact_phone", Value: "", Type: "string", Category: "contact", DisplayName: "联系电话", Description: "客服联系电话", IsPublic: true, IsEditable: true},
		{Key: "contact_address", Value: "", Type: "string", Category: "contact", DisplayName: "联系地址", Description: "公司地址", IsPublic: true, IsEditable: true},
		{Key: "max_upload_size", Value: "10", Type: "number", Category: "upload", DisplayName: "最大上传大小(MB)", Description: "文件上传大小限制", IsPublic: false, IsEditable: true},
		{Key: "allowed_file_types", Value: "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx", Type: "string", Category: "upload", DisplayName: "允许的文件类型", Description: "允许上传的文件类型", IsPublic: false, IsEditable: true},
		{Key: "session_timeout", Value: "1440", Type: "number", Category: "security", DisplayName: "会话超时时间(分钟)", Description: "用户会话超时时间", IsPublic: false, IsEditable: true},
		{Key: "password_min_length", Value: "6", Type: "number", Category: "security", DisplayName: "密码最小长度", Description: "用户密码最小长度", IsPublic: false, IsEditable: true},
		{Key: "login_attempts_limit", Value: "5", Type: "number", Category: "security", DisplayName: "登录尝试次数限制", Description: "登录失败次数限制", IsPublic: false, IsEditable: true},
		{Key: "enable_registration", Value: "true", Type: "boolean", Category: "user", DisplayName: "启用用户注册", Description: "是否允许新用户注册", IsPublic: false, IsEditable: true},
		{Key: "enable_email_verification", Value: "false", Type: "boolean", Category: "user", DisplayName: "启用邮箱验证", Description: "注册时是否需要邮箱验证", IsPublic: false, IsEditable: true},
		{Key: "default_user_role", Value: "user", Type: "string", Category: "user", DisplayName: "默认用户角色", Description: "新用户的默认角色", IsPublic: false, IsEditable: true},
	}

	for _, config := range defaultConfigs {
		var existingConfig SystemConfig
		if err := db.Where("key = ?", config.Key).First(&existingConfig).Error; err != nil {
			db.Create(&config)
		}
	}

	return nil
} 

// 租户切换历史记录
type TenantSwitchHistory struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	FromTenantID uint     `json:"from_tenant_id"`
	ToTenantID  uint      `json:"to_tenant_id" gorm:"not null"`
	SwitchTime  time.Time `json:"switch_time" gorm:"not null"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	Reason      string    `json:"reason"` // 切换原因
	User        User      `json:"user" gorm:"foreignKey:UserID"`
	FromTenant  Tenant    `json:"from_tenant" gorm:"foreignKey:FromTenantID"`
	ToTenant    Tenant    `json:"to_tenant" gorm:"foreignKey:ToTenantID"`
	gorm.Model
}

// 初始化租户切换历史系统
func initTenantSwitchHistory() error {
	return db.AutoMigrate(&TenantSwitchHistory{})
} 

// WhatsApp账号分组模型
type WhatsAppGroup struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	TenantID    uint      `json:"tenant_id" gorm:"not null"` // 租户ID
	Name        string    `json:"name" gorm:"not null"`       // 分组名称
	Description string    `json:"description"`                 // 分组描述
	Color       string    `json:"color" gorm:"default:#409EFF"` // 分组颜色
	Status      bool      `json:"status" gorm:"default:true"` // 分组状态
	SortOrder   int       `json:"sort_order" gorm:"default:0"` // 排序
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	Tenant      Tenant           `json:"tenant" gorm:"foreignKey:TenantID"`
	Accounts    []WhatsAppAccount `json:"accounts" gorm:"foreignKey:GroupID"`
}

// WhatsApp账号模型
type WhatsAppAccount struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	TenantID          uint      `json:"tenant_id" gorm:"not null"` // 租户ID
	GroupID           *uint     `json:"group_id"`                   // 分组ID，可为空
	AccountName       string    `json:"account_name" gorm:"not null"` // 账号名称
	PhoneNumber       string    `json:"phone_number" gorm:"unique;not null"` // 手机号码
	SessionID         string    `json:"session_id" gorm:"unique"`   // WhatsApp会话ID
	AccountType       string    `json:"account_type" gorm:"default:personal"` // 账号类型：personal(个人版), business(商业版)
	Avatar            string    `json:"avatar" gorm:"type:text"`    // 头像URL
	Nickname          string    `json:"nickname"`                    // 昵称
	Remark            string    `json:"remark" gorm:"type:text"`    // 备注
	Status            string    `json:"status" gorm:"default:disconnected"` // 连接状态
	ConnectionStatus  string    `json:"connection_status" gorm:"default:disconnected"` // 连接状态：disconnected, connecting, connected, error, banned, offline
	AccountStatus     string    `json:"account_status" gorm:"default:normal"` // 账号状态：normal(正常), banned(封号)
	QRCode            string    `json:"qr_code" gorm:"type:text"`   // 二维码数据（临时）
	QRCodeExpiresAt   *time.Time `json:"qr_code_expires_at"`        // 二维码过期时间
	LastActivity      *time.Time `json:"last_activity"`              // 最后活动时间
	LoginTime         *time.Time `json:"login_time"`                 // 登录时间
	BanTime           *time.Time `json:"ban_time"`                   // 封号时间
	BanReason         string    `json:"ban_reason" gorm:"type:text"` // 封号原因
	Error             string    `json:"error" gorm:"type:text"`     // 错误信息
	IsActive          bool      `json:"is_active" gorm:"default:true"` // 是否启用
	AutoReconnect     bool      `json:"auto_reconnect" gorm:"default:true"` // 自动重连
	MaxReconnectAttempts int    `json:"max_reconnect_attempts" gorm:"default:3"` // 最大重连次数
	ReconnectAttempts int       `json:"reconnect_attempts" gorm:"default:0"` // 当前重连次数
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	
	Tenant            Tenant        `json:"tenant" gorm:"foreignKey:TenantID"`
	Group             *WhatsAppGroup `json:"group" gorm:"foreignKey:GroupID"`
}

// 初始化WhatsApp账号和分组系统
func initWhatsAppSystem() error {
	// 自动迁移数据库
	err := db.AutoMigrate(&WhatsAppGroup{}, &WhatsAppAccount{})
	if err != nil {
		return err
	}

	// 为每个租户创建默认分组
	var tenants []Tenant
	if err := db.Find(&tenants).Error; err != nil {
		return err
	}

	for _, tenant := range tenants {
		// 检查是否已有默认分组
		var existingGroup WhatsAppGroup
		if err := db.Where("tenant_id = ? AND name = ?", tenant.ID, "默认分组").First(&existingGroup).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建默认分组
				defaultGroup := WhatsAppGroup{
					TenantID:    tenant.ID,
					Name:        "默认分组",
					Description: "系统默认分组",
					Color:       "#409EFF",
					Status:      true,
					SortOrder:   0,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
				if err := db.Create(&defaultGroup).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
} 