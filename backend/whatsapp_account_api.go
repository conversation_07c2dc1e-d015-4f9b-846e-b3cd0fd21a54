package main

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// 获取WhatsApp账号列表
func getWhatsAppAccountList(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var accounts []WhatsAppAccount
	
	// 所有用户都只查看当前租户的账号
	if user.TenantID == nil {
		errorResponse(c, 403, "用户必须属于某个租户才能查看WhatsApp账号")
		return
	}
	
	result := db.Preload("Group").Where("tenant_id = ?", *user.TenantID).Find(&accounts)
	if result.Error != nil {
		errorResponse(c, 500, "获取WhatsApp账号列表失败")
		return
	}

	successResponse(c, gin.H{
		"accounts": accounts,
		"total":    len(accounts),
	})
}

// 根据ID获取WhatsApp账号
func getWhatsAppAccountById(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var account WhatsAppAccount
	result := db.Preload("Group").Preload("Tenant").First(&account, id)
	if result.Error != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 所有用户都只能查看当前租户的账号
	if user.TenantID == nil {
		errorResponse(c, 403, "用户必须属于某个租户才能查看WhatsApp账号")
		return
	}
	
	if account.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权访问此WhatsApp账号")
		return
	}

	successResponse(c, account)
}

// 创建WhatsApp账号
func createWhatsAppAccount(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以创建WhatsApp账号
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以创建WhatsApp账号")
		return
	}

	var account WhatsAppAccount
	if err := c.ShouldBindJSON(&account); err != nil {
		errorResponse(c, 400, "请求数据格式错误")
		return
	}

	// 验证必填字段
	if account.AccountName == "" {
		errorResponse(c, 400, "账号名称不能为空")
		return
	}
	if account.PhoneNumber == "" {
		errorResponse(c, 400, "手机号码不能为空")
		return
	}

	// 设置租户ID
	if user.TenantID != nil {
		account.TenantID = *user.TenantID
	} else {
		errorResponse(c, 400, "用户必须属于某个租户")
		return
	}

	// 检查手机号码是否已存在
	var existingAccount WhatsAppAccount
	if err := db.Where("phone_number = ?", account.PhoneNumber).First(&existingAccount).Error; err == nil {
		errorResponse(c, 400, "手机号码已存在")
		return
	}

	// 生成唯一的SessionID
	timestamp := time.Now().Unix()
	account.SessionID = fmt.Sprintf("hive-%d-%s", timestamp, strconv.FormatInt(time.Now().UnixNano(), 36))

	// 设置默认值
	if account.Status == "" {
		account.Status = "disconnected"
	}
	if account.ConnectionStatus == "" {
		account.ConnectionStatus = "disconnected"
	}
	account.CreatedAt = time.Now()
	account.UpdatedAt = time.Now()

	result := db.Create(&account)
	if result.Error != nil {
		errorResponse(c, 500, "创建WhatsApp账号失败")
		return
	}

	successResponse(c, account)
}

// 更新WhatsApp账号
func updateWhatsAppAccount(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以更新WhatsApp账号
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以更新WhatsApp账号")
		return
	}

	var account WhatsAppAccount
	if err := db.First(&account, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 租户级用户只能更新自己租户的账号
	if user.TenantID != nil && account.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权更新此WhatsApp账号")
		return
	}

	var updateData WhatsAppAccount
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "请求数据格式错误")
		return
	}

	// 只允许更新特定字段
	updates := map[string]interface{}{
		"account_name": updateData.AccountName,
		"group_id":     updateData.GroupID,
		"is_active":    updateData.IsActive,
		"auto_reconnect": updateData.AutoReconnect,
		"max_reconnect_attempts": updateData.MaxReconnectAttempts,
		"remark":       updateData.Remark,
		"account_status": updateData.AccountStatus,
		"updated_at":   time.Now(),
	}

	result := db.Model(&account).Updates(updates)
	if result.Error != nil {
		errorResponse(c, 500, "更新WhatsApp账号失败")
		return
	}

	successResponse(c, account)
}

// 删除WhatsApp账号
func deleteWhatsAppAccount(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	// 检查权限：只有管理员可以删除WhatsApp账号
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		errorResponse(c, 403, "只有管理员可以删除WhatsApp账号")
		return
	}

	var account WhatsAppAccount
	if err := db.First(&account, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 租户级用户只能删除自己租户的账号
	if user.TenantID != nil && account.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权删除此WhatsApp账号")
		return
	}

	// 先清理Session文件
	if account.SessionID != "" {
		// 调用Node服务清理Session
		nodeReq := NodeServiceRequest{
			SessionID:   account.SessionID,
			PhoneNumber: account.PhoneNumber,
			TenantID:    *user.TenantID,
			Action:      "cleanup",
		}

		_, err := callNodeService(nodeReq)
		if err != nil {
			// 记录错误但不阻止删除
			fmt.Printf("清理Session失败: %v\n", err)
		}
	}

	// 删除数据库记录
	result := db.Delete(&account)
	if result.Error != nil {
		errorResponse(c, 500, "删除WhatsApp账号失败")
		return
	}

	successResponse(c, gin.H{"message": "WhatsApp账号删除成功"})
}

// 获取WhatsApp账号二维码
func getWhatsAppAccountQRCode(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var account WhatsAppAccount
	if err := db.First(&account, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 租户级用户只能访问自己租户的账号
	if user.TenantID != nil && account.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权访问此WhatsApp账号")
		return
	}

	// 这里应该调用WhatsApp服务获取二维码
	// 暂时返回模拟数据
	successResponse(c, gin.H{
		"qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		"expires_at": time.Now().Add(5 * time.Minute),
	})
}

// 连接WhatsApp账号
func connectWhatsAppAccount(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var account WhatsAppAccount
	if err := db.First(&account, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 租户级用户只能操作自己租户的账号
	if user.TenantID != nil && account.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权操作此WhatsApp账号")
		return
	}

	// 更新连接状态
	updates := map[string]interface{}{
		"connection_status": "connecting",
		"updated_at":        time.Now(),
	}

	result := db.Model(&account).Updates(updates)
	if result.Error != nil {
		errorResponse(c, 500, "更新WhatsApp账号状态失败")
		return
	}

	successResponse(c, gin.H{"message": "正在连接WhatsApp账号"})
}

// 断开WhatsApp账号
func disconnectWhatsAppAccount(c *gin.Context) {
	id := c.Param("id")
	
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var account WhatsAppAccount
	if err := db.First(&account, id).Error; err != nil {
		errorResponse(c, 404, "WhatsApp账号不存在")
		return
	}

	// 租户级用户只能操作自己租户的账号
	if user.TenantID != nil && account.TenantID != *user.TenantID {
		errorResponse(c, 403, "无权操作此WhatsApp账号")
		return
	}

	// 更新连接状态
	updates := map[string]interface{}{
		"connection_status": "disconnected",
		"qr_code":          "",
		"qr_code_expires_at": nil,
		"error":            "",
		"updated_at":       time.Now(),
	}

	result := db.Model(&account).Updates(updates)
	if result.Error != nil {
		errorResponse(c, 500, "更新WhatsApp账号状态失败")
		return
	}

	successResponse(c, gin.H{"message": "WhatsApp账号已断开"})
} 

// 获取登录二维码
func getQRCodeForLogin(c *gin.Context) {
	// 检查权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(401, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user User
	if err := db.Preload("Tenant").First(&user, userID).Error; err != nil {
		c.JSON(404, gin.H{"error": "用户不存在"})
		return
	}

	// 检查是否为租户管理员或系统超级管理员
	if user.Role != "admin" && user.Role != "manager" {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	// 生成临时会话ID
	sessionID := fmt.Sprintf("hive-%d-%s", time.Now().Unix(), generateRandomString(6))
	
	// 这里应该调用whatsapp-service的API来获取二维码
	// 暂时返回模拟数据
	qrCodeData := "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
	
	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"session_id": sessionID,
			"qr_code": qrCodeData,
			"expires_at": time.Now().Add(5 * time.Minute),
		},
	})
}

// 获取账号登录状态
func getAccountLoginStatus(c *gin.Context) {
	// 检查权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(401, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user User
	if err := db.Preload("Tenant").First(&user, userID).Error; err != nil {
		c.JSON(404, gin.H{"error": "用户不存在"})
		return
	}

	// 检查是否为租户管理员或系统超级管理员
	if user.Role != "admin" && user.Role != "manager" {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	accountID := c.Param("id")
	
	var account WhatsAppAccount
	if err := db.Preload("Group").First(&account, accountID).Error; err != nil {
		c.JSON(404, gin.H{"error": "账号不存在"})
		return
	}

	// 检查租户权限
	if user.Role == "manager" && user.TenantID != nil && account.TenantID != *user.TenantID {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	// 这里应该调用whatsapp-service的API来检查登录状态
	// 暂时返回模拟数据
	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"account_id": account.ID,
			"session_id": account.SessionID,
			"status": account.ConnectionStatus,
			"phone_number": account.PhoneNumber,
			"nickname": account.Nickname,
			"avatar": account.Avatar,
			"is_connected": account.ConnectionStatus == "connected",
		},
	})
}

// 更新账号状态
func updateAccountStatus(c *gin.Context) {
	// 检查权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(401, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user User
	if err := db.Preload("Tenant").First(&user, userID).Error; err != nil {
		c.JSON(404, gin.H{"error": "用户不存在"})
		return
	}

	// 检查是否为管理员
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	accountID := c.Param("id")
	
	var account WhatsAppAccount
	if err := db.Preload("Group").First(&account, accountID).Error; err != nil {
		c.JSON(404, gin.H{"error": "账号不存在"})
		return
	}

	// 检查租户权限：所有用户都只能操作当前租户的账号
	if user.TenantID == nil || account.TenantID != *user.TenantID {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	var req struct {
		AccountStatus string `json:"account_status"`
		BanReason     string `json:"ban_reason"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "请求参数错误"})
		return
	}

	// 更新账号状态
	updates := map[string]interface{}{
		"account_status": req.AccountStatus,
	}

	if req.AccountStatus == "banned" {
		now := time.Now()
		updates["ban_time"] = &now
		updates["ban_reason"] = req.BanReason
	} else if req.AccountStatus == "normal" {
		updates["ban_time"] = nil
		updates["ban_reason"] = ""
	}

	if err := db.Model(&account).Updates(updates).Error; err != nil {
		c.JSON(500, gin.H{"error": "更新失败"})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "状态更新成功",
	})
}

// 获取账号头像
func getAccountAvatar(c *gin.Context) {
	// 检查权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(401, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user User
	if err := db.Preload("Tenant").First(&user, userID).Error; err != nil {
		c.JSON(404, gin.H{"error": "用户不存在"})
		return
	}

	// 检查是否为管理员
	if user.Role != "admin" && user.Role != "manager" && user.Role != "super_admin" {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	accountID := c.Param("id")
	
	var account WhatsAppAccount
	if err := db.Preload("Group").First(&account, accountID).Error; err != nil {
		c.JSON(404, gin.H{"error": "账号不存在"})
		return
	}

	// 检查租户权限：所有用户都只能操作当前租户的账号
	if user.TenantID == nil || account.TenantID != *user.TenantID {
		c.JSON(403, gin.H{"error": "权限不足"})
		return
	}

	// 这里应该调用whatsapp-service的API来获取头像
	// 暂时返回默认头像
	avatarURL := account.Avatar
	if avatarURL == "" {
		avatarURL = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2ZyB4PSIxMCIgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIiBmaWxsPSJub25lIj4KPHBhdGggZD0iTTEwIDUuNUMxMC4yNzYxIDUuNSAxMC41IDUuNzIzODYgMTAuNSA2VjE0QzEwLjUgMTQuMjc2MSAxMC4yNzYxIDE0LjUgMTAgMTQuNUM5LjcyMzg2IDE0LjUgOS41IDE0LjI3NjEgOS41IDE0VjZDOS41IDUuNzIzODYgOS43MjM4NiA1LjUgMTAgNS41WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTE1IDkuNUg1QzQuNzIzODYgOS41IDQuNSA5LjcyMzg2IDQuNSAxMEM0LjUgMTAuMjc2MSA0LjcyMzg2IDEwLjUgNSAxMC41SDE1QzE1LjI3NjEgMTAuNSAxNS41IDEwLjI3NjEgMTUuNSAxMEMxNS41IDkuNzIzODYgMTUuMjc2MSA5LjUgMTUgOS41WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo="
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"avatar_url": avatarURL,
		},
	})
}

// 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
} 