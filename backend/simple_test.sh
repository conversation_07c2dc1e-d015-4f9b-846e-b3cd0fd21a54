#!/bin/bash

echo "=== 简化租户切换测试 ==="

# 1. 登录获取token
echo "1. 登录获取token..."
TOKEN=$(curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"manager","password":"admin123"}' | jq -r '.data.token')

echo "Token: $TOKEN"

# 2. 获取当前租户信息
echo -e "\n2. 获取当前租户信息..."
curl -s -X GET http://localhost:8081/api/tenant/current \
  -H "Authorization: Bearer $TOKEN" | jq '.'

# 3. 获取可访问的租户列表
echo -e "\n3. 获取可访问的租户列表..."
curl -s -X GET http://localhost:8081/api/tenant/accessible \
  -H "Authorization: Bearer $TOKEN" | jq '.'

# 4. 切换到租户2
echo -e "\n4. 切换到租户2..."
SWITCH_RESPONSE=$(curl -s -X POST http://localhost:8081/api/tenant/switch \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": 2,
    "reason": "测试租户切换功能"
  }')

echo $SWITCH_RESPONSE | jq '.'

# 5. 获取新token
NEW_TOKEN=$(echo $SWITCH_RESPONSE | jq -r '.data.new_token')
echo "新Token: $NEW_TOKEN"

# 6. 使用新token获取当前租户信息
echo -e "\n6. 使用新token获取当前租户信息..."
curl -s -X GET http://localhost:8081/api/tenant/current \
  -H "Authorization: Bearer $NEW_TOKEN" | jq '.'

# 7. 获取切换历史
echo -e "\n7. 获取切换历史..."
curl -s -X GET http://localhost:8081/api/tenant/switch-history \
  -H "Authorization: Bearer $NEW_TOKEN" | jq '.'

# 8. 切换回原租户
echo -e "\n8. 切换回原租户..."
curl -s -X POST http://localhost:8081/api/tenant/switch \
  -H "Authorization: Bearer $NEW_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": 1,
    "reason": "切换回默认租户"
  }' | jq '.'

echo -e "\n=== 测试完成 ===" 