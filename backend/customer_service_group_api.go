package main

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// 获取客服分组列表
func getCustomerServiceGroupList(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var groups []CustomerServiceGroup
	query := db.Preload("Tenant")

	// 权限控制：系统管理员可以查看所有，租户管理员只能查看自己租户的
	if user.UserType == "system" && user.Role == "super_admin" {
		// 系统管理员可以查看所有分组
		if tenantID := c.Query("tenant_id"); tenantID != "" {
			if tid, err := strconv.ParseUint(tenantID, 10, 32); err == nil {
				query = query.Where("tenant_id = ?", uint(tid))
			}
		}
	} else {
		// 租户管理员只能查看自己租户的分组
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法访问其他租户的分组")
			return
		}
		query = query.Where("tenant_id = ?", *user.TenantID)
	}

	// 搜索过滤
	if groupName := c.Query("group_name"); groupName != "" {
		query = query.Where("group_name LIKE ?", "%"+groupName+"%")
	}

	if err := query.Find(&groups).Error; err != nil {
		errorResponse(c, 500, "获取分组列表失败")
		return
	}

	successResponse(c, gin.H{
		"groups": groups,
		"total":  len(groups),
	})
}

// 根据ID获取客服分组详情
func getCustomerServiceGroupById(c *gin.Context) {
	id := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var group CustomerServiceGroup
	if err := db.Preload("Tenant").Preload("CustomerServices").First(&group, id).Error; err != nil {
		errorResponse(c, 404, "分组不存在")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || group.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权访问此分组")
			return
		}
	}

	successResponse(c, group)
}

// 创建客服分组
func createCustomerServiceGroup(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var newGroup CustomerServiceGroup
	if err := c.ShouldBindJSON(&newGroup); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 设置租户ID
	if user.UserType == "system" && user.Role == "super_admin" {
		// 系统管理员需要指定租户ID
		if newGroup.TenantID == 0 {
			errorResponse(c, 400, "系统管理员创建分组时必须指定租户ID")
			return
		}
	} else {
		// 租户管理员只能在自己的租户中创建分组
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法在其他租户中创建分组")
			return
		}
		newGroup.TenantID = *user.TenantID
	}

	// 检查分组名称是否已存在
	var existingGroup CustomerServiceGroup
	if err := db.Where("group_name = ? AND tenant_id = ?", newGroup.GroupName, newGroup.TenantID).First(&existingGroup).Error; err == nil {
		errorResponse(c, 400, "分组名称已存在")
		return
	}

	// 设置默认值
	if newGroup.DailyAssignLimit == 0 {
		newGroup.DailyAssignLimit = 100
	}

	if err := db.Create(&newGroup).Error; err != nil {
		errorResponse(c, 500, "创建分组失败")
		return
	}

	successResponse(c, newGroup)
}

// 更新客服分组
func updateCustomerServiceGroup(c *gin.Context) {
	id := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var group CustomerServiceGroup
	if err := db.First(&group, id).Error; err != nil {
		errorResponse(c, 404, "分组不存在")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || group.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权修改此分组")
			return
		}
	}

	var updateData CustomerServiceGroup
	if err := c.ShouldBindJSON(&updateData); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 更新字段
	group.GroupName = updateData.GroupName
	group.Description = updateData.Description
	group.DailyAssignLimit = updateData.DailyAssignLimit
	group.IsAssignNewSession = updateData.IsAssignNewSession

	if err := db.Save(&group).Error; err != nil {
		errorResponse(c, 500, "更新分组失败")
		return
	}

	successResponse(c, group)
}

// 删除客服分组
func deleteCustomerServiceGroup(c *gin.Context) {
	id := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var group CustomerServiceGroup
	if err := db.First(&group, id).Error; err != nil {
		errorResponse(c, 404, "分组不存在")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || group.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权删除此分组")
			return
		}
	}

	// 检查分组下是否还有客服
	var serviceCount int64
	db.Model(&CustomerServiceProfile{}).Where("group_id = ?", group.ID).Count(&serviceCount)
	if serviceCount > 0 {
		errorResponse(c, 400, "分组下还有客服，无法删除")
		return
	}

	if err := db.Delete(&group).Error; err != nil {
		errorResponse(c, 500, "删除分组失败")
		return
	}

	successResponse(c, gin.H{"message": "分组删除成功"})
}

// 批量操作客服分组
func batchUpdateCustomerServiceGroups(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var req struct {
		GroupIDs []uint `json:"group_ids" binding:"required"`
		Action   string `json:"action" binding:"required"`
		Data     map[string]interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		errorResponse(c, 400, "请求参数错误")
		return
	}

	// 权限检查：确保所有分组都属于当前用户的租户
	var groups []CustomerServiceGroup
	query := db.Where("id IN ?", req.GroupIDs)
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil {
			errorResponse(c, 403, "租户级管理员无法操作其他租户的分组")
			return
		}
		query = query.Where("tenant_id = ?", *user.TenantID)
	}

	if err := query.Find(&groups).Error; err != nil {
		errorResponse(c, 500, "查询分组失败")
		return
	}

	if len(groups) != len(req.GroupIDs) {
		errorResponse(c, 403, "部分分组不存在或无权操作")
		return
	}

	// 执行批量操作
	updates := make(map[string]interface{})
	switch req.Action {
	case "set_daily_limit":
		if limit, ok := req.Data["daily_assign_limit"].(float64); ok {
			updates["daily_assign_limit"] = int(limit)
		}
	case "enable_assign_session":
		updates["is_assign_new_session"] = true
	case "disable_assign_session":
		updates["is_assign_new_session"] = false
	default:
		errorResponse(c, 400, "不支持的操作类型")
		return
	}

	if err := db.Model(&CustomerServiceGroup{}).Where("id IN ?", req.GroupIDs).Updates(updates).Error; err != nil {
		errorResponse(c, 500, "批量更新失败")
		return
	}

	// 如果是批量操作分组的分配会话设置，同时更新分组下的所有客服
	if req.Action == "enable_assign_session" || req.Action == "disable_assign_session" {
		assignSession := req.Action == "enable_assign_session"
		db.Model(&CustomerServiceProfile{}).Where("group_id IN ?", req.GroupIDs).Update("is_assign_new_session", assignSession)
	}

	successResponse(c, gin.H{"message": "批量操作成功"})
}

// 获取分组下的客服列表
func getGroupCustomerServices(c *gin.Context) {
	groupID := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		errorResponse(c, 401, "用户未认证")
		return
	}

	var user User
	if err := db.First(&user, userID).Error; err != nil {
		errorResponse(c, 404, "用户不存在")
		return
	}

	var group CustomerServiceGroup
	if err := db.First(&group, groupID).Error; err != nil {
		errorResponse(c, 404, "分组不存在")
		return
	}

	// 权限检查
	if user.UserType != "system" || user.Role != "super_admin" {
		if user.TenantID == nil || group.TenantID != *user.TenantID {
			errorResponse(c, 403, "无权访问此分组")
			return
		}
	}

	// 获取分组下的客服档案，并关联用户信息
	var profiles []CustomerServiceProfile
	if err := db.Preload("User").Where("group_id = ?", groupID).Find(&profiles).Error; err != nil {
		errorResponse(c, 500, "获取客服列表失败")
		return
	}

	// 转换为客服信息格式
	var services []map[string]interface{}
	for _, profile := range profiles {
		service := map[string]interface{}{
			"id":                      profile.User.ID,
			"service_account":         profile.User.Username,
			"service_nickname":        profile.User.RealName,
			"email":                   profile.User.Email,
			"phone":                   profile.User.Phone,
			"status":                  getStatusFromUser(profile.User.Status),
			"service_type":            profile.ServiceType,
			"daily_assign_limit":      profile.DailyAssignLimit,
			"is_assign_new_session":   profile.IsAssignNewSession,
			"hide_customer_ws_number": profile.HideCustomerWSNumber,
			"is_customer_group_send":  profile.IsCustomerGroupSend,
			"created_at":              profile.User.CreatedAt,
			"updated_at":              profile.User.UpdatedAt,
		}
		services = append(services, service)
	}

	successResponse(c, gin.H{
		"services": services,
		"total":    len(services),
	})
}


