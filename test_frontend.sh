#!/bin/bash

# 前端测试脚本
FRONTEND_URL="http://localhost:5173"
BACKEND_URL="http://localhost:8081"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 检查服务状态
check_services() {
    print_info "检查服务状态..."
    
    # 检查前端服务
    if curl --noproxy "*" -s "$FRONTEND_URL" > /dev/null; then
        print_success "前端服务运行正常 (http://localhost:5173)"
    else
        print_error "前端服务未运行"
        return 1
    fi
    
    # 检查后端服务
    if curl --noproxy "*" -s "$BACKEND_URL/health" > /dev/null; then
        print_success "后端服务运行正常 (http://localhost:8081)"
    else
        print_error "后端服务未运行"
        return 1
    fi
}

# 测试登录功能
test_login() {
    print_info "测试登录功能..."
    
    # 获取登录页面
    LOGIN_PAGE=$(curl --noproxy "*" -s "$FRONTEND_URL/login")
    if echo "$LOGIN_PAGE" | grep -q "DOCTYPE html"; then
        print_success "登录页面可访问"
    else
        print_error "登录页面无法访问"
    fi
}

# 测试WhatsApp页面路由
test_whatsapp_routes() {
    print_info "测试WhatsApp页面路由..."
    
    # 测试账号管理页面
    ACCOUNTS_PAGE=$(curl --noproxy "*" -s "$FRONTEND_URL/whatsapp/accounts")
    if echo "$ACCOUNTS_PAGE" | grep -q "DOCTYPE html"; then
        print_success "WhatsApp账号管理页面路由正常"
    else
        print_error "WhatsApp账号管理页面路由异常"
    fi
    
    # 测试分组管理页面
    GROUPS_PAGE=$(curl --noproxy "*" -s "$FRONTEND_URL/whatsapp/groups")
    if echo "$GROUPS_PAGE" | grep -q "DOCTYPE html"; then
        print_success "WhatsApp分组管理页面路由正常"
    else
        print_error "WhatsApp分组管理页面路由异常"
    fi
}

# 显示访问信息
show_access_info() {
    print_info "前端访问信息："
    echo "  🌐 前端地址: http://localhost:5173"
    echo "  🔧 后端地址: http://localhost:8081"
    echo ""
    echo "📱 WhatsApp管理页面："
    echo "  📋 账号管理: http://localhost:5173/whatsapp/accounts"
    echo "  📁 分组管理: http://localhost:5173/whatsapp/groups"
    echo ""
    echo "🔑 测试账号："
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    echo "💡 使用说明："
    echo "  1. 打开浏览器访问 http://localhost:5173"
    echo "  2. 使用admin/admin123登录"
    echo "  3. 在左侧菜单找到'WhatsApp管理'"
    echo "  4. 点击'账号管理'或'分组管理'"
}

# 主函数
main() {
    print_info "开始前端测试..."
    
    check_services
    if [ $? -ne 0 ]; then
        print_error "服务检查失败，请确保前后端服务都已启动"
        exit 1
    fi
    
    test_login
    test_whatsapp_routes
    
    echo ""
    show_access_info
    
    print_success "前端测试完成！"
}

# 运行主函数
main 