<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .button:hover {
            background: #337ecc;
        }
        .success {
            color: #67c23a;
            font-weight: bold;
        }
        .error {
            color: #f56c6c;
            font-weight: bold;
        }
        .info {
            color: #909399;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录测试</h1>
        <p class="info">测试登录功能，系统级超级管理员账户：admin / admin123</p>
        
        <div class="form-group">
            <label for="username">用户名：</label>
            <input type="text" id="username" name="username" value="admin" required>
        </div>
        
        <div class="form-group">
            <label for="password">密码：</label>
            <input type="password" id="password" name="password" value="admin123" required>
        </div>
        
        <button type="submit" class="button">登录测试</button>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>测试说明：</h3>
            <ul>
                <li><strong>系统级超级管理员账户：</strong>admin / admin123</li>
                <li><strong>租户级超级管理员账户：</strong>super_admin_Hive SaaS / admin123</li>
                <li><strong>普通管理员账户：</strong>admin_Hive SaaS / admin123</li>
                <li>系统级超级管理员不属于任何租户，可以管理所有租户</li>
                <li>登录成功后会自动保存token到localStorage</li>
                <li>可以多次尝试登录，不会再有锁定限制</li>
                <li>如果遇到问题，请先访问 <a href="frontend/clear-lock.html">清除锁定状态</a></li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8081/api';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<p class="info">登录中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    // 保存token到localStorage
                    localStorage.setItem('token', result.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(result.data.user_info));
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 登录成功！</h3>
                            <p><strong>用户：</strong>${result.data.user_info.username}</p>
                            <p><strong>角色：</strong>${result.data.user_info.role}</p>
                            <p><strong>租户：</strong>${result.data.user_info.tenant?.name || '未知'}</p>
                            <p><strong>Token：</strong>${result.data.token.substring(0, 20)}...</p>
                            <p><a href="http://localhost:5173" target="_blank">前往前端应用</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 登录失败：${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误：${error.message}</div>`;
            }
        });
        
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const result = await response.json();
                console.log('后端服务状态：', result);
            } catch (error) {
                console.error('后端服务连接失败：', error);
            }
        }
        
        // 页面加载时检查服务状态
        window.onload = function() {
            checkServiceStatus();
        };
    </script>
</body>
</html> 