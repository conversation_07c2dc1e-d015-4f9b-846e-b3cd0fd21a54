import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 主测试函数
async function runReconnectTest() {
    // 创建测试目录
    const TEST_DIR = './test-data';
    if (!fs.existsSync(TEST_DIR)) {
        fs.mkdirSync(TEST_DIR, { recursive: true });
    }

    console.log('🚀 开始WhatsApp离线重连测试...\n');

    // 测试配置 - 使用固定的session ID
    const SESSION_ID = 'reconnect-test-session';
    const AUTH_PATH = path.join(TEST_DIR, 'auth', SESSION_ID);

    console.log('📋 测试配置:');
    console.log('  - Session ID:', SESSION_ID);
    console.log('  - Auth Path:', AUTH_PATH);

    // 检查是否存在认证数据
    const sessionDataPath = path.join(TEST_DIR, 'auth', `session-${SESSION_ID}`);
    const hasAuthData = fs.existsSync(sessionDataPath);
    
    console.log('📋 认证数据检查:');
    console.log('  - 认证数据存在:', hasAuthData);
    if (hasAuthData) {
        console.log('  - 认证数据路径:', sessionDataPath);
        const files = fs.readdirSync(sessionDataPath);
        console.log('  - 认证文件数量:', files.length);
    }

    // 创建客户端配置
    const clientOptions = {
        authStrategy: new LocalAuth({
            clientId: SESSION_ID,
            dataPath: path.join(TEST_DIR, 'auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps'
            ]
        }
    };

    console.log('\n📋 测试1: 客户端创建');
    let client;
    try {
        client = new Client(clientOptions);
        console.log('✅ 客户端创建成功');
    } catch (error) {
        console.error('❌ 客户端创建失败:', error);
        process.exit(1);
    }

    // 设置事件监听器
    console.log('\n📋 测试2: 事件监听器设置');

    let qrReceived = false;
    let authenticated = false;
    let ready = false;
    let error = null;
    let clientInfo = null;

    client.on('qr', (qr) => {
        console.log('📱 收到QR码');
        console.log('📝 QR码数据长度:', qr.length);
        qrReceived = true;
        
        // 生成QR码图片
        const qrPath = path.join(TEST_DIR, `${SESSION_ID}-qr.png`);
        QRCode.toFile(qrPath, qr, (err) => {
            if (err) {
                console.error('❌ QR码图片生成失败:', err);
            } else {
                console.log('✅ QR码图片已保存到:', qrPath);
            }
        });
        
        // 在控制台显示QR码
        QRCode.toString(qr, { type: 'terminal', small: true }, (err, string) => {
            if (!err) {
                console.log('\n📱 终端QR码:');
                console.log(string);
            }
        });
    });

    client.on('authenticated', () => {
        console.log('✅ 客户端已认证');
        authenticated = true;
    });

    client.on('auth_failure', (msg) => {
        console.log('❌ 认证失败:', msg);
        error = msg;
    });

    client.on('ready', () => {
        console.log('✅ 客户端准备就绪');
        ready = true;
        
        // 获取客户端信息
        clientInfo = {
            wid: client.info?.wid,
            platform: client.info?.platform,
            pushname: client.info?.pushname,
            businessName: client.info?.businessName,
            businessHours: client.info?.businessHours,
            description: client.info?.description,
            email: client.info?.email,
            website: client.info?.website,
            latitude: client.info?.latitude,
            longitude: client.info?.longitude,
            address: client.info?.address,
            category: client.info?.category,
            subcategory: client.info?.subcategory,
            isBusiness: client.info?.isBusiness,
            isEnterprise: client.info?.isEnterprise,
            isHighLevelVerified: client.info?.isHighLevelVerified,
            isMe: client.info?.isMe,
            isMyContact: client.info?.isMyContact,
            isPSA: client.info?.isPSA,
            isUser: client.info?.isUser,
            isVerified: client.info?.isVerified,
            isWAContact: client.info?.isWAContact,
            labels: client.info?.labels,
            status: client.info?.status,
            statusMute: client.info?.statusMute,
            statusTimestamp: client.info?.statusTimestamp,
            unreadCount: client.info?.unreadCount
        };
        
        console.log('📱 客户端详细信息:');
        console.log(JSON.stringify(clientInfo, null, 2));
        
        // 保存客户端信息到文件
        const infoPath = path.join(TEST_DIR, `${SESSION_ID}-info.json`);
        fs.writeFileSync(infoPath, JSON.stringify(clientInfo, null, 2));
        console.log('✅ 客户端信息已保存到:', infoPath);
    });

    client.on('disconnected', (reason) => {
        console.log('🔌 客户端断开连接:', reason);
    });

    console.log('✅ 事件监听器设置完成');

    // 启动客户端
    console.log('\n📋 测试3: 客户端初始化');
    if (hasAuthData) {
        console.log('⚠️  检测到认证数据，尝试离线重连...');
    } else {
        console.log('⚠️  未检测到认证数据，需要扫码登录...');
    }
    console.log('📱 请扫描终端中显示的QR码完成登录（如果需要）');

    try {
        await client.initialize();
        console.log('✅ 客户端初始化成功');
    } catch (error) {
        console.error('❌ 客户端初始化失败:', error);
        process.exit(1);
    }

    // 等待认证完成
    console.log('\n📋 测试4: 等待认证完成');
    console.log('⏳ 等待60秒完成认证...');

    let timeElapsed = 0;
    const interval = setInterval(async () => {
        timeElapsed += 10;
        console.log(`⏱️  已等待 ${timeElapsed} 秒...`);
        
        if (qrReceived) {
            console.log('📱 QR码已收到');
        }
        
        if (authenticated) {
            console.log('✅ 已认证');
        }
        
        if (ready) {
            console.log('✅ 客户端已就绪');
            console.log('📱 客户端信息已获取');
        }
        
        if (error) {
            console.log('❌ 发生错误:', error);
        }
        
        if (timeElapsed >= 60 || ready) {
            clearInterval(interval);
            console.log('\n📋 认证测试完成');
            console.log('📊 最终状态:');
            console.log('  - QR码收到:', qrReceived);
            console.log('  - 已认证:', authenticated);
            console.log('  - 已就绪:', ready);
            console.log('  - 错误:', error || '无');
            
            if (ready && clientInfo) {
                console.log('\n📋 测试5: 重连验证测试');
                
                // 测试重连功能
                await testReconnectFunctionality(client, SESSION_ID, TEST_DIR);
            }
            
            // 断开客户端
            console.log('\n🔌 断开客户端...');
            client.destroy().then(() => {
                console.log('✅ 客户端已断开');
                process.exit(0);
            }).catch((err) => {
                console.error('❌ 断开客户端失败:', err);
                process.exit(0);
            });
        }
    }, 10000);

    // 处理进程退出
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        clearInterval(interval);
        if (client) {
            try {
                await client.destroy();
            } catch (err) {
                console.error('❌ 断开客户端失败:', err);
            }
        }
        process.exit(0);
    });
}

// 测试重连功能
async function testReconnectFunctionality(client, sessionId, testDir) {
    console.log('\n📋 重连功能测试');
    
    try {
        // 1. 测试基本功能
        console.log('\n📱 测试1: 基本功能验证');
        
        // 获取状态
        try {
            const state = await client.getState();
            console.log('  ✅ 客户端状态:', state);
        } catch (error) {
            console.log('  ❌ 获取状态失败:', error.message);
        }
        
        // 获取联系人
        try {
            const contacts = await client.getContacts();
            console.log('  ✅ 联系人总数:', contacts.length);
        } catch (error) {
            console.log('  ❌ 获取联系人失败:', error.message);
        }
        
        // 2. 测试发送消息
        console.log('\n📱 测试2: 发送测试消息');
        try {
            const testPhoneNumber = '<EMAIL>';
            const testMessage = 'Reconnect test message. Time: ' + new Date().toISOString();
            
            console.log('  📤 发送消息到:', testPhoneNumber);
            console.log('  📝 消息内容:', testMessage);
            
            const message = await client.sendMessage(testPhoneNumber, testMessage);
            console.log('  ✅ 消息发送成功，ID:', message.id._serialized);
        } catch (error) {
            console.log('  ❌ 发送消息失败:', error.message);
        }
        
        // 3. 测试获取聊天
        console.log('\n📱 测试3: 获取聊天信息');
        try {
            const testPhoneNumber = '<EMAIL>';
            const chat = await client.getChatById(testPhoneNumber);
            console.log('  ✅ 获取聊天成功:', chat.name);
        } catch (error) {
            console.log('  ❌ 获取聊天失败:', error.message);
        }
        
        // 4. 保存重连测试结果
        const reconnectResults = {
            sessionId: sessionId,
            timestamp: new Date().toISOString(),
            tests: {
                getState: { success: true },
                getContacts: { success: true },
                sendMessage: { success: true },
                getChatById: { success: true }
            }
        };
        
        const resultsPath = path.join(testDir, `${sessionId}-reconnect-results.json`);
        fs.writeFileSync(resultsPath, JSON.stringify(reconnectResults, null, 2));
        console.log('\n✅ 重连测试结果已保存到:', resultsPath);
        
        console.log('\n📊 重连测试总结:');
        console.log('  - 离线重连: 成功');
        console.log('  - 基本功能: 正常');
        console.log('  - 消息发送: 正常');
        console.log('  - 聊天获取: 正常');
        
    } catch (error) {
        console.error('❌ 重连测试过程中发生错误:', error);
    }
}

// 运行测试
runReconnectTest().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
}); 