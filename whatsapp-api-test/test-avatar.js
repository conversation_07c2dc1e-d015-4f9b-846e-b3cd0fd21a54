import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 主测试函数
async function runAvatarTest() {
    // 创建测试目录
    const TEST_DIR = './test-data';
    if (!fs.existsSync(TEST_DIR)) {
        fs.mkdirSync(TEST_DIR, { recursive: true });
    }

    console.log('🚀 开始WhatsApp头像获取测试...\n');

    // 测试配置
    const SESSION_ID = 'avatar-test-' + Date.now();
    const AUTH_PATH = path.join(TEST_DIR, 'auth', SESSION_ID);

    console.log('📋 测试配置:');
    console.log('  - Session ID:', SESSION_ID);
    console.log('  - Auth Path:', AUTH_PATH);

    // 创建客户端配置
    const clientOptions = {
        authStrategy: new LocalAuth({
            clientId: SESSION_ID,
            dataPath: path.join(TEST_DIR, 'auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps'
            ]
        }
    };

    console.log('\n📋 测试1: 客户端创建和初始化');
    let client;
    try {
        client = new Client(clientOptions);
        console.log('✅ 客户端创建成功');
    } catch (error) {
        console.error('❌ 客户端创建失败:', error);
        process.exit(1);
    }

    // 设置事件监听器
    console.log('\n📋 测试2: 事件监听器设置');

    let qrReceived = false;
    let authenticated = false;
    let ready = false;
    let error = null;
    let clientInfo = null;

    client.on('qr', (qr) => {
        console.log('📱 收到QR码');
        console.log('📝 QR码数据长度:', qr.length);
        qrReceived = true;
        
        // 生成QR码图片
        const qrPath = path.join(TEST_DIR, `${SESSION_ID}-qr.png`);
        QRCode.toFile(qrPath, qr, (err) => {
            if (err) {
                console.error('❌ QR码图片生成失败:', err);
            } else {
                console.log('✅ QR码图片已保存到:', qrPath);
            }
        });
        
        // 在控制台显示QR码
        QRCode.toString(qr, { type: 'terminal', small: true }, (err, string) => {
            if (!err) {
                console.log('\n📱 终端QR码:');
                console.log(string);
            }
        });
    });

    client.on('authenticated', () => {
        console.log('✅ 客户端已认证');
        authenticated = true;
    });

    client.on('auth_failure', (msg) => {
        console.log('❌ 认证失败:', msg);
        error = msg;
    });

    client.on('ready', () => {
        console.log('✅ 客户端准备就绪');
        ready = true;
        
        // 获取客户端信息
        clientInfo = {
            wid: client.info?.wid,
            platform: client.info?.platform,
            pushname: client.info?.pushname,
            businessName: client.info?.businessName,
            businessHours: client.info?.businessHours,
            description: client.info?.description,
            email: client.info?.email,
            website: client.info?.website,
            latitude: client.info?.latitude,
            longitude: client.info?.longitude,
            address: client.info?.address,
            category: client.info?.category,
            subcategory: client.info?.subcategory,
            isBusiness: client.info?.isBusiness,
            isEnterprise: client.info?.isEnterprise,
            isHighLevelVerified: client.info?.isHighLevelVerified,
            isMe: client.info?.isMe,
            isMyContact: client.info?.isMyContact,
            isPSA: client.info?.isPSA,
            isUser: client.info?.isUser,
            isVerified: client.info?.isVerified,
            isWAContact: client.info?.isWAContact,
            labels: client.info?.labels,
            status: client.info?.status,
            statusMute: client.info?.statusMute,
            statusTimestamp: client.info?.statusTimestamp,
            unreadCount: client.info?.unreadCount
        };
        
        console.log('📱 客户端详细信息:');
        console.log(JSON.stringify(clientInfo, null, 2));
        
        // 保存客户端信息到文件
        const infoPath = path.join(TEST_DIR, `${SESSION_ID}-info.json`);
        fs.writeFileSync(infoPath, JSON.stringify(clientInfo, null, 2));
        console.log('✅ 客户端信息已保存到:', infoPath);
    });

    client.on('disconnected', (reason) => {
        console.log('🔌 客户端断开连接:', reason);
    });

    console.log('✅ 事件监听器设置完成');

    // 启动客户端
    console.log('\n📋 测试3: 客户端初始化');
    console.log('⚠️  注意: 这将启动Puppeteer浏览器');
    console.log('📱 请扫描终端中显示的QR码完成登录');

    try {
        await client.initialize();
        console.log('✅ 客户端初始化成功');
    } catch (error) {
        console.error('❌ 客户端初始化失败:', error);
        process.exit(1);
    }

    // 等待认证完成
    console.log('\n📋 测试4: 等待认证完成');
    console.log('⏳ 等待60秒完成认证...');

    let timeElapsed = 0;
    const interval = setInterval(async () => {
        timeElapsed += 10;
        console.log(`⏱️  已等待 ${timeElapsed} 秒...`);
        
        if (qrReceived) {
            console.log('📱 QR码已收到');
        }
        
        if (authenticated) {
            console.log('✅ 已认证');
        }
        
        if (ready) {
            console.log('✅ 客户端已就绪');
            console.log('📱 客户端信息已获取');
        }
        
        if (error) {
            console.log('❌ 发生错误:', error);
        }
        
        if (timeElapsed >= 60 || ready) {
            clearInterval(interval);
            console.log('\n📋 认证测试完成');
            console.log('📊 最终状态:');
            console.log('  - QR码收到:', qrReceived);
            console.log('  - 已认证:', authenticated);
            console.log('  - 已就绪:', ready);
            console.log('  - 错误:', error || '无');
            
            if (ready && clientInfo) {
                console.log('\n📋 测试5: 头像获取测试');
                
                try {
                    // 测试1: 获取自己的头像
                    console.log('\n📱 测试1: 获取自己的头像');
                    const myWid = clientInfo.wid._serialized;
                    console.log('  - 自己的WID:', myWid);
                    
                    const myProfilePicUrl = await client.getProfilePicUrl(myWid);
                    console.log('  - 头像URL:', myProfilePicUrl);
                    
                    // 保存头像URL到文件
                    const avatarInfo = {
                        wid: myWid,
                        profilePicUrl: myProfilePicUrl,
                        timestamp: new Date().toISOString()
                    };
                    
                    const avatarPath = path.join(TEST_DIR, `${SESSION_ID}-avatar.json`);
                    fs.writeFileSync(avatarPath, JSON.stringify(avatarInfo, null, 2));
                    console.log('✅ 头像信息已保存到:', avatarPath);
                    
                } catch (error) {
                    console.error('❌ 获取自己的头像失败:', error.message);
                }
                
                try {
                    // 测试2: 获取联系人头像（如果有的话）
                    console.log('\n📱 测试2: 获取联系人列表');
                    const contacts = await client.getContacts();
                    console.log('  - 联系人总数:', contacts.length);
                    
                    // 显示前5个联系人
                    const sampleContacts = contacts.slice(0, 5);
                    console.log('  - 前5个联系人:');
                    sampleContacts.forEach((contact, index) => {
                        console.log(`    ${index + 1}. ${contact.pushname || contact.number} (${contact.number})`);
                    });
                    
                    // 尝试获取第一个联系人的头像
                    if (sampleContacts.length > 0) {
                        const firstContact = sampleContacts[0];
                        console.log(`\n📱 测试3: 获取联系人 "${firstContact.pushname || firstContact.number}" 的头像`);
                        
                        try {
                            const contactProfilePicUrl = await client.getProfilePicUrl(firstContact.id._serialized);
                            console.log('  - 联系人头像URL:', contactProfilePicUrl);
                            
                            // 保存联系人头像信息
                            const contactAvatarInfo = {
                                contactId: firstContact.id._serialized,
                                contactName: firstContact.pushname || firstContact.number,
                                profilePicUrl: contactProfilePicUrl,
                                timestamp: new Date().toISOString()
                            };
                            
                            const contactAvatarPath = path.join(TEST_DIR, `${SESSION_ID}-contact-avatar.json`);
                            fs.writeFileSync(contactAvatarPath, JSON.stringify(contactAvatarInfo, null, 2));
                            console.log('✅ 联系人头像信息已保存到:', contactAvatarPath);
                            
                        } catch (error) {
                            console.error('❌ 获取联系人头像失败:', error.message);
                        }
                    }
                    
                } catch (error) {
                    console.error('❌ 获取联系人列表失败:', error.message);
                }
                
                // 测试3: 检查客户端对象的所有方法
                console.log('\n📱 测试4: 检查客户端方法');
                console.log('  - 客户端方法列表:');
                const clientMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(client));
                const relevantMethods = clientMethods.filter(method => 
                    method.includes('profile') || 
                    method.includes('avatar') || 
                    method.includes('pic') ||
                    method.includes('contact') ||
                    method.includes('get')
                );
                
                relevantMethods.forEach(method => {
                    console.log(`    - ${method}`);
                });
                
                // 测试5: 检查客户端info对象的所有属性
                console.log('\n📱 测试5: 检查客户端info对象');
                console.log('  - info对象属性:');
                const infoProperties = Object.keys(client.info || {});
                infoProperties.forEach(prop => {
                    console.log(`    - ${prop}:`, client.info[prop]);
                });
            }
            
            // 断开客户端
            console.log('\n🔌 断开客户端...');
            client.destroy().then(() => {
                console.log('✅ 客户端已断开');
                process.exit(0);
            }).catch((err) => {
                console.error('❌ 断开客户端失败:', err);
                process.exit(0);
            });
        }
    }, 10000);

    // 处理进程退出
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        clearInterval(interval);
        if (client) {
            try {
                await client.destroy();
            } catch (err) {
                console.error('❌ 断开客户端失败:', err);
            }
        }
        process.exit(0);
    });
}

// 运行测试
runAvatarTest().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
}); 