import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 主测试函数
async function runTest() {
    // 创建测试目录
    const TEST_DIR = './test-data';
    if (!fs.existsSync(TEST_DIR)) {
        fs.mkdirSync(TEST_DIR, { recursive: true });
    }

    console.log('🚀 开始WhatsApp客户端创建测试...\n');

    // 测试配置
    const SESSION_ID = 'test-session-' + Date.now();
    const AUTH_PATH = path.join(TEST_DIR, 'auth', SESSION_ID);

    console.log('📋 测试配置:');
    console.log('  - Session ID:', SESSION_ID);
    console.log('  - Auth Path:', AUTH_PATH);

    // 创建客户端配置
    const clientOptions = {
        authStrategy: new LocalAuth({
            clientId: SESSION_ID,
            dataPath: path.join(TEST_DIR, 'auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps'
            ]
        }
    };

    console.log('\n📋 测试1: 客户端创建');
    let client;
    try {
        client = new Client(clientOptions);
        console.log('✅ 客户端创建成功');
    } catch (error) {
        console.error('❌ 客户端创建失败:', error);
        process.exit(1);
    }

    // 设置事件监听器
    console.log('\n📋 测试2: 事件监听器设置');

    let qrReceived = false;
    let authenticated = false;
    let ready = false;
    let error = null;

    client.on('qr', (qr) => {
        console.log('📱 收到QR码');
        console.log('📝 QR码数据长度:', qr.length);
        qrReceived = true;
        
        // 生成QR码图片
        const qrPath = path.join(TEST_DIR, `${SESSION_ID}-qr.png`);
        QRCode.toFile(qrPath, qr, (err) => {
            if (err) {
                console.error('❌ QR码图片生成失败:', err);
            } else {
                console.log('✅ QR码图片已保存到:', qrPath);
            }
        });
        
        // 在控制台显示QR码
        QRCode.toString(qr, { type: 'terminal', small: true }, (err, string) => {
            if (!err) {
                console.log('\n📱 终端QR码:');
                console.log(string);
            }
        });
    });

    client.on('authenticated', () => {
        console.log('✅ 客户端已认证');
        authenticated = true;
    });

    client.on('auth_failure', (msg) => {
        console.log('❌ 认证失败:', msg);
        error = msg;
    });

    client.on('ready', () => {
        console.log('✅ 客户端准备就绪');
        console.log('📱 客户端信息:');
        console.log('  - WID:', client.info?.wid);
        console.log('  - Platform:', client.info?.platform);
        console.log('  - Pushname:', client.info?.pushname);
        ready = true;
    });

    client.on('disconnected', (reason) => {
        console.log('🔌 客户端断开连接:', reason);
    });

    client.on('message', (msg) => {
        console.log('📨 收到消息:', {
            from: msg.from,
            body: msg.body?.substring(0, 50) + '...',
            timestamp: msg.timestamp
        });
    });

    console.log('✅ 事件监听器设置完成');

    // 启动客户端
    console.log('\n📋 测试3: 客户端初始化');
    console.log('⚠️  注意: 这将启动Puppeteer浏览器');
    console.log('📱 请扫描终端中显示的QR码完成登录');

    try {
        await client.initialize();
        console.log('✅ 客户端初始化成功');
    } catch (error) {
        console.error('❌ 客户端初始化失败:', error);
        process.exit(1);
    }

    // 等待一段时间观察状态
    console.log('\n📋 测试4: 状态监控');
    console.log('⏳ 等待30秒观察客户端状态...');

    let timeElapsed = 0;
    const interval = setInterval(() => {
        timeElapsed += 5;
        console.log(`⏱️  已等待 ${timeElapsed} 秒...`);
        
        if (qrReceived) {
            console.log('📱 QR码已收到');
        }
        
        if (authenticated) {
            console.log('✅ 已认证');
        }
        
        if (ready) {
            console.log('✅ 客户端已就绪');
        }
        
        if (error) {
            console.log('❌ 发生错误:', error);
        }
        
        if (timeElapsed >= 30) {
            clearInterval(interval);
            console.log('\n📋 测试完成');
            console.log('📊 最终状态:');
            console.log('  - QR码收到:', qrReceived);
            console.log('  - 已认证:', authenticated);
            console.log('  - 已就绪:', ready);
            console.log('  - 错误:', error || '无');
            
            // 断开客户端
            console.log('\n🔌 断开客户端...');
            client.destroy().then(() => {
                console.log('✅ 客户端已断开');
                process.exit(0);
            }).catch((err) => {
                console.error('❌ 断开客户端失败:', err);
                process.exit(0);
            });
        }
    }, 5000);

    // 处理进程退出
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        clearInterval(interval);
        if (client) {
            try {
                await client.destroy();
            } catch (err) {
                console.error('❌ 断开客户端失败:', err);
            }
        }
        process.exit(0);
    });
}

// 运行测试
runTest().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
}); 