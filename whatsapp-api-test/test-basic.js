import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 创建测试目录
const TEST_DIR = './test-data';
if (!fs.existsSync(TEST_DIR)) {
    fs.mkdirSync(TEST_DIR, { recursive: true });
}

console.log('🚀 开始WhatsApp-web.js基础测试...\n');

// 测试1: 基本导入和模块检查
console.log('📋 测试1: 模块导入检查');
try {
    console.log('✅ whatsapp-web.js 导入成功');
    console.log('✅ qrcode 导入成功');
    console.log('✅ fs 和 path 模块可用');
    console.log('📝 导入的模块:');
    console.log('  - Client:', typeof Client);
    console.log('  - LocalAuth:', typeof LocalAuth);
} catch (error) {
    console.error('❌ 模块导入失败:', error);
}

// 测试2: 客户端配置选项
console.log('\n📋 测试2: 客户端配置选项');
const clientOptions = {
    authStrategy: new LocalAuth({
        clientId: 'test-client',
        dataPath: path.join(TEST_DIR, 'auth')
    }),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--disable-javascript',
            '--disable-default-apps'
        ]
    }
};

console.log('✅ 客户端配置选项创建成功');
console.log('📝 配置详情:');
console.log('  - Auth Strategy: LocalAuth');
console.log('  - Client ID: test-client');
console.log('  - Data Path:', path.join(TEST_DIR, 'auth'));
console.log('  - Puppeteer: headless mode');

// 测试3: 客户端实例创建
console.log('\n📋 测试3: 客户端实例创建');
let client;
try {
    client = new Client(clientOptions);
    console.log('✅ 客户端实例创建成功');
    console.log('📝 客户端信息:');
    console.log('  - 类型:', client.constructor.name);
    console.log('  - 状态: 未初始化');
} catch (error) {
    console.error('❌ 客户端创建失败:', error);
}

// 测试4: 事件监听器设置
console.log('\n📋 测试4: 事件监听器设置');
if (client) {
    try {
        // 准备就绪事件
        client.on('ready', () => {
            console.log('✅ 客户端准备就绪');
            console.log('📱 客户端信息:', {
                info: client.info,
                wid: client.info?.wid
            });
        });

        // QR码事件
        client.on('qr', (qr) => {
            console.log('📱 收到QR码');
            console.log('📝 QR码数据长度:', qr.length);
            
            // 生成QR码图片
            QRCode.toFile(path.join(TEST_DIR, 'qr-code.png'), qr, (err) => {
                if (err) {
                    console.error('❌ QR码图片生成失败:', err);
                } else {
                    console.log('✅ QR码图片已保存到:', path.join(TEST_DIR, 'qr-code.png'));
                }
            });
        });

        // 认证事件
        client.on('authenticated', () => {
            console.log('✅ 客户端已认证');
        });

        // 认证失败事件
        client.on('auth_failure', (msg) => {
            console.log('❌ 认证失败:', msg);
        });

        // 断开连接事件
        client.on('disconnected', (reason) => {
            console.log('🔌 客户端断开连接:', reason);
        });

        // 消息事件
        client.on('message', (msg) => {
            console.log('📨 收到消息:', {
                from: msg.from,
                body: msg.body?.substring(0, 50) + '...',
                timestamp: msg.timestamp
            });
        });

        console.log('✅ 所有事件监听器设置成功');
        console.log('📝 监听的事件:');
        console.log('  - ready: 客户端准备就绪');
        console.log('  - qr: 收到QR码');
        console.log('  - authenticated: 认证成功');
        console.log('  - auth_failure: 认证失败');
        console.log('  - disconnected: 断开连接');
        console.log('  - message: 收到消息');

    } catch (error) {
        console.error('❌ 事件监听器设置失败:', error);
    }
}

// 测试5: 客户端初始化（不实际启动）
console.log('\n📋 测试5: 客户端初始化检查');
if (client) {
    console.log('✅ 客户端可以初始化');
    console.log('⚠️  注意: 这里只是检查，不实际启动客户端');
    console.log('📝 要实际测试，请运行: npm run test:client');
}

console.log('\n🎉 基础测试完成！');
console.log('\n📁 测试数据目录:', TEST_DIR);
console.log('📋 下一步测试:');
console.log('  - npm run test:client  # 测试客户端创建和初始化');
console.log('  - npm run test:qr      # 测试QR码生成');
console.log('  - npm run test:auth    # 测试认证流程'); 