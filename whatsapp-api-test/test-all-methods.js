import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 主测试函数
async function runAllMethodsTest() {
    // 创建测试目录
    const TEST_DIR = './test-data';
    if (!fs.existsSync(TEST_DIR)) {
        fs.mkdirSync(TEST_DIR, { recursive: true });
    }

    console.log('🚀 开始WhatsApp所有方法测试...\n');

    // 测试配置 - 使用已有的认证数据
    const SESSION_ID = 'reconnect-test-session';
    const AUTH_PATH = path.join(TEST_DIR, 'auth', SESSION_ID);

    console.log('📋 测试配置:');
    console.log('  - Session ID:', SESSION_ID);
    console.log('  - Auth Path:', AUTH_PATH);

    // 创建客户端配置
    const clientOptions = {
        authStrategy: new LocalAuth({
            clientId: SESSION_ID,
            dataPath: path.join(TEST_DIR, 'auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps'
            ]
        }
    };

    console.log('\n📋 测试1: 客户端创建和初始化');
    let client;
    try {
        client = new Client(clientOptions);
        console.log('✅ 客户端创建成功');
    } catch (error) {
        console.error('❌ 客户端创建失败:', error);
        process.exit(1);
    }

    // 设置事件监听器
    console.log('\n📋 测试2: 事件监听器设置');

    let qrReceived = false;
    let authenticated = false;
    let ready = false;
    let error = null;
    let clientInfo = null;

    client.on('qr', (qr) => {
        console.log('📱 收到QR码');
        console.log('📝 QR码数据长度:', qr.length);
        qrReceived = true;
        
        // 生成QR码图片
        const qrPath = path.join(TEST_DIR, `${SESSION_ID}-qr.png`);
        QRCode.toFile(qrPath, qr, (err) => {
            if (err) {
                console.error('❌ QR码图片生成失败:', err);
            } else {
                console.log('✅ QR码图片已保存到:', qrPath);
            }
        });
        
        // 在控制台显示QR码
        QRCode.toString(qr, { type: 'terminal', small: true }, (err, string) => {
            if (!err) {
                console.log('\n📱 终端QR码:');
                console.log(string);
            }
        });
    });

    client.on('authenticated', () => {
        console.log('✅ 客户端已认证');
        authenticated = true;
    });

    client.on('auth_failure', (msg) => {
        console.log('❌ 认证失败:', msg);
        error = msg;
    });

    client.on('ready', () => {
        console.log('✅ 客户端准备就绪');
        ready = true;
        
        // 获取客户端信息
        clientInfo = {
            wid: client.info?.wid,
            platform: client.info?.platform,
            pushname: client.info?.pushname,
            businessName: client.info?.businessName,
            businessHours: client.info?.businessHours,
            description: client.info?.description,
            email: client.info?.email,
            website: client.info?.website,
            latitude: client.info?.latitude,
            longitude: client.info?.longitude,
            address: client.info?.address,
            category: client.info?.category,
            subcategory: client.info?.subcategory,
            isBusiness: client.info?.isBusiness,
            isEnterprise: client.info?.isEnterprise,
            isHighLevelVerified: client.info?.isHighLevelVerified,
            isMe: client.info?.isMe,
            isMyContact: client.info?.isMyContact,
            isPSA: client.info?.isPSA,
            isUser: client.info?.isUser,
            isVerified: client.info?.isVerified,
            isWAContact: client.info?.isWAContact,
            labels: client.info?.labels,
            status: client.info?.status,
            statusMute: client.info?.statusMute,
            statusTimestamp: client.info?.statusTimestamp,
            unreadCount: client.info?.unreadCount
        };
        
        console.log('📱 客户端详细信息:');
        console.log(JSON.stringify(clientInfo, null, 2));
        
        // 保存客户端信息到文件
        const infoPath = path.join(TEST_DIR, `${SESSION_ID}-info.json`);
        fs.writeFileSync(infoPath, JSON.stringify(clientInfo, null, 2));
        console.log('✅ 客户端信息已保存到:', infoPath);
    });

    client.on('disconnected', (reason) => {
        console.log('🔌 客户端断开连接:', reason);
    });

    console.log('✅ 事件监听器设置完成');

    // 启动客户端
    console.log('\n📋 测试3: 客户端初始化');
    console.log('⚠️  注意: 这将启动Puppeteer浏览器');
    console.log('📱 请扫描终端中显示的QR码完成登录');

    try {
        await client.initialize();
        console.log('✅ 客户端初始化成功');
    } catch (error) {
        console.error('❌ 客户端初始化失败:', error);
        process.exit(1);
    }

    // 等待认证完成
    console.log('\n📋 测试4: 等待认证完成');
    console.log('⏳ 等待60秒完成认证...');

    let timeElapsed = 0;
    const interval = setInterval(async () => {
        timeElapsed += 10;
        console.log(`⏱️  已等待 ${timeElapsed} 秒...`);
        
        if (qrReceived) {
            console.log('📱 QR码已收到');
        }
        
        if (authenticated) {
            console.log('✅ 已认证');
        }
        
        if (ready) {
            console.log('✅ 客户端已就绪');
            console.log('📱 客户端信息已获取');
        }
        
        if (error) {
            console.log('❌ 发生错误:', error);
        }
        
        if (timeElapsed >= 60 || ready) {
            clearInterval(interval);
            console.log('\n📋 认证测试完成');
            console.log('📊 最终状态:');
            console.log('  - QR码收到:', qrReceived);
            console.log('  - 已认证:', authenticated);
            console.log('  - 已就绪:', ready);
            console.log('  - 错误:', error || '无');
            
            if (ready && clientInfo) {
                console.log('\n📋 测试5: 所有客户端方法测试');
                
                // 测试所有可用的方法
                await testAllMethods(client, SESSION_ID, TEST_DIR);
            }
            
            // 断开客户端
            console.log('\n🔌 断开客户端...');
            client.destroy().then(() => {
                console.log('✅ 客户端已断开');
                process.exit(0);
            }).catch((err) => {
                console.error('❌ 断开客户端失败:', err);
                process.exit(0);
            });
        }
    }, 10000);

    // 处理进程退出
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        clearInterval(interval);
        if (client) {
            try {
                await client.destroy();
            } catch (err) {
                console.error('❌ 断开客户端失败:', err);
            }
        }
        process.exit(0);
    });
}

// 测试所有客户端方法
async function testAllMethods(client, sessionId, testDir) {
    const results = {};
    
    try {
        // 1. 获取Web版本
        console.log('\n📱 测试1: getWWebVersion');
        try {
            const version = await client.getWWebVersion();
            results.getWWebVersion = { success: true, data: version };
            console.log('  ✅ Web版本:', version);
        } catch (error) {
            results.getWWebVersion = { success: false, error: error.message };
            console.log('  ❌ 获取Web版本失败:', error.message);
        }

        // 2. 获取状态
        console.log('\n📱 测试2: getState');
        try {
            const state = await client.getState();
            results.getState = { success: true, data: state };
            console.log('  ✅ 客户端状态:', state);
        } catch (error) {
            results.getState = { success: false, error: error.message };
            console.log('  ❌ 获取状态失败:', error.message);
        }

        // 3. 获取联系人
        console.log('\n📱 测试3: getContacts');
        try {
            const contacts = await client.getContacts();
            results.getContacts = { success: true, count: contacts.length };
            console.log('  ✅ 联系人总数:', contacts.length);
            
            // 显示前3个联系人
            contacts.slice(0, 3).forEach((contact, index) => {
                console.log(`    ${index + 1}. ${contact.pushname || contact.number} (${contact.number})`);
            });
        } catch (error) {
            results.getContacts = { success: false, error: error.message };
            console.log('  ❌ 获取联系人失败:', error.message);
        }

        // 4. 获取聊天列表
        console.log('\n📱 测试4: getChats');
        try {
            const chats = await client.getChats();
            results.getChats = { success: true, count: chats.length };
            console.log('  ✅ 聊天总数:', chats.length);
            
            // 显示前3个聊天
            chats.slice(0, 3).forEach((chat, index) => {
                console.log(`    ${index + 1}. ${chat.name} (${chat.id._serialized})`);
            });
        } catch (error) {
            results.getChats = { success: false, error: error.message };
            console.log('  ❌ 获取聊天失败:', error.message);
        }

        // 5. 获取标签
        console.log('\n📱 测试5: getLabels');
        try {
            const labels = await client.getLabels();
            results.getLabels = { success: true, count: labels.length };
            console.log('  ✅ 标签总数:', labels.length);
            
            // 显示所有标签
            labels.forEach((label, index) => {
                console.log(`    ${index + 1}. ${label.name} (${label.id})`);
            });
        } catch (error) {
            results.getLabels = { success: false, error: error.message };
            console.log('  ❌ 获取标签失败:', error.message);
        }

        // 6. 获取广播列表
        console.log('\n📱 测试6: getBroadcasts');
        try {
            const broadcasts = await client.getBroadcasts();
            results.getBroadcasts = { success: true, count: broadcasts.length };
            console.log('  ✅ 广播总数:', broadcasts.length);
            
            // 显示所有广播
            broadcasts.forEach((broadcast, index) => {
                console.log(`    ${index + 1}. ${broadcast.name} (${broadcast.id._serialized})`);
            });
        } catch (error) {
            results.getBroadcasts = { success: false, error: error.message };
            console.log('  ❌ 获取广播失败:', error.message);
        }

        // 7. 获取被阻止的联系人
        console.log('\n📱 测试7: getBlockedContacts');
        try {
            const blockedContacts = await client.getBlockedContacts();
            results.getBlockedContacts = { success: true, count: blockedContacts.length };
            console.log('  ✅ 被阻止联系人总数:', blockedContacts.length);
            
            // 显示所有被阻止的联系人
            blockedContacts.forEach((contact, index) => {
                console.log(`    ${index + 1}. ${contact.pushname || contact.number} (${contact.number})`);
            });
        } catch (error) {
            results.getBlockedContacts = { success: false, error: error.message };
            console.log('  ❌ 获取被阻止联系人失败:', error.message);
        }

        // 8. 获取自己的头像
        console.log('\n📱 测试8: getProfilePicUrl (自己)');
        try {
            const myWid = client.info.wid._serialized;
            const myProfilePicUrl = await client.getProfilePicUrl(myWid);
            results.getProfilePicUrl = { success: true, data: myProfilePicUrl };
            console.log('  ✅ 自己的头像URL:', myProfilePicUrl);
        } catch (error) {
            results.getProfilePicUrl = { success: false, error: error.message };
            console.log('  ❌ 获取自己的头像失败:', error.message);
        }

        // 9. 获取联系人设备数量
        console.log('\n📱 测试9: getContactDeviceCount');
        try {
            const myWid = client.info.wid._serialized;
            const deviceCount = await client.getContactDeviceCount(myWid);
            results.getContactDeviceCount = { success: true, data: deviceCount };
            console.log('  ✅ 自己的设备数量:', deviceCount);
        } catch (error) {
            results.getContactDeviceCount = { success: false, error: error.message };
            console.log('  ❌ 获取设备数量失败:', error.message);
        }

        // 10. 获取国家代码
        console.log('\n📱 测试10: getCountryCode');
        try {
            const countryCode = await client.getCountryCode();
            results.getCountryCode = { success: true, data: countryCode };
            console.log('  ✅ 国家代码:', countryCode);
        } catch (error) {
            results.getCountryCode = { success: false, error: error.message };
            console.log('  ❌ 获取国家代码失败:', error.message);
        }

        // 11. 发送测试消息（安全内容）
        console.log('\n📱 测试11: 发送测试消息');
        try {
            const testPhoneNumber = '<EMAIL>';
            const testMessage = 'Hello! This is a test message from WhatsApp API testing. Time: ' + new Date().toISOString();
            
            console.log('  📤 发送消息到:', testPhoneNumber);
            console.log('  📝 消息内容:', testMessage);
            
            const message = await client.sendMessage(testPhoneNumber, testMessage);
            results.sendMessage = { success: true, messageId: message.id._serialized };
            console.log('  ✅ 消息发送成功，ID:', message.id._serialized);
        } catch (error) {
            results.sendMessage = { success: false, error: error.message };
            console.log('  ❌ 发送消息失败:', error.message);
        }

        // 12. 获取特定聊天
        console.log('\n📱 测试12: getChatById');
        try {
            const testPhoneNumber = '<EMAIL>';
            const chat = await client.getChatById(testPhoneNumber);
            results.getChatById = { success: true, data: { id: chat.id._serialized, name: chat.name } };
            console.log('  ✅ 获取聊天成功:', chat.name);
        } catch (error) {
            results.getChatById = { success: false, error: error.message };
            console.log('  ❌ 获取聊天失败:', error.message);
        }

        // 13. 获取消息
        console.log('\n📱 测试13: getMessages');
        try {
            const testPhoneNumber = '<EMAIL>';
            const messages = await client.getMessages(testPhoneNumber, { limit: 5 });
            results.getMessages = { success: true, count: messages.length };
            console.log('  ✅ 获取消息成功，数量:', messages.length);
            
            // 显示前3条消息
            messages.slice(0, 3).forEach((msg, index) => {
                console.log(`    ${index + 1}. ${msg.body?.substring(0, 50) || '无内容'}...`);
            });
        } catch (error) {
            results.getMessages = { success: false, error: error.message };
            console.log('  ❌ 获取消息失败:', error.message);
        }

        // 14. 搜索消息
        console.log('\n📱 测试14: searchMessages');
        try {
            const searchResults = await client.searchMessages('test', { limit: 5 });
            results.searchMessages = { success: true, count: searchResults.length };
            console.log('  ✅ 搜索消息成功，结果数量:', searchResults.length);
        } catch (error) {
            results.searchMessages = { success: false, error: error.message };
            console.log('  ❌ 搜索消息失败:', error.message);
        }

        // 15. 获取群组成员请求
        console.log('\n📱 测试15: getGroupMembershipRequests');
        try {
            const requests = await client.getGroupMembershipRequests();
            results.getGroupMembershipRequests = { success: true, count: requests.length };
            console.log('  ✅ 群组成员请求数量:', requests.length);
        } catch (error) {
            results.getGroupMembershipRequests = { success: false, error: error.message };
            console.log('  ❌ 获取群组成员请求失败:', error.message);
        }

        // 保存测试结果
        const resultsPath = path.join(testDir, `${sessionId}-method-results.json`);
        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
        console.log('\n✅ 所有方法测试结果已保存到:', resultsPath);

        // 显示测试总结
        console.log('\n📊 测试总结:');
        const totalTests = Object.keys(results).length;
        const successfulTests = Object.values(results).filter(r => r.success).length;
        const failedTests = totalTests - successfulTests;
        
        console.log(`  - 总测试数: ${totalTests}`);
        console.log(`  - 成功: ${successfulTests}`);
        console.log(`  - 失败: ${failedTests}`);
        console.log(`  - 成功率: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 运行测试
runAllMethodsTest().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
}); 