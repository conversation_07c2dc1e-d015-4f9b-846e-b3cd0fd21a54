# WhatsApp API 测试项目

这个项目用于逐步测试 WhatsApp-web.js 的每个功能步骤，为后续数据库设计做准备。

## 🚀 项目结构

```
whatsapp-api-test/
├── test-basic.js           # 基础功能测试
├── test-client-creation.js # 客户端创建测试
├── test-authentication.js  # 认证流程测试
├── test-message-sending.js # 消息发送测试 (待创建)
├── test-contacts.js        # 联系人测试 (待创建)
├── test-chats.js          # 聊天记录测试 (待创建)
├── test-data/             # 测试数据目录
│   ├── auth/              # 认证数据
│   ├── *.png              # QR码图片
│   └── *.json             # 客户端信息
└── package.json
```

## 📋 测试脚本

### 1. 基础测试 (`npm test`)
- ✅ 模块导入检查
- ✅ 客户端配置选项
- ✅ 客户端实例创建
- ✅ 事件监听器设置
- ✅ 客户端初始化检查

### 2. 客户端创建测试 (`npm run test:client`)
- ✅ 客户端创建和配置
- ✅ QR码生成和显示
- ✅ 事件监听器设置
- ✅ 状态监控
- ✅ 自动断开连接

### 3. 认证流程测试 (`npm run test:auth`)
- ✅ 完整的认证流程
- ✅ 客户端信息获取
- ✅ 详细信息分析
- ✅ 数据保存到文件

### 4. 待创建的测试
- 📝 消息发送测试 (`npm run test:messages`)
- 📝 联系人测试 (`npm run test:contacts`)
- 📝 聊天记录测试 (`npm run test:chats`)

## 🔧 使用方法

### 安装依赖
```bash
npm install
```

### 运行基础测试
```bash
npm test
```

### 运行客户端创建测试
```bash
npm run test:client
```

### 运行认证流程测试
```bash
npm run test:auth
```

## 📊 测试结果

### 基础测试结果
- ✅ whatsapp-web.js 导入成功
- ✅ qrcode 导入成功
- ✅ 客户端配置选项创建成功
- ✅ 客户端实例创建成功
- ✅ 事件监听器设置成功

### 客户端创建测试结果
- ✅ 客户端创建成功
- ✅ QR码生成成功
- ✅ 事件监听器设置完成
- ✅ 客户端初始化成功
- ✅ 状态监控正常

### 认证流程测试结果
- ✅ 完整的认证流程测试
- ✅ 客户端信息获取
- ✅ 详细信息分析
- ✅ 数据保存功能

## 📱 WhatsApp-web.js API 分析

### 客户端信息字段
```javascript
{
  wid: "用户ID",
  platform: "平台信息",
  pushname: "显示名称",
  businessName: "业务名称",
  businessHours: "营业时间",
  description: "描述",
  email: "邮箱",
  website: "网站",
  latitude: "纬度",
  longitude: "经度",
  address: "地址",
  category: "分类",
  subcategory: "子分类",
  isBusiness: "是否为企业",
  isEnterprise: "是否为企业版",
  isHighLevelVerified: "是否高级验证",
  isMe: "是否为自己",
  isMyContact: "是否为我的联系人",
  isPSA: "是否为PSA",
  isUser: "是否为用户",
  isVerified: "是否已验证",
  isWAContact: "是否为WA联系人",
  labels: "标签",
  status: "状态",
  statusMute: "状态静音",
  statusTimestamp: "状态时间戳",
  unreadCount: "未读消息数"
}
```

### 消息对象字段
```javascript
{
  from: "发送者",
  body: "消息内容",
  timestamp: "时间戳",
  type: "消息类型",
  hasMedia: "是否有媒体",
  isStatus: "是否为状态消息",
  isForwarded: "是否为转发消息",
  broadcast: "是否为广播消息"
}
```

## 🎯 测试结果总结

### 成功测试的方法 (12/15, 80%成功率)

✅ **基础功能**
- `getWWebVersion()` - 获取Web版本: 2.3000.1025257277
- `getState()` - 获取客户端状态: CONNECTED
- `getContacts()` - 获取联系人: 5个联系人
- `getChats()` - 获取聊天列表: 2个聊天
- `getLabels()` - 获取标签: 0个标签
- `getBroadcasts()` - 获取广播: 0个广播
- `getBlockedContacts()` - 获取被阻止联系人: 0个

✅ **头像和设备信息**
- `getProfilePicUrl()` - 获取头像URL成功
- `getContactDeviceCount()` - 获取设备数量: 2个设备

✅ **消息功能**
- `sendMessage()` - 消息发送成功
- `getChatById()` - 获取特定聊天成功
- `searchMessages()` - 搜索消息成功: 2个结果

### 失败的方法 (3/15)

❌ **需要修复的方法**
- `getCountryCode()` - 错误: Cannot read properties of undefined (reading 'replace')
- `getMessages()` - 错误: client.getMessages is not a function
- `getGroupMembershipRequests()` - 错误: Evaluation failed: b

### 离线重连功能

✅ **重连测试成功**
- 使用已有认证数据直接重连
- 无需重新扫码登录
- 所有基本功能正常工作
- 消息发送功能正常

## 🎯 下一步计划

1. **修复失败的方法** - 解决getCountryCode、getMessages等方法的问题
2. **媒体处理测试** - 测试图片、音频、视频等媒体文件
3. **群组管理测试** - 测试群组创建、管理等功能
4. **状态更新测试** - 测试状态消息和动态更新
5. **集成到主项目** - 将测试结果集成到whatsapp-service项目中

## 📝 注意事项

- 测试过程中需要扫描QR码进行WhatsApp登录
- 每个测试都会创建独立的会话ID
- 测试数据保存在 `test-data/` 目录中
- 使用 `Ctrl+C` 可以中断测试并清理资源
- 所有测试都使用无头浏览器模式

## 🔍 数据库设计参考

基于测试结果，建议的数据库字段设计：

### WhatsApp账号表
```sql
CREATE TABLE whatsapp_accounts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  session_id VARCHAR(255) UNIQUE NOT NULL,
  wid VARCHAR(255) NOT NULL,
  platform VARCHAR(100),
  pushname VARCHAR(255),
  business_name VARCHAR(255),
  description TEXT,
  email VARCHAR(255),
  website VARCHAR(255),
  address TEXT,
  category VARCHAR(100),
  subcategory VARCHAR(100),
  is_business BOOLEAN DEFAULT FALSE,
  is_enterprise BOOLEAN DEFAULT FALSE,
  is_verified BOOLEAN DEFAULT FALSE,
  status VARCHAR(100),
  unread_count INT DEFAULT 0,
  login_time TIMESTAMP,
  ban_time TIMESTAMP NULL,
  ban_reason TEXT,
  profile_pic_url VARCHAR(500),
  profile_pic_updated_at TIMESTAMP,
  device_count INT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### WhatsApp联系人表
```sql
CREATE TABLE whatsapp_contacts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  account_id BIGINT NOT NULL,
  contact_id VARCHAR(255) NOT NULL,
  phone_number VARCHAR(50),
  pushname VARCHAR(255),
  profile_pic_url VARCHAR(500),
  profile_pic_updated_at TIMESTAMP,
  is_me BOOLEAN DEFAULT FALSE,
  is_my_contact BOOLEAN DEFAULT FALSE,
  is_user BOOLEAN DEFAULT FALSE,
  is_wa_contact BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (account_id) REFERENCES whatsapp_accounts(id),
  UNIQUE KEY unique_contact (account_id, contact_id)
);
```

### 消息表
```sql
CREATE TABLE whatsapp_messages (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  account_id BIGINT NOT NULL,
  message_id VARCHAR(255) UNIQUE NOT NULL,
  from_number VARCHAR(255) NOT NULL,
  to_number VARCHAR(255) NOT NULL,
  body TEXT,
  message_type VARCHAR(50),
  has_media BOOLEAN DEFAULT FALSE,
  media_url VARCHAR(500),
  is_status BOOLEAN DEFAULT FALSE,
  is_forwarded BOOLEAN DEFAULT FALSE,
  broadcast BOOLEAN DEFAULT FALSE,
  timestamp BIGINT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (account_id) REFERENCES whatsapp_accounts(id)
);
``` 