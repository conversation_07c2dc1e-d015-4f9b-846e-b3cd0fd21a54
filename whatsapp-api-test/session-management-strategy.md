# WhatsApp Session 管理策略设计

## 🎯 核心目标

1. **一对一映射**: 一个WA号只对应一个Session
2. **自动清理**: 清理无效的Session文件
3. **级联删除**: 删除账号时自动删除对应文件夹
4. **空间优化**: 避免数据占用过多空间

## 🏗️ 数据库设计

### 1. WhatsApp账号表 (增强版)
```sql
CREATE TABLE whatsapp_accounts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  session_id VARCHAR(255) UNIQUE NOT NULL,
  wid VARCHAR(255) UNIQUE NOT NULL, -- 确保WA号唯一性
  phone_number VARCHAR(50) UNIQUE NOT NULL, -- 确保手机号唯一性
  platform VARCHAR(100),
  pushname VARCHAR(255),
  business_name VARCHAR(255),
  description TEXT,
  email VARCHAR(255),
  website VARCHAR(255),
  address TEXT,
  category VARCHAR(100),
  subcategory VARCHAR(100),
  is_business BOOLEAN DEFAULT FALSE,
  is_enterprise BOOLEAN DEFAULT FALSE,
  is_verified BOOLEAN DEFAULT FALSE,
  status ENUM('disconnected', 'connecting', 'connected', 'error', 'banned', 'offline') DEFAULT 'disconnected',
  unread_count INT DEFAULT 0,
  login_time TIMESTAMP NULL,
  ban_time TIMESTAMP NULL,
  ban_reason TEXT,
  profile_pic_url VARCHAR(500),
  profile_pic_updated_at TIMESTAMP NULL,
  device_count INT DEFAULT 1,
  session_storage_path VARCHAR(500),
  session_storage_type ENUM('local', 's3', 'oss') DEFAULT 'local',
  session_created_at TIMESTAMP NULL,
  session_last_used TIMESTAMP NULL,
  session_file_size BIGINT DEFAULT 0, -- Session文件大小(字节)
  session_file_count INT DEFAULT 0, -- Session文件数量
  is_session_valid BOOLEAN DEFAULT FALSE, -- Session是否有效
  last_activity TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL, -- 软删除
  FOREIGN KEY (tenant_id) REFERENCES tenants(id),
  INDEX idx_tenant_status (tenant_id, status),
  INDEX idx_session_id (session_id),
  INDEX idx_wid (wid),
  INDEX idx_phone_number (phone_number),
  INDEX idx_session_valid (is_session_valid),
  INDEX idx_last_activity (last_activity)
);
```

### 2. Session清理日志表
```sql
CREATE TABLE session_cleanup_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  account_id BIGINT NOT NULL,
  session_id VARCHAR(255) NOT NULL,
  cleanup_type ENUM('invalid_session', 'orphaned_session', 'account_deleted', 'size_limit') NOT NULL,
  file_size_freed BIGINT DEFAULT 0, -- 释放的空间(字节)
  file_count_freed INT DEFAULT 0, -- 释放的文件数量
  cleanup_reason TEXT,
  cleaned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (account_id) REFERENCES whatsapp_accounts(id)
);
```

## 🔧 Session管理服务

### 1. SessionManager.ts
```typescript
import { Client, LocalAuth } from 'whatsapp-web.js';
import fs from 'fs';
import path from 'path';
import { EventEmitter } from 'events';

interface SessionInfo {
  sessionId: string;
  accountId: number;
  wid: string;
  phoneNumber: string;
  status: string;
  fileSize: number;
  fileCount: number;
  lastUsed: Date;
  isValid: boolean;
}

export class SessionManager extends EventEmitter {
  private sessions: Map<string, Client> = new Map();
  private sessionInfo: Map<string, SessionInfo> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(
    private db: Database,
    private storagePath: string = '/var/whatsapp-sessions',
    private maxSessionAge: number = 7 * 24 * 60 * 60 * 1000, // 7天
    private maxSessionSize: number = 100 * 1024 * 1024 // 100MB
  ) {
    super();
    this.startCleanupScheduler();
  }

  // 创建新Session (确保唯一性)
  async createSession(accountId: number, phoneNumber: string): Promise<string> {
    // 检查是否已存在该WA号的Session
    const existingAccount = await this.db.query(
      'SELECT session_id FROM whatsapp_accounts WHERE phone_number = ? AND deleted_at IS NULL',
      [phoneNumber]
    );

    if (existingAccount.length > 0) {
      throw new Error(`WA号 ${phoneNumber} 已存在Session: ${existingAccount[0].session_id}`);
    }

    // 生成唯一Session ID
    const sessionId = `session-${accountId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建Session目录
    const sessionPath = path.join(this.storagePath, sessionId);
    await fs.promises.mkdir(sessionPath, { recursive: true });

    // 更新数据库
    await this.db.query(
      'UPDATE whatsapp_accounts SET session_id = ?, session_storage_path = ?, session_created_at = NOW() WHERE id = ?',
      [sessionId, sessionPath, accountId]
    );

    return sessionId;
  }

  // 重连Session (验证有效性)
  async reconnectSession(sessionId: string): Promise<boolean> {
    try {
      // 检查Session是否存在且有效
      const account = await this.db.query(
        'SELECT * FROM whatsapp_accounts WHERE session_id = ? AND deleted_at IS NULL',
        [sessionId]
      );

      if (account.length === 0) {
        await this.cleanupSession(sessionId, 'account_deleted');
        return false;
      }

      const sessionPath = account[0].session_storage_path;
      if (!await this.isSessionValid(sessionPath)) {
        await this.cleanupSession(sessionId, 'invalid_session');
        return false;
      }

      // 创建客户端
      const client = new Client({
        authStrategy: new LocalAuth({
          clientId: sessionId,
          dataPath: this.storagePath
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu'
          ]
        }
      });

      // 设置事件监听器
      this.setupClientEvents(client, sessionId);

      // 初始化客户端
      await client.initialize();

      this.sessions.set(sessionId, client);
      await this.updateSessionInfo(sessionId, { isValid: true, lastUsed: new Date() });

      return true;
    } catch (error) {
      console.error(`Failed to reconnect session ${sessionId}:`, error);
      await this.cleanupSession(sessionId, 'invalid_session');
      return false;
    }
  }

  // 验证Session有效性
  private async isSessionValid(sessionPath: string): Promise<boolean> {
    try {
      if (!fs.existsSync(sessionPath)) {
        return false;
      }

      // 检查关键文件是否存在
      const requiredFiles = [
        'Default/Cookies',
        'Default/Local Storage/leveldb/CURRENT',
        'Default/Local Storage/leveldb/000003.log'
      ];

      for (const file of requiredFiles) {
        if (!fs.existsSync(path.join(sessionPath, file))) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  // 更新Session信息
  private async updateSessionInfo(sessionId: string, updates: Partial<SessionInfo>) {
    const sessionPath = path.join(this.storagePath, sessionId);
    
    if (fs.existsSync(sessionPath)) {
      const stats = await this.getSessionStats(sessionPath);
      updates.fileSize = stats.size;
      updates.fileCount = stats.count;
    }

    await this.db.query(
      'UPDATE whatsapp_accounts SET session_last_used = NOW(), session_file_size = ?, session_file_count = ?, is_session_valid = ?, updated_at = NOW() WHERE session_id = ?',
      [updates.fileSize || 0, updates.fileCount || 0, updates.isValid || false, sessionId]
    );
  }

  // 获取Session统计信息
  private async getSessionStats(sessionPath: string): Promise<{ size: number; count: number }> {
    let totalSize = 0;
    let fileCount = 0;

    const walkDir = async (dir: string) => {
      const files = await fs.promises.readdir(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.promises.stat(filePath);
        if (stat.isDirectory()) {
          await walkDir(filePath);
        } else {
          totalSize += stat.size;
          fileCount++;
        }
      }
    };

    await walkDir(sessionPath);
    return { size: totalSize, fileCount };
  }

  // 清理Session
  async cleanupSession(sessionId: string, reason: string): Promise<void> {
    try {
      // 断开客户端连接
      const client = this.sessions.get(sessionId);
      if (client) {
        await client.destroy();
        this.sessions.delete(sessionId);
      }

      // 获取Session信息
      const account = await this.db.query(
        'SELECT * FROM whatsapp_accounts WHERE session_id = ?',
        [sessionId]
      );

      if (account.length > 0) {
        const sessionPath = account[0].session_storage_path;
        let fileSizeFreed = 0;
        let fileCountFreed = 0;

        // 删除Session文件
        if (fs.existsSync(sessionPath)) {
          const stats = await this.getSessionStats(sessionPath);
          fileSizeFreed = stats.size;
          fileCountFreed = stats.count;
          
          await fs.promises.rm(sessionPath, { recursive: true, force: true });
        }

        // 记录清理日志
        await this.db.query(
          'INSERT INTO session_cleanup_logs (account_id, session_id, cleanup_type, file_size_freed, file_count_freed, cleanup_reason) VALUES (?, ?, ?, ?, ?, ?)',
          [account[0].id, sessionId, reason, fileSizeFreed, fileCountFreed, reason]
        );

        // 更新账号状态
        await this.db.query(
          'UPDATE whatsapp_accounts SET session_id = NULL, session_storage_path = NULL, is_session_valid = FALSE, status = "disconnected" WHERE session_id = ?',
          [sessionId]
        );
      }
    } catch (error) {
      console.error(`Failed to cleanup session ${sessionId}:`, error);
    }
  }

  // 删除账号时清理Session
  async deleteAccount(accountId: number): Promise<void> {
    const account = await this.db.query(
      'SELECT session_id FROM whatsapp_accounts WHERE id = ?',
      [accountId]
    );

    if (account.length > 0 && account[0].session_id) {
      await this.cleanupSession(account[0].session_id, 'account_deleted');
    }

    // 软删除账号
    await this.db.query(
      'UPDATE whatsapp_accounts SET deleted_at = NOW() WHERE id = ?',
      [accountId]
    );
  }

  // 启动清理调度器
  private startCleanupScheduler(): void {
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanup();
    }, 60 * 60 * 1000); // 每小时执行一次
  }

  // 执行清理任务
  private async performCleanup(): Promise<void> {
    console.log('Starting session cleanup...');

    // 1. 清理过期的Session
    const expiredSessions = await this.db.query(
      'SELECT session_id FROM whatsapp_accounts WHERE session_last_used < DATE_SUB(NOW(), INTERVAL ? DAY) AND is_session_valid = TRUE',
      [this.maxSessionAge / (24 * 60 * 60 * 1000)]
    );

    for (const session of expiredSessions) {
      await this.cleanupSession(session.session_id, 'expired_session');
    }

    // 2. 清理过大的Session
    const oversizedSessions = await this.db.query(
      'SELECT session_id FROM whatsapp_accounts WHERE session_file_size > ? AND is_session_valid = TRUE',
      [this.maxSessionSize]
    );

    for (const session of oversizedSessions) {
      await this.cleanupSession(session.session_id, 'size_limit');
    }

    // 3. 清理孤立的Session文件
    await this.cleanupOrphanedSessions();

    console.log('Session cleanup completed');
  }

  // 清理孤立的Session文件
  private async cleanupOrphanedSessions(): Promise<void> {
    try {
      const sessionDirs = await fs.promises.readdir(this.storagePath);
      
      for (const dir of sessionDirs) {
        if (dir.startsWith('session-')) {
          const sessionId = dir;
          const account = await this.db.query(
            'SELECT id FROM whatsapp_accounts WHERE session_id = ? AND deleted_at IS NULL',
            [sessionId]
          );

          if (account.length === 0) {
            // 孤立的Session文件，清理它
            const sessionPath = path.join(this.storagePath, sessionId);
            if (fs.existsSync(sessionPath)) {
              const stats = await this.getSessionStats(sessionPath);
              await fs.promises.rm(sessionPath, { recursive: true, force: true });
              
              // 记录清理日志
              await this.db.query(
                'INSERT INTO session_cleanup_logs (account_id, session_id, cleanup_type, file_size_freed, file_count_freed, cleanup_reason) VALUES (0, ?, ?, ?, ?, ?)',
                [sessionId, 'orphaned_session', stats.size, stats.count, 'Orphaned session file']
              );
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to cleanup orphaned sessions:', error);
    }
  }

  // 获取存储统计
  async getStorageStats(): Promise<{
    totalAccounts: number;
    activeSessions: number;
    totalSize: number;
    totalFiles: number;
    cleanupStats: any[];
  }> {
    const stats = await this.db.query(`
      SELECT 
        COUNT(*) as total_accounts,
        SUM(CASE WHEN is_session_valid = TRUE THEN 1 ELSE 0 END) as active_sessions,
        SUM(session_file_size) as total_size,
        SUM(session_file_count) as total_files
      FROM whatsapp_accounts 
      WHERE deleted_at IS NULL
    `);

    const cleanupStats = await this.db.query(`
      SELECT 
        cleanup_type,
        COUNT(*) as cleanup_count,
        SUM(file_size_freed) as total_size_freed,
        SUM(file_count_freed) as total_files_freed
      FROM session_cleanup_logs 
      WHERE cleaned_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY cleanup_type
    `);

    return {
      totalAccounts: stats[0].total_accounts,
      activeSessions: stats[0].active_sessions,
      totalSize: stats[0].total_size || 0,
      totalFiles: stats[0].total_files || 0,
      cleanupStats
    };
  }

  // 停止服务
  async shutdown(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // 断开所有客户端
    for (const [sessionId, client] of this.sessions) {
      try {
        await client.destroy();
      } catch (error) {
        console.error(`Failed to destroy client ${sessionId}:`, error);
      }
    }

    this.sessions.clear();
  }
}
```

## 🔄 数据库触发器

### 1. 删除账号时自动清理Session
```sql
DELIMITER //
CREATE TRIGGER before_account_delete
BEFORE DELETE ON whatsapp_accounts
FOR EACH ROW
BEGIN
    -- 记录删除的Session信息
    INSERT INTO session_cleanup_logs (
        account_id, 
        session_id, 
        cleanup_type, 
        file_size_freed, 
        file_count_freed, 
        cleanup_reason
    ) VALUES (
        OLD.id,
        OLD.session_id,
        'account_deleted',
        OLD.session_file_size,
        OLD.session_file_count,
        'Account deleted'
    );
END//
DELIMITER ;
```

### 2. 更新Session信息时记录
```sql
DELIMITER //
CREATE TRIGGER after_session_update
AFTER UPDATE ON whatsapp_accounts
FOR EACH ROW
BEGIN
    -- 如果Session状态变为无效，记录清理
    IF OLD.is_session_valid = TRUE AND NEW.is_session_valid = FALSE THEN
        INSERT INTO session_cleanup_logs (
            account_id,
            session_id,
            cleanup_type,
            file_size_freed,
            file_count_freed,
            cleanup_reason
        ) VALUES (
            NEW.id,
            NEW.session_id,
            'invalid_session',
            NEW.session_file_size,
            NEW.session_file_count,
            'Session became invalid'
        );
    END IF;
END//
DELIMITER ;
```

## 📊 监控和告警

### 1. 存储监控脚本
```typescript
// StorageMonitor.ts
export class StorageMonitor {
  async checkStorageHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const stats = await this.sessionManager.getStorageStats();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查存储空间
    const totalSizeGB = stats.totalSize / (1024 * 1024 * 1024);
    if (totalSizeGB > 10) { // 超过10GB
      issues.push(`存储空间过大: ${totalSizeGB.toFixed(2)}GB`);
      recommendations.push('建议清理无效Session或增加存储空间');
    }

    // 检查Session有效性
    const invalidRate = (stats.totalAccounts - stats.activeSessions) / stats.totalAccounts;
    if (invalidRate > 0.3) { // 超过30%的Session无效
      issues.push(`Session有效性低: ${(invalidRate * 100).toFixed(1)}%`);
      recommendations.push('建议检查Session创建和清理机制');
    }

    // 检查清理效率
    const recentCleanup = stats.cleanupStats.find(s => s.cleanup_type === 'invalid_session');
    if (recentCleanup && recentCleanup.cleanup_count > 10) {
      issues.push(`近期清理了${recentCleanup.cleanup_count}个无效Session`);
      recommendations.push('建议优化Session创建和验证机制');
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations
    };
  }
}
```

### 2. 告警配置
```typescript
// AlertManager.ts
export class AlertManager {
  async sendStorageAlert(health: any): Promise<void> {
    if (!health.isHealthy) {
      const message = `
🚨 WhatsApp Session 存储告警

问题:
${health.issues.map(issue => `- ${issue}`).join('\n')}

建议:
${health.recommendations.map(rec => `- ${rec}`).join('\n')}

时间: ${new Date().toISOString()}
      `;

      // 发送告警到钉钉/企业微信等
      await this.sendNotification(message);
    }
  }
}
```

## 🎯 实施步骤

### 阶段1: 基础实现
1. 实现SessionManager核心功能
2. 添加数据库约束和索引
3. 实现基本的清理机制

### 阶段2: 监控和告警
1. 实现StorageMonitor
2. 添加告警机制
3. 实现清理日志

### 阶段3: 优化
1. 实现Session压缩
2. 优化清理策略
3. 添加性能监控

## 📈 预期效果

### 存储优化
- **空间节省**: 自动清理无效Session，节省50-80%存储空间
- **性能提升**: 减少无效Session，提高启动速度
- **成本控制**: 通过清理机制控制存储成本

### 数据一致性
- **一对一映射**: 确保WA号与Session的唯一对应关系
- **级联删除**: 删除账号时自动清理相关数据
- **状态同步**: 实时更新Session状态和统计信息

### 运维便利
- **自动清理**: 无需手动干预，系统自动维护
- **监控告警**: 及时发现和处理问题
- **日志追踪**: 完整的清理记录和审计

这样的设计确保了Session管理的自动化、高效性和可靠性，有效避免了数据占用空间的问题。 