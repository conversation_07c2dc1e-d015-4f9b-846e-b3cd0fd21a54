import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 主测试函数
async function runSimpleMessageTest() {
    // 创建测试目录
    const TEST_DIR = './test-data';
    if (!fs.existsSync(TEST_DIR)) {
        fs.mkdirSync(TEST_DIR, { recursive: true });
    }

    console.log('🚀 开始简单消息发送测试...\n');

    // 测试配置 - 使用之前认证过的session ID
    const SESSION_ID = 'reconnect-test-session';
    const AUTH_PATH = path.join(TEST_DIR, 'auth', SESSION_ID);

    console.log('📋 测试配置:');
    console.log('  - Session ID:', SESSION_ID);
    console.log('  - Auth Path:', AUTH_PATH);

    // 检查是否存在认证数据
    const sessionDataPath = path.join(TEST_DIR, 'auth', `session-${SESSION_ID}`);
    const hasAuthData = fs.existsSync(sessionDataPath);
    
    console.log('📋 认证数据检查:');
    console.log('  - 认证数据存在:', hasAuthData);

    // 创建客户端配置
    const clientOptions = {
        authStrategy: new LocalAuth({
            clientId: SESSION_ID,
            dataPath: path.join(TEST_DIR, 'auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps'
            ]
        }
    };

    console.log('\n📋 测试1: 客户端创建');
    let client;
    try {
        client = new Client(clientOptions);
        console.log('✅ 客户端创建成功');
    } catch (error) {
        console.error('❌ 客户端创建失败:', error);
        process.exit(1);
    }

    // 设置事件监听器
    console.log('\n📋 测试2: 事件监听器设置');

    let qrReceived = false;
    let authenticated = false;
    let ready = false;
    let error = null;

    client.on('qr', (qr) => {
        console.log('📱 收到QR码');
        qrReceived = true;
        
        // 在控制台显示QR码
        QRCode.toString(qr, { type: 'terminal', small: true }, (err, string) => {
            if (!err) {
                console.log('\n📱 终端QR码:');
                console.log(string);
            }
        });
    });

    client.on('authenticated', () => {
        console.log('✅ 客户端已认证');
        authenticated = true;
    });

    client.on('auth_failure', (msg) => {
        console.log('❌ 认证失败:', msg);
        error = msg;
    });

    client.on('ready', () => {
        console.log('✅ 客户端准备就绪');
        ready = true;
        console.log('📱 客户端信息:', client.info?.pushname || 'Unknown');
    });

    client.on('disconnected', (reason) => {
        console.log('🔌 客户端断开连接:', reason);
    });

    console.log('✅ 事件监听器设置完成');

    // 启动客户端
    console.log('\n📋 测试3: 客户端初始化');
    if (hasAuthData) {
        console.log('⚠️  检测到认证数据，尝试离线重连...');
    } else {
        console.log('⚠️  未检测到认证数据，需要扫码登录...');
    }

    try {
        await client.initialize();
        console.log('✅ 客户端初始化成功');
    } catch (error) {
        console.error('❌ 客户端初始化失败:', error);
        process.exit(1);
    }

    // 等待认证完成
    console.log('\n📋 测试4: 等待认证完成');
    console.log('⏳ 等待60秒完成认证...');

    let timeElapsed = 0;
    const interval = setInterval(async () => {
        timeElapsed += 10;
        console.log(`⏱️  已等待 ${timeElapsed} 秒...`);
        
        if (qrReceived) {
            console.log('📱 QR码已收到');
        }
        
        if (authenticated) {
            console.log('✅ 已认证');
        }
        
        if (ready) {
            console.log('✅ 客户端已就绪');
        }
        
        if (error) {
            console.log('❌ 发生错误:', error);
        }
        
        if (timeElapsed >= 60 || ready) {
            clearInterval(interval);
            console.log('\n📋 认证测试完成');
            console.log('📊 最终状态:');
            console.log('  - QR码收到:', qrReceived);
            console.log('  - 已认证:', authenticated);
            console.log('  - 已就绪:', ready);
            console.log('  - 错误:', error || '无');
            
            if (ready) {
                console.log('\n📋 测试5: 发送简单消息');
                
                // 发送简单消息
                await sendSimpleMessage(client);
            }
            
            // 断开客户端
            console.log('\n🔌 断开客户端...');
            client.destroy().then(() => {
                console.log('✅ 客户端已断开');
                process.exit(0);
            }).catch((err) => {
                console.error('❌ 断开客户端失败:', err);
                process.exit(0);
            });
        }
    }, 10000);

    // 处理进程退出
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        clearInterval(interval);
        if (client) {
            try {
                await client.destroy();
            } catch (err) {
                console.error('❌ 断开客户端失败:', err);
            }
        }
        process.exit(0);
    });
}

// 发送简单消息
async function sendSimpleMessage(client) {
    try {
        const testPhoneNumber = '<EMAIL>';
        const testMessage = 'hello';
        
        console.log('📤 发送消息到:', testPhoneNumber);
        console.log('📝 消息内容:', testMessage);
        
        const message = await client.sendMessage(testPhoneNumber, testMessage);
        console.log('✅ 消息发送成功，ID:', message.id._serialized);
        console.log('✅ 消息时间戳:', message.timestamp);
        console.log('✅ 消息状态:', message.status);
        
        // 等待2秒确认消息状态
        console.log('⏳ 等待2秒确认消息状态...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 检查消息是否已发送
        try {
            const chat = await client.getChatById(testPhoneNumber);
            const messages = await chat.fetchMessages({ limit: 1 });
            if (messages.length > 0) {
                const lastMessage = messages[0];
                console.log('📱 最新消息:', lastMessage.body);
                console.log('📱 消息时间:', new Date(lastMessage.timestamp * 1000));
                console.log('📱 消息状态:', lastMessage.status);
            }
        } catch (error) {
            console.log('❌ 获取最新消息失败:', error.message);
        }
        
    } catch (error) {
        console.log('❌ 发送消息失败:', error.message);
        console.log('❌ 错误详情:', error);
    }
}

// 运行测试
runSimpleMessageTest().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
}); 