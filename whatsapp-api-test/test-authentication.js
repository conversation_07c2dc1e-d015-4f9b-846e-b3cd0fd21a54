import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// 主测试函数
async function runAuthTest() {
    // 创建测试目录
    const TEST_DIR = './test-data';
    if (!fs.existsSync(TEST_DIR)) {
        fs.mkdirSync(TEST_DIR, { recursive: true });
    }

    console.log('🚀 开始WhatsApp认证流程测试...\n');

    // 测试配置
    const SESSION_ID = 'auth-test-' + Date.now();
    const AUTH_PATH = path.join(TEST_DIR, 'auth', SESSION_ID);

    console.log('📋 测试配置:');
    console.log('  - Session ID:', SESSION_ID);
    console.log('  - Auth Path:', AUTH_PATH);

    // 创建客户端配置
    const clientOptions = {
        authStrategy: new LocalAuth({
            clientId: SESSION_ID,
            dataPath: path.join(TEST_DIR, 'auth')
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps'
            ]
        }
    };

    console.log('\n📋 测试1: 客户端创建和初始化');
    let client;
    try {
        client = new Client(clientOptions);
        console.log('✅ 客户端创建成功');
    } catch (error) {
        console.error('❌ 客户端创建失败:', error);
        process.exit(1);
    }

    // 设置事件监听器
    console.log('\n📋 测试2: 事件监听器设置');

    let qrReceived = false;
    let authenticated = false;
    let ready = false;
    let error = null;
    let clientInfo = null;

    client.on('qr', (qr) => {
        console.log('📱 收到QR码');
        console.log('📝 QR码数据长度:', qr.length);
        qrReceived = true;
        
        // 生成QR码图片
        const qrPath = path.join(TEST_DIR, `${SESSION_ID}-qr.png`);
        QRCode.toFile(qrPath, qr, (err) => {
            if (err) {
                console.error('❌ QR码图片生成失败:', err);
            } else {
                console.log('✅ QR码图片已保存到:', qrPath);
            }
        });
        
        // 在控制台显示QR码
        QRCode.toString(qr, { type: 'terminal', small: true }, (err, string) => {
            if (!err) {
                console.log('\n📱 终端QR码:');
                console.log(string);
            }
        });
    });

    client.on('authenticated', () => {
        console.log('✅ 客户端已认证');
        authenticated = true;
    });

    client.on('auth_failure', (msg) => {
        console.log('❌ 认证失败:', msg);
        error = msg;
    });

    client.on('ready', () => {
        console.log('✅ 客户端准备就绪');
        ready = true;
        
        // 获取客户端信息
        clientInfo = {
            wid: client.info?.wid,
            platform: client.info?.platform,
            pushname: client.info?.pushname,
            businessName: client.info?.businessName,
            businessHours: client.info?.businessHours,
            description: client.info?.description,
            email: client.info?.email,
            website: client.info?.website,
            latitude: client.info?.latitude,
            longitude: client.info?.longitude,
            address: client.info?.address,
            category: client.info?.category,
            subcategory: client.info?.subcategory,
            isBusiness: client.info?.isBusiness,
            isEnterprise: client.info?.isEnterprise,
            isHighLevelVerified: client.info?.isHighLevelVerified,
            isMe: client.info?.isMe,
            isMyContact: client.info?.isMyContact,
            isPSA: client.info?.isPSA,
            isUser: client.info?.isUser,
            isVerified: client.info?.isVerified,
            isWAContact: client.info?.isWAContact,
            labels: client.info?.labels,
            status: client.info?.status,
            statusMute: client.info?.statusMute,
            statusTimestamp: client.info?.statusTimestamp,
            unreadCount: client.info?.unreadCount
        };
        
        console.log('📱 客户端详细信息:');
        console.log(JSON.stringify(clientInfo, null, 2));
        
        // 保存客户端信息到文件
        const infoPath = path.join(TEST_DIR, `${SESSION_ID}-info.json`);
        fs.writeFileSync(infoPath, JSON.stringify(clientInfo, null, 2));
        console.log('✅ 客户端信息已保存到:', infoPath);
    });

    client.on('disconnected', (reason) => {
        console.log('🔌 客户端断开连接:', reason);
    });

    client.on('message', (msg) => {
        console.log('📨 收到消息:', {
            from: msg.from,
            body: msg.body?.substring(0, 50) + '...',
            timestamp: msg.timestamp,
            type: msg.type,
            hasMedia: msg.hasMedia,
            isStatus: msg.isStatus,
            isForwarded: msg.isForwarded,
            broadcast: msg.broadcast
        });
    });

    console.log('✅ 事件监听器设置完成');

    // 启动客户端
    console.log('\n📋 测试3: 客户端初始化');
    console.log('⚠️  注意: 这将启动Puppeteer浏览器');
    console.log('📱 请扫描终端中显示的QR码完成登录');

    try {
        await client.initialize();
        console.log('✅ 客户端初始化成功');
    } catch (error) {
        console.error('❌ 客户端初始化失败:', error);
        process.exit(1);
    }

    // 等待认证完成
    console.log('\n📋 测试4: 等待认证完成');
    console.log('⏳ 等待60秒完成认证...');

    let timeElapsed = 0;
    const interval = setInterval(() => {
        timeElapsed += 10;
        console.log(`⏱️  已等待 ${timeElapsed} 秒...`);
        
        if (qrReceived) {
            console.log('📱 QR码已收到');
        }
        
        if (authenticated) {
            console.log('✅ 已认证');
        }
        
        if (ready) {
            console.log('✅ 客户端已就绪');
            console.log('📱 客户端信息已获取');
        }
        
        if (error) {
            console.log('❌ 发生错误:', error);
        }
        
        if (timeElapsed >= 60 || ready) {
            clearInterval(interval);
            console.log('\n📋 认证测试完成');
            console.log('📊 最终状态:');
            console.log('  - QR码收到:', qrReceived);
            console.log('  - 已认证:', authenticated);
            console.log('  - 已就绪:', ready);
            console.log('  - 错误:', error || '无');
            
            if (ready && clientInfo) {
                console.log('\n📋 测试5: 客户端信息分析');
                console.log('📱 关键信息:');
                console.log('  - WID:', clientInfo.wid);
                console.log('  - 平台:', clientInfo.platform);
                console.log('  - 显示名称:', clientInfo.pushname);
                console.log('  - 是否为企业:', clientInfo.isEnterprise);
                console.log('  - 是否已验证:', clientInfo.isVerified);
                console.log('  - 业务名称:', clientInfo.businessName);
                console.log('  - 描述:', clientInfo.description);
                console.log('  - 邮箱:', clientInfo.email);
                console.log('  - 网站:', clientInfo.website);
                console.log('  - 地址:', clientInfo.address);
                console.log('  - 分类:', clientInfo.category);
                console.log('  - 子分类:', clientInfo.subcategory);
                console.log('  - 标签:', clientInfo.labels);
                console.log('  - 状态:', clientInfo.status);
                console.log('  - 未读消息数:', clientInfo.unreadCount);
            }
            
            // 断开客户端
            console.log('\n🔌 断开客户端...');
            client.destroy().then(() => {
                console.log('✅ 客户端已断开');
                process.exit(0);
            }).catch((err) => {
                console.error('❌ 断开客户端失败:', err);
                process.exit(0);
            });
        }
    }, 10000);

    // 处理进程退出
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到中断信号，正在清理...');
        clearInterval(interval);
        if (client) {
            try {
                await client.destroy();
            } catch (err) {
                console.error('❌ 断开客户端失败:', err);
            }
        }
        process.exit(0);
    });
}

// 运行测试
runAuthTest().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
}); 