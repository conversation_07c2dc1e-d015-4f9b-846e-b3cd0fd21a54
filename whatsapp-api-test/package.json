{"name": "whatsapp-api-test", "version": "1.0.0", "description": "WhatsApp-web.js API测试项目，用于逐步测试每个功能步骤", "main": "index.js", "type": "module", "scripts": {"test": "node test-basic.js", "test:qr": "node test-qr-generation.js", "test:client": "node test-client-creation.js", "test:auth": "node test-authentication.js", "test:avatar": "node test-avatar.js", "test:all-methods": "node test-all-methods.js", "test:reconnect": "node test-reconnect.js", "test:simple-message": "node test-simple-message.js", "test:messages": "node test-message-sending.js", "test:contacts": "node test-contacts.js", "test:chats": "node test-chats.js", "dev": "nodemon --exec node test-basic.js"}, "keywords": ["whatsapp", "testing", "api"], "author": "", "license": "ISC", "dependencies": {"whatsapp-web.js": "^1.31.0", "qrcode": "^1.5.4", "puppeteer": "^24.15.0"}, "devDependencies": {"nodemon": "^3.1.10"}}