#!/bin/bash

# 测试系统管理员的WhatsApp管理菜单显示逻辑
# 验证系统管理租户隐藏菜单，其他租户显示菜单

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BACKEND_URL="http://localhost:8081"
FRONTEND_URL="http://localhost:5173"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"

echo -e "${BLUE}=== 系统管理员WhatsApp管理菜单显示逻辑测试 ===${NC}"
echo

# 函数：打印测试结果
print_result() {
    local test_name="$1"
    local status="$2"
    local message="$3"
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✓ $test_name: $message${NC}"
    else
        echo -e "${RED}✗ $test_name: $message${NC}"
    fi
}

# 函数：检查服务状态
check_services() {
    echo -e "${YELLOW}1. 检查服务状态...${NC}"
    
    # 检查后端服务
    if curl --noproxy "*" -s "$BACKEND_URL/health" > /dev/null 2>&1; then
        print_result "后端服务" "PASS" "运行正常"
    else
        print_result "后端服务" "FAIL" "无法连接"
        return 1
    fi
    
    # 检查前端服务
    if curl --noproxy "*" -s "$FRONTEND_URL" > /dev/null 2>&1; then
        print_result "前端服务" "PASS" "运行正常"
    else
        print_result "前端服务" "FAIL" "无法连接"
        return 1
    fi
    
    echo
}

# 函数：获取访问令牌
get_access_token() {
    echo -e "${YELLOW}2. 获取访问令牌...${NC}"
    
    local response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$ADMIN_USERNAME\",
            \"password\": \"$ADMIN_PASSWORD\"
        }")
    
    if echo "$response" | grep -q '"token"'; then
        ACCESS_TOKEN=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        print_result "获取令牌" "PASS" "成功获取访问令牌"
        echo "令牌: ${ACCESS_TOKEN:0:20}..."
    else
        print_result "获取令牌" "FAIL" "登录失败"
        echo "响应: $response"
        return 1
    fi
    
    echo
}

# 主测试流程
main() {
    echo -e "${BLUE}开始系统管理员WhatsApp管理菜单显示逻辑测试${NC}"
    echo "=================================================="
    echo
    
    # 执行测试步骤
    check_services || exit 1
    get_access_token || exit 1
    
    echo -e "${GREEN}=== 基础测试完成 ===${NC}"
}

# 函数：测试系统管理租户的菜单显示
test_system_tenant_menu() {
    echo -e "${YELLOW}3. 测试系统管理租户的菜单显示...${NC}"
    
    # 首先切换到系统管理（租户ID为0）
    local switch_response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/tenant/switch" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"tenant_id\": 0,
            \"reason\": \"测试系统管理模式\"
        }")
    
    echo "切换到系统管理响应: $switch_response"
    
    # 如果切换成功，获取新的token
    if echo "$switch_response" | grep -q '"code":200'; then
        ACCESS_TOKEN=$(echo "$switch_response" | grep -o '"new_token":"[^"]*"' | cut -d'"' -f4)
        print_result "切换到系统管理" "PASS" "成功切换到系统管理模式"
        echo "新令牌: ${ACCESS_TOKEN:0:20}..."
    else
        print_result "切换到系统管理" "FAIL" "无法切换到系统管理模式"
        echo "响应: $switch_response"
        return 1
    fi
    
    # 获取系统管理租户信息
    local response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/tenant/current" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "系统管理租户响应: $response"
    
    # 检查是否包含 is_system: true
    if echo "$response" | grep -q '"is_system":true'; then
        print_result "系统租户标识" "PASS" "正确返回 is_system: true"
    else
        print_result "系统租户标识" "FAIL" "未找到 is_system: true"
    fi
    
    # 检查菜单应该被隐藏（通过API响应验证）
    if echo "$response" | grep -q '"is_system":true'; then
        print_result "菜单隐藏逻辑" "PASS" "系统管理租户应该隐藏WhatsApp管理菜单"
    else
        print_result "菜单隐藏逻辑" "FAIL" "系统管理租户菜单显示逻辑异常"
    fi
    
    echo
}

# 函数：测试其他租户的菜单显示
test_other_tenant_menu() {
    echo -e "${YELLOW}4. 测试其他租户的菜单显示...${NC}"
    
    # 获取所有租户列表
    local tenants_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/tenants" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "租户列表响应: $tenants_response"
    
    # 查找非系统管理租户（选择第一个租户进行测试）
    local non_system_tenant_id=$(echo "$tenants_response" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
    
    if [ -n "$non_system_tenant_id" ]; then
        print_result "找到其他租户" "PASS" "租户ID: $non_system_tenant_id"
        
        # 切换到其他租户
        local switch_response=$(curl --noproxy "*" -s -X POST "$BACKEND_URL/api/tenant/switch" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"tenant_id\": $non_system_tenant_id,
                \"reason\": \"测试租户切换\"
            }")
        
        echo "切换租户响应: $switch_response"
        
        # 获取切换后的租户信息
        local current_tenant_response=$(curl --noproxy "*" -s -X GET "$BACKEND_URL/api/tenant/current" \
            -H "Authorization: Bearer $ACCESS_TOKEN")
        
        echo "当前租户响应: $current_tenant_response"
        
        # 检查是否不包含 is_system 或 is_system 为 false
        if echo "$current_tenant_response" | grep -q '"is_system":false' || ! echo "$current_tenant_response" | grep -q '"is_system"'; then
            print_result "其他租户标识" "PASS" "正确返回 is_system: false 或未定义"
        else
            print_result "其他租户标识" "FAIL" "其他租户不应该有 is_system: true"
        fi
        
        # 检查菜单应该显示
        if echo "$current_tenant_response" | grep -q '"is_system":false' || ! echo "$current_tenant_response" | grep -q '"is_system"'; then
            print_result "菜单显示逻辑" "PASS" "其他租户应该显示WhatsApp管理菜单"
        else
            print_result "菜单显示逻辑" "FAIL" "其他租户菜单隐藏逻辑异常"
        fi
        
    else
        print_result "找到其他租户" "FAIL" "未找到非系统管理租户"
    fi
    
    echo
}

# 函数：测试前端菜单显示逻辑
test_frontend_menu_logic() {
    echo -e "${YELLOW}5. 测试前端菜单显示逻辑...${NC}"
    
    # 检查App.vue文件中的条件渲染逻辑
    if [ -f "frontend/src/App.vue" ]; then
        local app_vue_content=$(cat "frontend/src/App.vue")
        
        # 检查是否包含tenantStore.currentTenant?.is_system逻辑
        if echo "$app_vue_content" | grep -q "tenantStore.currentTenant?.is_system"; then
            print_result "前端条件渲染" "PASS" "App.vue包含正确的条件渲染逻辑"
        else
            print_result "前端条件渲染" "FAIL" "App.vue缺少条件渲染逻辑"
        fi
        
        # 检查是否包含showWhatsAppManagement计算属性
        if echo "$app_vue_content" | grep -q "showWhatsAppManagement"; then
            print_result "WhatsApp菜单控制" "PASS" "App.vue包含WhatsApp菜单控制逻辑"
        else
            print_result "WhatsApp菜单控制" "FAIL" "App.vue缺少WhatsApp菜单控制逻辑"
        fi
        
        # 检查是否包含authStore.ismanager逻辑
        if echo "$app_vue_content" | grep -q "authStore.ismanager"; then
            print_result "管理员权限检查" "PASS" "App.vue包含管理员权限检查逻辑"
        else
            print_result "管理员权限检查" "FAIL" "App.vue缺少管理员权限检查逻辑"
        fi
        
    else
        print_result "前端文件检查" "FAIL" "找不到App.vue文件"
    fi
    
    # 检查auth.ts文件中的ismanager定义
    if [ -f "frontend/src/stores/auth.ts" ]; then
        local auth_ts_content=$(cat "frontend/src/stores/auth.ts")
        
        # 检查是否定义了ismanager计算属性
        if echo "$auth_ts_content" | grep -q "const ismanager.*computed"; then
            print_result "ismanager定义" "PASS" "auth.ts中正确定义了ismanager计算属性"
        else
            print_result "ismanager定义" "FAIL" "auth.ts中缺少ismanager计算属性定义"
        fi
        
        # 检查是否在返回对象中导出了ismanager
        if echo "$auth_ts_content" | grep -q "ismanager,"; then
            print_result "ismanager导出" "PASS" "auth.ts中正确导出了ismanager"
        else
            print_result "ismanager导出" "FAIL" "auth.ts中缺少ismanager导出"
        fi
        
    else
        print_result "auth.ts检查" "FAIL" "找不到auth.ts文件"
    fi
    
    # 检查tenant.ts文件中的租户逻辑
    if [ -f "frontend/src/stores/tenant.ts" ]; then
        local tenant_ts_content=$(cat "frontend/src/stores/tenant.ts")
        
        # 检查是否包含currentTenant相关逻辑
        if echo "$tenant_ts_content" | grep -q "currentTenant"; then
            print_result "租户状态管理" "PASS" "tenant.ts包含租户状态管理逻辑"
        else
            print_result "租户状态管理" "FAIL" "tenant.ts缺少租户状态管理逻辑"
        fi
        
    else
        print_result "tenant.ts检查" "FAIL" "找不到tenant.ts文件"
    fi
    
    echo
}

# 函数：验证API端点
test_api_endpoints() {
    echo -e "${YELLOW}6. 验证相关API端点...${NC}"
    
    # 测试租户相关API
    local endpoints=(
        "GET /api/tenants/current"
        "GET /api/tenants"
        "GET /api/whatsapp/accounts"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local method=$(echo "$endpoint" | cut -d' ' -f1)
        local path=$(echo "$endpoint" | cut -d' ' -f2)
        
        local response=$(curl --noproxy "*" -s -X "$method" "$BACKEND_URL$path" \
            -H "Authorization: Bearer $ACCESS_TOKEN")
        
        if [ $? -eq 0 ]; then
            print_result "$endpoint" "PASS" "API端点可访问"
        else
            print_result "$endpoint" "FAIL" "API端点无法访问"
        fi
    done
    
    echo
}

# 主测试流程
main() {
    echo -e "${BLUE}开始系统管理员WhatsApp管理菜单显示逻辑测试${NC}"
    echo "=================================================="
    echo
    
    # 执行测试步骤
    check_services || exit 1
    get_access_token || exit 1
    test_system_tenant_menu
    test_other_tenant_menu
    test_frontend_menu_logic
    test_api_endpoints
    
    echo -e "${GREEN}=== 测试完成 ===${NC}"
    echo -e "${BLUE}测试总结:${NC}"
    echo "- 验证了系统管理租户的菜单隐藏逻辑"
    echo "- 验证了其他租户的菜单显示逻辑"
    echo "- 检查了前端条件渲染代码"
    echo "- 验证了相关API端点"
}

# 执行主函数
main "$@" 